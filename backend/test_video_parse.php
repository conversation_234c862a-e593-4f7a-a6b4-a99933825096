<?php
// Video parsing test dosyası
header('Content-Type: text/html; charset=utf-8');

// Test XML verisi
$testXml = '<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
<channel>
<item>
<HaberKodu>20250730AW507828</HaberKodu>
<UstKategori>ULUSAL HABER</UstKategori>
<Kategori>POLİTİKA</Kategori>
<Sehir>ANKARA</Sehir>
<SonDakika>Hayır</SonDakika>
<title>MGK\'dan 6 maddelik bildiri: Terörsüz Türkiye vurgusu</title>
<description>
<![CDATA[ <PERSON>i Gü<PERSON>ru<PERSON> (MGK) sonrası yayımlanan bildiride, Türkiye\'nin ayaklarına vurulmak istenen terör prangasının sökülüp atılmasıyla birlikte milletin kardeşliğinin daha da pekişeceği ve milli hedeflere daha hızlı ve istikrarlı adımlarla ilerleneceği ifade edildi. ]]>
</description>
<pubDate>30.07.2025 21:16:40</pubDate>
<SonHaberGuncellenmeTarihi>30.07.2025 21:41</SonHaberGuncellenmeTarihi>
<SonFotografEklenmeTarihi>-</SonFotografEklenmeTarihi>
<videos>
<video>
<path_video VideoKodu="1798636" HaberKodu="20250730AW507828" filesize="6143692" duration="65">https://abonerss.iha.com.tr/download.ashx?type=video&amp;param1=IRWvQROLUVWjYZWTYBa3gBi3gBGLQ5S_M5ODUtaLYZSDYVWXIpivE1iLYpWvM9W7UJSvYVS7U9G3I1aLUVGD</path_video>
<path_poster VideoKodu="1798636" HaberKodu="20250730AW507828">https://abonerss.iha.com.tr/download.ashx?type=video&amp;param1=IRS_UJafUFWnU9S7I5a3EVirgNC7Q5S_UNW7URaLYdS_MFO7IpC_EViDMdWPUJaLQ5SbYZSDU9G3I1aHUVW_</path_poster>
<Aciklama VideoKodu="1798636" HaberKodu="20250730AW507828">
<![CDATA[ MGK\'DAN 6 Maddelik bildiri: Terörsüz Türkiye vurgusu ]]>
</Aciklama>
</video>
</videos>
</item>
</channel>
</rss>';

echo "<h1>Video Parsing Test</h1>";

try {
    // XML'i parse et
    $xml = simplexml_load_string($testXml);
    
    if ($xml === false) {
        throw new Exception("XML parse edilemedi");
    }
    
    echo "<h2>XML Parse Başarılı</h2>";
    
    // Item'ı al
    $item = $xml->channel->item;
    
    echo "<h3>Haber Bilgileri:</h3>";
    echo "<p><strong>Haber Kodu:</strong> " . (string)$item->HaberKodu . "</p>";
    echo "<p><strong>Başlık:</strong> " . (string)$item->title . "</p>";
    
    // Videos bölümünü kontrol et
    if (isset($item->videos) && isset($item->videos->video)) {
        echo "<h3>Video Bilgileri Bulundu:</h3>";
        
        foreach ($item->videos->video as $video) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            
            // path_video elementini kontrol et
            if (isset($video->path_video)) {
                $videoElement = $video->path_video;
                $videoKodu = (string)$videoElement['VideoKodu'];
                $haberKodu = (string)$videoElement['HaberKodu'];
                $filesize = (string)$videoElement['filesize'];
                $duration = (string)$videoElement['duration'];
                $videoUrl = (string)$videoElement;
                
                echo "<h4>Video Detayları:</h4>";
                echo "<p><strong>Video Kodu:</strong> " . $videoKodu . "</p>";
                echo "<p><strong>Haber Kodu:</strong> " . $haberKodu . "</p>";
                echo "<p><strong>Dosya Boyutu:</strong> " . $filesize . " bytes</p>";
                echo "<p><strong>Süre:</strong> " . $duration . " saniye</p>";
                echo "<p><strong>Video URL:</strong> " . $videoUrl . "</p>";
            }
            
            // path_poster elementini kontrol et
            if (isset($video->path_poster)) {
                $posterElement = $video->path_poster;
                $posterUrl = (string)$posterElement;
                
                echo "<p><strong>Poster URL:</strong> " . $posterUrl . "</p>";
            }
            
            // Açıklama elementini kontrol et
            if (isset($video->Aciklama)) {
                $aciklama = (string)$video->Aciklama;
                echo "<p><strong>Açıklama:</strong> " . $aciklama . "</p>";
            }
            
            echo "</div>";
        }
        
        // SQL Insert örneği
        echo "<h3>SQL Insert Örneği:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px;'>";
        
        foreach ($item->videos->video as $video) {
            if (isset($video->path_video)) {
                $videoElement = $video->path_video;
                $videoKodu = (string)$videoElement['VideoKodu'];
                $haberKodu = (string)$videoElement['HaberKodu'];
                $videoUrl = (string)$videoElement;
                $posterUrl = isset($video->path_poster) ? (string)$video->path_poster : '';
                $aciklama = isset($video->Aciklama) ? (string)$video->Aciklama : '';
                $filesize = (string)$videoElement['filesize'];
                $duration = (string)$videoElement['duration'];
                
                echo "INSERT INTO news_videos (haber_kodu, video_kodu, video_url, poster_url, aciklama, filesize, duration) VALUES (\n";
                echo "    '" . addslashes($haberKodu) . "',\n";
                echo "    '" . addslashes($videoKodu) . "',\n";
                echo "    '" . addslashes($videoUrl) . "',\n";
                echo "    '" . addslashes($posterUrl) . "',\n";
                echo "    '" . addslashes($aciklama) . "',\n";
                echo "    '" . addslashes($filesize) . "',\n";
                echo "    '" . addslashes($duration) . "'\n";
                echo ");\n\n";
            }
        }
        
        echo "</pre>";
        
    } else {
        echo "<h3>Video Bilgisi Bulunamadı</h3>";
        echo "<p>videos veya video elementi mevcut değil.</p>";
    }
    
    // XML yapısını göster
    echo "<h3>Tam XML Yapısı:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($testXml);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Hata:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Test Tamamlandı</h3>";
echo "<p>Bu test dosyası video parsing işleminin doğru çalışıp çalışmadığını kontrol eder.</p>";
?>
