<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// OPTIONS request için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'db.php';
require_once 'helpers.php';

// Hata raporlamayı kapat (production için)
error_reporting(0);
ini_set('display_errors', 0);

function sendResponse($success, $data = null, $message = '', $total = null) {
    $response = [
        'success' => $success,
        'data' => $data,
        'message' => $message
    ];
    
    if ($total !== null) {
        $response['total'] = $total;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

function getImageUrl($localPath) {
    if (empty($localPath)) return null;

    // Eğer tam URL ise direkt döndür
    if (strpos($localPath, 'http') === 0) {
        return $localPath;
    }

    // Mutlak path'i relative path'e çevir
    if (strpos($localPath, '/home/<USER>/public_html/') === 0) {
        $localPath = str_replace('/home/<USER>/public_html/', '', $localPath);
    }

    // Local path'i tam URL'e çevir
    $baseUrl = 'https://metaanalizhaber.com/';
    return $baseUrl . ltrim($localPath, '/');
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_news':
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;

            $category = $_GET['category'] ?? $_GET['kategori'] ?? '';
            $city = $_GET['city'] ?? $_GET['sehir'] ?? '';
            $breaking = $_GET['breaking'] ?? '';

            // Sıralama parametreleri
            $sort = $_GET['sort'] ?? 'pub_date';
            $order = strtoupper($_GET['order'] ?? 'DESC');

            // Güvenlik kontrolü
            $allowedSorts = ['pub_date', 'created_at', 'view_count', 'title'];
            $allowedOrders = ['ASC', 'DESC'];

            if (!in_array($sort, $allowedSorts)) $sort = 'pub_date';
            if (!in_array($order, $allowedOrders)) $order = 'DESC';
            
            // WHERE koşulları
            $whereConditions = ["status = 'active'"];
            $params = [];
            
            if (!empty($category)) {
                $whereConditions[] = "kategori = ?";
                $params[] = $category;
            }
            
            if (!empty($city)) {
                $whereConditions[] = "sehir = ?";
                $params[] = $city;
            }
            
            if ($breaking === 'true') {
                $whereConditions[] = "son_dakika = 'Evet'";
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // Toplam sayı
            $countSql = "SELECT COUNT(*) FROM news WHERE $whereClause";
            $countStmt = $pdo->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetchColumn();
            
            // Haberler
            $sql = "SELECT 
                        n.id,
                        n.haber_kodu,
                        n.title,
                        n.slug,
                        n.description,
                        n.kategori,
                        n.sehir,
                        n.son_dakika,
                        n.pub_date,
                        n.has_images,
                        n.has_videos,
                        n.image_count,
                        n.video_count,
                        n.view_count,
                        n.created_at,
                        ni.local_path as main_image
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id AND ni.resim_kodu = (
                        SELECT resim_kodu FROM news_images WHERE news_id = n.id ORDER BY id ASC LIMIT 1
                    )
                    WHERE $whereClause
                    ORDER BY n.$sort $order, n.created_at DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([...$params, $limit, $offset]);
            $news = $stmt->fetchAll();
            
            // Görsel URL'lerini düzenle
            foreach ($news as &$item) {
                $item['main_image'] = getImageUrl($item['main_image']);
                $item['pub_date'] = date('Y-m-d H:i:s', strtotime($item['pub_date']));
                $item['created_at'] = date('Y-m-d H:i:s', strtotime($item['created_at']));
            }
            unset($item); // Reference'ı temizle
            
            sendResponse(true, $news, '', $total);
            break;
            
        case 'get_news_by_slug':
            $slug = $_GET['slug'] ?? '';
            if (empty($slug)) {
                sendResponse(false, null, 'Slug parametresi gerekli');
            }

            $sql = "SELECT
                        n.*,
                        GROUP_CONCAT(DISTINCT ni.local_path ORDER BY ni.resim_kodu) as images
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id
                    WHERE n.slug = ? AND n.status = 'active'
                    GROUP BY n.id";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$slug]);
            $news = $stmt->fetch();

            if (!$news) {
                sendResponse(false, null, 'Haber bulunamadı');
            }

            // Görselleri düzenle
            if ($news['images']) {
                $images = explode(',', $news['images']);
                $news['images'] = array_map('getImageUrl', $images);
            } else {
                $news['images'] = [];
            }

            // Main image URL'ini düzenle
            $news['main_image'] = getImageUrl($news['main_image']);

            // Videoları getir (has_videos = 1 ise)
            $news['videos'] = [];
            if ($news['has_videos']) {
                $videoSql = "SELECT video_kodu, video_url, poster_url, description
                            FROM news_videos
                            WHERE news_id = ?
                            ORDER BY id ASC";
                $videoStmt = $pdo->prepare($videoSql);
                $videoStmt->execute([$news['id']]);
                $videos = $videoStmt->fetchAll();

                foreach ($videos as &$video) {
                    // Video URL'lerini düzenle
                    if (!empty($video['video_url'])) {
                        // Eğer tam URL değilse base URL ekle
                        if (strpos($video['video_url'], 'http') !== 0) {
                            $video['video_url'] = 'https://metaanalizhaber.com/' . ltrim($video['video_url'], '/');
                        }
                    }
                    if (!empty($video['poster_url'])) {
                        // Eğer tam URL değilse base URL ekle
                        if (strpos($video['poster_url'], 'http') !== 0) {
                            $video['poster_url'] = 'https://metaanalizhaber.com/' . ltrim($video['poster_url'], '/');
                        }
                    }
                }
                unset($video);

                $news['videos'] = $videos;
            }

            // View count artır
            $updateSql = "UPDATE news SET view_count = view_count + 1 WHERE id = ?";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute([$news['id']]);

            sendResponse(true, $news);
            break;
            
        case 'get_news_detail':
            $haberKodu = $_GET['haber_kodu'] ?? '';
            if (empty($haberKodu)) {
                sendResponse(false, null, 'Haber kodu parametresi gerekli');
            }

            $sql = "SELECT
                        n.*,
                        GROUP_CONCAT(DISTINCT ni.local_path ORDER BY ni.resim_kodu) as images
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id
                    WHERE n.haber_kodu = ? AND n.status = 'active'
                    GROUP BY n.id";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$haberKodu]);
            $news = $stmt->fetch();

            if (!$news) {
                sendResponse(false, null, 'Haber bulunamadı');
            }

            // Görselleri düzenle
            if ($news['images']) {
                $images = explode(',', $news['images']);
                $news['images'] = array_map('getImageUrl', $images);
            } else {
                $news['images'] = [];
            }

            // Main image URL'ini düzenle
            $news['main_image'] = getImageUrl($news['main_image']);

            // Videoları getir (has_videos = 1 ise)
            $news['videos'] = [];
            if ($news['has_videos']) {
                $videoSql = "SELECT video_kodu, video_url, poster_url, description
                            FROM news_videos
                            WHERE news_id = ?
                            ORDER BY id ASC";
                $videoStmt = $pdo->prepare($videoSql);
                $videoStmt->execute([$news['id']]);
                $videos = $videoStmt->fetchAll();

                foreach ($videos as &$video) {
                    // Video URL'lerini düzenle
                    if (!empty($video['video_url'])) {
                        // Eğer tam URL değilse base URL ekle
                        if (strpos($video['video_url'], 'http') !== 0) {
                            $video['video_url'] = 'https://metaanalizhaber.com/' . ltrim($video['video_url'], '/');
                        }
                    }
                    if (!empty($video['poster_url'])) {
                        // Eğer tam URL değilse base URL ekle
                        if (strpos($video['poster_url'], 'http') !== 0) {
                            $video['poster_url'] = 'https://metaanalizhaber.com/' . ltrim($video['poster_url'], '/');
                        }
                    }
                }
                unset($video);

                $news['videos'] = $videos;
            }

            // View count artır
            $updateSql = "UPDATE news SET view_count = view_count + 1 WHERE id = ?";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute([$news['id']]);

            sendResponse(true, $news);
            break;
            
        case 'get_categories':
            $sql = "SELECT DISTINCT kategori as name, COUNT(*) as count 
                    FROM news 
                    WHERE status = 'active' AND kategori != '' 
                    GROUP BY kategori 
                    ORDER BY count DESC";
            $stmt = $pdo->query($sql);
            $categories = $stmt->fetchAll();
            
            sendResponse(true, $categories);
            break;
            
        case 'search_news':
            $query = $_GET['q'] ?? '';
            if (empty($query)) {
                sendResponse(false, [], 'Arama sorgusu gerekli');
            }
            
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $searchTerm = "%$query%";
            
            // Toplam sayı
            $countSql = "SELECT COUNT(*) FROM news 
                        WHERE (title LIKE ? OR description LIKE ?) 
                        AND status = 'active'";
            $countStmt = $pdo->prepare($countSql);
            $countStmt->execute([$searchTerm, $searchTerm]);
            $total = $countStmt->fetchColumn();
            
            // Arama sonuçları
            $sql = "SELECT 
                        n.id,
                        n.haber_kodu,
                        n.title,
                        n.slug,
                        n.description,
                        n.kategori,
                        n.sehir,
                        n.son_dakika,
                        n.pub_date,
                        n.has_images,
                        n.image_count,
                        n.view_count,
                        n.created_at,
                        ni.local_path as main_image
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id AND ni.resim_kodu = (
                        SELECT resim_kodu FROM news_images WHERE news_id = n.id ORDER BY id ASC LIMIT 1
                    )
                    WHERE (n.title LIKE ? OR n.description LIKE ?)
                    AND n.status = 'active'
                    ORDER BY n.pub_date DESC, n.created_at DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$searchTerm, $searchTerm, $limit, $offset]);
            $news = $stmt->fetchAll();
            
            // Görsel URL'lerini düzenle
            foreach ($news as &$item) {
                $item['main_image'] = getImageUrl($item['main_image']);
            }
            
            sendResponse(true, $news, '', $total);
            break;
            
        case 'video-news':
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            // Video haberleri (has_videos = 1 olanlar)
            $countSql = "SELECT COUNT(*) FROM news WHERE has_videos = 1 AND status = 'active'";
            $countStmt = $pdo->query($countSql);
            $total = $countStmt->fetchColumn();
            
            $sql = "SELECT 
                        n.id,
                        n.haber_kodu,
                        n.title,
                        n.slug,
                        n.description,
                        n.kategori,
                        n.sehir,
                        n.pub_date,
                        n.video_count,
                        n.view_count,
                        n.created_at,
                        ni.local_path as main_image
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id AND ni.resim_kodu = (
                        SELECT resim_kodu FROM news_images WHERE news_id = n.id ORDER BY id ASC LIMIT 1
                    )
                    WHERE n.has_videos = 1 AND n.status = 'active'
                    ORDER BY n.pub_date DESC, n.created_at DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            $news = $stmt->fetchAll();
            
            // Görsel URL'lerini düzenle
            foreach ($news as &$item) {
                $item['main_image'] = getImageUrl($item['main_image']);
            }
            
            sendResponse(true, $news, '', $total);
            break;

        case 'get_cities':
            // Tüm benzersiz şehirleri getir (DIŞ HABER kategorisi hariç)
            $sql = "SELECT DISTINCT sehir FROM news WHERE sehir IS NOT NULL AND sehir != '' AND (ust_kategori IS NULL OR ust_kategori != 'DIŞ HABER') ORDER BY sehir ASC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            $cities = $stmt->fetchAll(PDO::FETCH_COLUMN);

            sendResponse(true, $cities, 'Şehirler başarıyla getirildi');
            break;

        case 'get_city_stats':
            // Her şehirden kaç haber olduğunu göster (debug için)
            $sql = "SELECT sehir, COUNT(*) as count FROM news WHERE sehir IS NOT NULL AND sehir != '' AND status = 'active' GROUP BY sehir ORDER BY count DESC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            $cityStats = $stmt->fetchAll();

            sendResponse(true, $cityStats, 'Şehir istatistikleri');
            break;





        case 'debug_ust_kategori':
            // Hangi ust_kategori değerleri var kontrol et
            $sql = "SELECT DISTINCT ust_kategori, COUNT(*) as count
                    FROM news
                    WHERE status = 'active' AND ust_kategori IS NOT NULL AND ust_kategori != ''
                    GROUP BY ust_kategori
                    ORDER BY count DESC";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            $ustKategoriler = $stmt->fetchAll();

            sendResponse(true, $ustKategoriler, 'Üst kategoriler');
            break;

        case 'create_test_world_news':
            // Test için bazı haberleri DIŞ HABER olarak güncelle
            $sql = "UPDATE news
                    SET ust_kategori = 'DIŞ HABER'
                    WHERE status = 'active'
                    AND (kategori LIKE '%DÜNYA%' OR kategori LIKE '%ULUSLARARASI%' OR kategori LIKE '%DIŞ%')
                    LIMIT 10";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute();
            $affectedRows = $stmt->rowCount();

            sendResponse(true, ['affected_rows' => $affectedRows], 'Test dünya haberleri oluşturuldu');
            break;

        case 'get_currency_rates':
            // TCMB XML API'sinden gerçek döviz kurlarını çek
            try {
                $xml_url = 'https://www.tcmb.gov.tr/kurlar/today.xml';

                // cURL kullanarak XML'i çek
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $xml_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_USERAGENT, 'MetaAnaliz Haber Bot');
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                $xml_content = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($xml_content === false || $http_code !== 200) {
                    throw new Exception('TCMB API\'sine erişilemedi');
                }

                $xml = simplexml_load_string($xml_content);
                if ($xml === false) {
                    throw new Exception('XML verisi işlenemedi');
                }

                $currencies = [];
                $currencyMap = [
                    'USD' => ['name' => 'Dolar', 'symbol' => '$'],
                    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
                    'GBP' => ['name' => 'Sterlin', 'symbol' => '£'],
                    'CHF' => ['name' => 'İsviçre Frangı', 'symbol' => 'CHF'],
                    'CAD' => ['name' => 'Kanada Doları', 'symbol' => 'C$'],
                    'JPY' => ['name' => 'Japon Yeni', 'symbol' => '¥'],
                    'AUD' => ['name' => 'Avustralya Doları', 'symbol' => 'A$'],
                    'SEK' => ['name' => 'İsveç Kronu', 'symbol' => 'kr']
                ];

                foreach ($xml->Currency as $currency) {
                    $code = (string)$currency['CurrencyCode'];

                    if (isset($currencyMap[$code])) {
                        $buyRate = (float)$currency->BanknoteBuying;
                        $sellRate = (float)$currency->BanknoteSelling;

                        if ($buyRate > 0 && $sellRate > 0) {
                            $change = (($sellRate - $buyRate) / $buyRate) * 100;

                            $currencies[] = [
                                'code' => $code,
                                'name' => $currencyMap[$code]['name'],
                                'symbol' => $currencyMap[$code]['symbol'],
                                'rate' => number_format($sellRate, 2),
                                'change' => ($change >= 0 ? '+' : '') . number_format($change, 2) . '%',
                                'isPositive' => $change >= 0
                            ];
                        }
                    }
                }

                if (empty($currencies)) {
                    throw new Exception('Döviz verileri bulunamadı');
                }

                sendResponse(true, $currencies, 'TCMB\'den döviz kurları başarıyla getirildi');

            } catch (Exception $e) {
                // Hata durumunda varsayılan değerler döndür
                $fallbackCurrencies = [
                    [
                        'code' => 'USD',
                        'name' => 'Dolar',
                        'symbol' => '$',
                        'rate' => '34.25',
                        'change' => '+0.15%',
                        'isPositive' => true
                    ],
                    [
                        'code' => 'EUR',
                        'name' => 'Euro',
                        'symbol' => '€',
                        'rate' => '37.18',
                        'change' => '-0.08%',
                        'isPositive' => false
                    ],
                    [
                        'code' => 'GBP',
                        'name' => 'Sterlin',
                        'symbol' => '£',
                        'rate' => '43.52',
                        'change' => '+0.22%',
                        'isPositive' => true
                    ]
                ];

                sendResponse(true, $fallbackCurrencies, 'Varsayılan döviz kurları (TCMB API hatası: ' . $e->getMessage() . ')');
            }
            break;

        case 'get_world_news':
            // Dünya haberlerini çek
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(20, max(1, intval($_GET['limit'] ?? 8)));
            $offset = ($page - 1) * $limit;

            $sql = "SELECT n.id, n.haber_kodu, n.slug, n.title, n.description, n.pub_date, n.kategori, n.ust_kategori, n.view_count, n.sehir, n.son_dakika,
                           COALESCE(ni.original_url, ni.local_path) as image_url,
                           DATE_FORMAT(n.pub_date, '%d.%m.%Y %H:%i') as formatted_date
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id
                    WHERE n.status = 'active' AND n.ust_kategori = 'DIŞ HABER'
                    GROUP BY n.id
                    ORDER BY n.pub_date DESC
                    LIMIT ? OFFSET ?";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            $worldNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($worldNews as &$news) {
                $news['main_image'] = getImageUrl($news['image_url']);
            }
            unset($news);

            sendResponse(true, $worldNews, 'Dünya haberleri başarıyla getirildi (Toplam: ' . count($worldNews) . ')');
            break;

        case 'get_video_news':
            // Video haberlerini çek
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(20, max(1, intval($_GET['limit'] ?? 8)));
            $offset = ($page - 1) * $limit;

            $sql = "SELECT n.id, n.haber_kodu, n.slug, n.title, n.description, n.pub_date, n.kategori, n.ust_kategori, n.view_count, n.sehir, n.has_videos, n.son_dakika,
                           COALESCE(ni.original_url, ni.local_path) as image_url,
                           DATE_FORMAT(n.pub_date, '%d.%m.%Y %H:%i') as formatted_date
                    FROM news n
                    LEFT JOIN news_images ni ON n.id = ni.news_id
                    WHERE n.status = 'active' AND n.has_videos = 1
                    GROUP BY n.id
                    ORDER BY n.pub_date DESC
                    LIMIT ? OFFSET ?";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            $videoNews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($videoNews as &$news) {
                $news['main_image'] = getImageUrl($news['image_url']);
            }
            unset($news);

            sendResponse(true, $videoNews, 'Video haberler başarıyla getirildi (Toplam: ' . count($videoNews) . ')');
            break;

        default:
            sendResponse(false, null, 'Geçersiz action parametresi');
    }
    
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    sendResponse(false, null, 'Sunucu hatası oluştu');
}
?>
