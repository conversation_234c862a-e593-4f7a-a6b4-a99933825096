<?php
require 'db.php';

header('Content-Type: text/plain; charset=utf-8');

echo "=== ANASAYFA VERİ KONTROLÜ ===\n\n";

// 1. Toplam haber sayısı
$stmt = $pdo->query("SELECT COUNT(*) FROM news WHERE status = 'active'");
$totalNews = $stmt->fetchColumn();
echo "1. Toplam aktif haber sayısı: $totalNews\n\n";

// 2. Görseli olan haber sayısı
$stmt = $pdo->query("SELECT COUNT(*) FROM news WHERE status = 'active' AND has_images = 1");
$newsWithImages = $stmt->fetchColumn();
echo "2. Görseli olan haber sayısı: $newsWithImages\n\n";

// 3. Son 10 haberin görsel durumu
echo "3. Son 10 haberin görsel durumu:\n";
$stmt = $pdo->query("
    SELECT 
        n.haber_kodu,
        n.title,
        n.has_images,
        n.created_at,
        ni.local_path as main_image,
        COUNT(ni2.id) as image_count
    FROM news n
    LEFT JOIN news_images ni ON n.id = ni.news_id AND ni.resim_kodu = (
        SELECT resim_kodu FROM news_images WHERE news_id = n.id ORDER BY id ASC LIMIT 1
    )
    LEFT JOIN news_images ni2 ON n.id = ni2.news_id
    WHERE n.status = 'active'
    GROUP BY n.id
    ORDER BY n.created_at DESC
    LIMIT 10
");

$news = $stmt->fetchAll();
foreach ($news as $item) {
    $imageStatus = $item['has_images'] ? '✅' : '❌';
    $mainImage = $item['main_image'] ? '✅ ' . basename($item['main_image']) : '❌ YOK';
    echo "   {$item['haber_kodu']} | {$imageStatus} | Resim: {$mainImage} | Toplam: {$item['image_count']}\n";
    echo "   Başlık: " . substr($item['title'], 0, 50) . "...\n";
    echo "   Tarih: {$item['created_at']}\n\n";
}

// 4. news_images tablosu durumu
$stmt = $pdo->query("SELECT COUNT(*) FROM news_images WHERE download_status = 'downloaded'");
$downloadedImages = $stmt->fetchColumn();
echo "4. İndirilmiş resim sayısı: $downloadedImages\n\n";

// 5. Son eklenen resimler
echo "5. Son eklenen 5 resim:\n";
$stmt = $pdo->query("
    SELECT 
        ni.haber_kodu,
        ni.resim_kodu,
        ni.local_path,
        ni.download_status,
        ni.created_at
    FROM news_images ni
    ORDER BY ni.created_at DESC
    LIMIT 5
");

$images = $stmt->fetchAll();
foreach ($images as $img) {
    $status = $img['download_status'] == 'downloaded' ? '✅' : '❌';
    echo "   {$img['haber_kodu']} | {$status} | {$img['resim_kodu']} | {$img['created_at']}\n";
    echo "   Path: {$img['local_path']}\n\n";
}

echo "=== TEST TAMAMLANDI ===\n";
?>
