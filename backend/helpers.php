<?php
function slugify($text) {
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    $text = preg_replace('~[^-\w]+~', '', $text);
    $text = trim($text, '-');
    $text = preg_replace('~-+~', '-', $text);
    return strtolower($text);
}

function downloadAndConvertImage($url, $savePath) {
    $imgData = @file_get_contents($url);
    if ($imgData === false) return false;

    // Hedef klasörün var olduğundan emin ol
    $saveDir = dirname($savePath);
    if (!is_dir($saveDir)) {
        if (!@mkdir($saveDir, 0755, true)) {
            return false; // Klasör oluşturulamadı
        }
    }

    // Yazma izni kontrolü
    if (!is_writable($saveDir)) {
        return false; // Yazma izni yok
    }

    $tmpFile = tempnam(sys_get_temp_dir(), 'img');
    file_put_contents($tmpFile, $imgData);

    $image = @imagecreatefromstring(file_get_contents($tmpFile));
    if (!$image) {
        unlink($tmpFile);
        return false;
    }

    // WebP kaydetmeyi dene
    $result = @imagewebp($image, $savePath, 80);
    imagedestroy($image);
    unlink($tmpFile);

    // Dosya oluşturulduktan sonra izinleri ayarla
    if ($result && file_exists($savePath)) {
        @chmod($savePath, 0644);
    }

    return $result;
}
