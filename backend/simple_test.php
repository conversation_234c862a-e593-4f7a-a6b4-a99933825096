<?php
header('Content-Type: text/plain; charset=utf-8');

// RSS URL
$rss_url = 'https://abonerss.iha.com.tr/xml/standartrss?UserCode=10002&UstKategori=0&UserName=metaanaliz&UserPassword=iha1091&Kategori=0&Sehir=0&wp=0&tagp=1&tip=1';

// RSS'i çek
$rss_content = @file_get_contents($rss_url);

if (!$rss_content) {
    die("RSS içeriği alınamadı!");
}

// XML parse et
$xml = simplexml_load_string($rss_content);

if (!$xml) {
    die("XML parse edilemedi!");
}

// Son 10 haberi al
$items = array_slice($xml->channel->item->xpath('//item'), 0, 10);

foreach ($items as $index => $item) {
    $haber_kodu = (string)$item->HaberKodu;

    // Görseller
    $has_images = 0;
    $image_count = 0;

    if (isset($item->images) && isset($item->images->image)) {
        $has_images = 1;
        $image_count = count($item->images->image);
    }

    // Videolar
    $has_videos = 0;
    $video_count = 0;

    if (isset($item->videos) && isset($item->videos->video)) {
        $has_videos = 1;
        $video_count = count($item->videos->video);
    }

    echo $haber_kodu . ", " . $has_images . ", " . $has_videos . ", " . $image_count . ", " . $video_count . "\n";
}
