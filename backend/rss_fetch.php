<?php

require 'db.php';
require 'helpers.php';

header('Content-Type: text/plain; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

$startTime = microtime(true);

$rssUrl = 'https://abonerss.iha.com.tr/xml/standartrss?UserCode=10002&UstKategori=0&UserName=metaanaliz&UserPassword=iha1091&Kategori=0&Sehir=0&wp=0&tagp=1&tip=1';

$inserted = $updated = 0;

try {
    $rss_content = @file_get_contents($rssUrl);
    $xml = simplexml_load_string($rss_content);
    if (!$xml) throw new Exception("RSS okunamadı.");

    // Tüm haberleri al ve tarihe göre sırala
    $allItems = [];
    foreach ($xml->channel->item as $item) {
        $allItems[] = $item;
    }

    // Tarihe göre sırala (en yeni önce)
    usort($allItems, function($a, $b) {
        return strtotime($b->pubDate) - strtotime($a->pubDate);
    });

    // Son 10 haberi al
    $items = array_slice($allItems, 0, 10);



    foreach ($items as $item) {
        $haberKodu = (string)$item->HaberKodu;
        $title = trim((string)$item->title);
        $description = trim((string)$item->description);
        $pubDate = date('Y-m-d H:i:s', strtotime($item->pubDate));
        $ustKategori = (string)$item->UstKategori;
        $kategori = (string)$item->Kategori;
        $sehir = (string)$item->Sehir;
        $sonDakika = ((string)$item->SonDakika == 'Evet') ? 'Evet' : 'Hayır';
        $slug = slugify($title);

        // Sadece son 24 saatteki haberleri işle
        $pubDateTime = strtotime($item->pubDate);
        $oneDayAgo = time() - (24 * 60 * 60);

        if ($pubDateTime < $oneDayAgo) {
            continue;
        }

        $stmt = $pdo->prepare("SELECT id FROM news WHERE haber_kodu = ?");
        $stmt->execute([$haberKodu]);
        $newsId = $stmt->fetchColumn();
        
        if ($newsId) {
            $pdo->prepare("UPDATE news SET title=?, description=?, pub_date=?, ust_kategori=?, kategori=?, sehir=?, son_dakika=?, slug=?, updated_at=NOW() WHERE haber_kodu=?")
                ->execute([$title, $description, $pubDate, $ustKategori, $kategori, $sehir, $sonDakika, $slug, $haberKodu]);
            $updated++;
        } else {
            $pdo->prepare("INSERT INTO news (haber_kodu, title, description, pub_date, ust_kategori, kategori, sehir, son_dakika, slug) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)")
                ->execute([$haberKodu, $title, $description, $pubDate, $ustKategori, $kategori, $sehir, $sonDakika, $slug]);
            $newsId = $pdo->lastInsertId();
            $inserted++;
        }

        // Görseller
        if (isset($item->images) && isset($item->images->image)) {
            $images = $item->images->image;
            $hasImageUpdated = false;
            foreach ($images as $image) {
                $resimKodu = (string)$image['ResimKodu'];
                $imageUrl = (string)$image;
                $savePath = "/home/<USER>/public_html/uploads/images/{$resimKodu}.webp";

                if (downloadAndConvertImage($imageUrl, $savePath)) {
                    $pdo->prepare("INSERT IGNORE INTO news_images (news_id, haber_kodu, resim_kodu, original_url, local_path, download_status) VALUES (?, ?, ?, ?, ?, 'downloaded')")
                        ->execute([$newsId, $haberKodu, $resimKodu, $imageUrl, $savePath]);

                    if (!$hasImageUpdated) {
                        $pdo->prepare("UPDATE news SET has_images = 1 WHERE id = ?")->execute([$newsId]);
                        $hasImageUpdated = true;
                    }
                }
            }
        }

        // Videolar
        if (isset($item->videos) && isset($item->videos->video)) {
            echo "🎬 Video bölümü bulundu: $haberKodu\n";
            $videos = $item->videos->video;
            $hasVideoUpdated = false;
            foreach ($videos as $video) {
                // path_video elementinden bilgileri al
                if (isset($video->path_video)) {
                    $videoElement = $video->path_video;
                    $videoKodu = (string)$videoElement['VideoKodu'];
                    $haberKodu = (string)$videoElement['HaberKodu'];
                    $filesize = (string)$videoElement['filesize'];
                    $duration = (string)$videoElement['duration'];
                    $videoUrl = (string)$videoElement;

                    // path_poster URL'sini al
                    $posterUrl = isset($video->path_poster) ? (string)$video->path_poster : '';

                    // Açıklama al
                    $description = isset($video->Aciklama) ? (string)$video->Aciklama : '';

                    $pdo->prepare("INSERT IGNORE INTO news_videos (news_id, haber_kodu, video_kodu, video_url, poster_url, description, file_size, duration, download_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'downloaded')")
                        ->execute([$newsId, $haberKodu, $videoKodu, $videoUrl, $posterUrl, $description, $filesize, $duration]);

                    if (!$hasVideoUpdated) {
                        $pdo->prepare("UPDATE news SET has_videos = 1 WHERE id = ?")->execute([$newsId]);
                        $hasVideoUpdated = true;
                    }

                    echo "📹 Video kaydedildi: $videoKodu (Süre: {$duration}s, Boyut: {$filesize} bytes)\n";
                } else {
                    echo "⚠️ Video path_video elementi bulunamadı\n";
                }
            }
        } else {
            echo "ℹ️ Video bölümü yok: $haberKodu\n";
        }
    }

    echo "✅ $inserted haber eklendi, $updated haber güncellendi.\n";

} catch (Exception $e) {
    echo "⚠️ Hata oluştu: " . $e->getMessage() . PHP_EOL;
}

$endTime = microtime(true);
$executionTime = round($endTime - $startTime, 2);
echo "⏱️ Toplam çalışma süresi: {$executionTime} saniye\n";