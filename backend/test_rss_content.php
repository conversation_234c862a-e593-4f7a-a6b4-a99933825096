<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== RSS İÇERİK KONTROLÜ ===\n\n";

// RSS URL'i
$rssUrl = 'https://abonerss.iha.com.tr/xml/standartrss?UserCode=10002&UstKategori=0&UserName=metaanaliz&UserPassword=iha1091&Kategori=0&Sehir=0&wp=0&tagp=1&tip=1';

echo "RSS URL: $rssUrl\n\n";

// RSS'i çek
$rssContent = @file_get_contents($rssUrl);
if (!$rssContent) {
    echo "❌ RSS çekilemedi!\n";
    exit;
}

// XML parse et
$xml = @simplexml_load_string($rssContent);
if (!$xml) {
    echo "❌ XML parse edilemedi!\n";
    exit;
}

$items = $xml->channel->item;
echo "📊 RSS'de toplam haber sayısı: " . count($items) . "\n\n";

echo "🔍 İlk 15 haberin detayları:\n";
echo str_repeat("=", 80) . "\n";

foreach (array_slice($items, 0, 15) as $index => $item) {
    $haberKodu = (string)$item->HaberKodu;
    $title = trim((string)$item->title);
    $pubDate = date('Y-m-d H:i:s', strtotime($item->pubDate));
    $kategori = (string)$item->Kategori;
    $sonDakika = ((string)$item->SonDakika == 'Evet') ? '🔴 EVET' : '⚪ Hayır';
    
    echo ($index + 1) . ". $haberKodu\n";
    echo "   Başlık: " . substr($title, 0, 60) . "...\n";
    echo "   Tarih: $pubDate\n";
    echo "   Kategori: $kategori\n";
    echo "   Son Dakika: $sonDakika\n";
    echo "   " . str_repeat("-", 70) . "\n";
}

echo "\n=== VERİTABANI KARŞILAŞTIRMASI ===\n";

require 'db.php';

// İlk 10 haber kodunu kontrol et
$firstTenCodes = array_slice(array_map(function($item) {
    return (string)$item->HaberKodu;
}, $items), 0, 10);

echo "RSS'den ilk 10 haber kodu:\n";
foreach ($firstTenCodes as $i => $code) {
    echo ($i + 1) . ". $code\n";
}

echo "\nVeritabanında bu kodlar var mı?\n";
foreach ($firstTenCodes as $i => $code) {
    $stmt = $pdo->prepare("SELECT id, title, created_at FROM news WHERE haber_kodu = ?");
    $stmt->execute([$code]);
    $result = $stmt->fetch();
    
    if ($result) {
        echo ($i + 1) . ". $code ✅ VAR (ID: {$result['id']}, Oluşturma: {$result['created_at']})\n";
    } else {
        echo ($i + 1) . ". $code ❌ YOK\n";
    }
}

echo "\n=== SON EKLENENLERİ KONTROL ET ===\n";
$stmt = $pdo->query("
    SELECT haber_kodu, title, created_at, updated_at 
    FROM news 
    ORDER BY created_at DESC 
    LIMIT 10
");
$latestNews = $stmt->fetchAll();

echo "Veritabanındaki son 10 haber:\n";
foreach ($latestNews as $i => $news) {
    echo ($i + 1) . ". {$news['haber_kodu']} - {$news['created_at']}\n";
    echo "   " . substr($news['title'], 0, 50) . "...\n";
}

echo "\n=== TEST TAMAMLANDI ===\n";
?>
