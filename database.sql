-- <PERSON><PERSON> Veritabanı
-- IHA RSS entegrasyonu için tasarlan<PERSON> tablolar

-- <PERSON>gor<PERSON> tablosu
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_code INT UNIQUE NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS cities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    city_code INT UNIQUE NOT NULL,
    city_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Ana haberler tablosu
CREATE TABLE IF NOT EXISTS news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    haber_kodu VARCHAR(50) UNIQUE NOT NULL,
    ust_kategori VARCHAR(50) NOT NULL,
    kategori VARCHAR(100) NOT NULL,
    sehir VARCHAR(50) NOT NULL,
    son_dakika ENUM('Evet', 'Hayır') DEFAULT 'Hayır',
    title TEXT NOT NULL,
    description LONGTEXT NOT NULL,
    pub_date DATETIME NOT NULL,
    son_haber_guncelleme_tarihi DATETIME NULL,
    son_fotograf_ekleme_tarihi DATETIME NULL,
    has_images BOOLEAN DEFAULT FALSE,
    has_videos BOOLEAN DEFAULT FALSE,
    image_count INT DEFAULT 0,
    video_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    slug VARCHAR(255) NULL,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_haber_kodu (haber_kodu),
    INDEX idx_pub_date (pub_date),
    INDEX idx_ust_kategori (ust_kategori),
    INDEX idx_kategori (kategori),
    INDEX idx_sehir (sehir),
    INDEX idx_son_dakika (son_dakika),
    INDEX idx_status (status),
    INDEX idx_slug (slug)
);

-- Haber görselleri tablosu
CREATE TABLE IF NOT EXISTS news_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    news_id INT NOT NULL,
    haber_kodu VARCHAR(50) NOT NULL,
    resim_kodu VARCHAR(50) NOT NULL,
    original_url TEXT NOT NULL,
    local_path VARCHAR(500) NULL,
    file_size INT NULL,
    description TEXT NULL,
    download_status ENUM('pending', 'downloaded', 'failed') DEFAULT 'pending',
    download_attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE,
    INDEX idx_haber_kodu (haber_kodu),
    INDEX idx_resim_kodu (resim_kodu),
    INDEX idx_download_status (download_status),
    UNIQUE KEY unique_resim (haber_kodu, resim_kodu)
);

-- Haber videoları tablosu
CREATE TABLE IF NOT EXISTS news_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    news_id INT NOT NULL,
    haber_kodu VARCHAR(50) NOT NULL,
    video_kodu VARCHAR(50) NOT NULL,
    video_url TEXT NOT NULL,
    poster_url TEXT NULL,
    local_video_path VARCHAR(500) NULL,
    local_poster_path VARCHAR(500) NULL,
    file_size INT NULL,
    duration INT NULL,
    description TEXT NULL,
    download_status ENUM('pending', 'downloaded', 'failed') DEFAULT 'pending',
    download_attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE,
    INDEX idx_haber_kodu (haber_kodu),
    INDEX idx_video_kodu (video_kodu),
    INDEX idx_download_status (download_status),
    UNIQUE KEY unique_video (haber_kodu, video_kodu)
);

-- RSS çekme logları tablosu
CREATE TABLE IF NOT EXISTS rss_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fetch_type ENUM('news', 'images', 'videos') NOT NULL,
    status ENUM('success', 'error', 'partial') NOT NULL,
    news_count INT DEFAULT 0,
    new_news_count INT DEFAULT 0,
    updated_news_count INT DEFAULT 0,
    error_message TEXT NULL,
    execution_time DECIMAL(10,3) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_fetch_type (fetch_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Kategoriler için başlangıç verileri
INSERT IGNORE INTO categories (category_code, category_name) VALUES
(2, 'Magazin'),
(3, 'Spor'),
(4, 'Politika'),
(5, 'Asayiş'),
(8, 'Genel'),
(9, 'Ekonomi'),
(11, 'Haberde İnsan'),
(12, 'Sağlık'),
(13, 'Eğitim'),
(14, 'Bilim Ve Teknoloji'),
(15, 'Kültür Sanat'),
(16, 'Çevre');

-- Şehirler için başlangıç verileri (IHA dokümantasyonundan)
INSERT IGNORE INTO cities (city_code, city_name) VALUES
(0, 'Hepsi'),
(1, 'Adana'),
(40, 'Adıyaman'),
(82, 'Afyonkarahisar'),
(73, 'Ağrı'),
(55, 'Aksaray'),
(64, 'Amasya'),
(6, 'Ankara'),
(7, 'Antalya'),
(58, 'Ardahan'),
(71, 'Artvin'),
(12, 'Aydın'),
(16, 'Balıkesir'),
(79, 'Bartın'),
(33, 'Batman'),
(36, 'Bayburt'),
(21, 'Bilecik'),
(76, 'Bingöl'),
(74, 'Bitlis'),
(61, 'Bolu'),
(10, 'Burdur'),
(15, 'Bursa'),
(17, 'Çanakkale'),
(24, 'Çankırı'),
(23, 'Çorum'),
(9, 'Denizli'),
(27, 'Diyarbakır'),
(62, 'Düzce'),
(44, 'Edirne'),
(28, 'Elazığ'),
(35, 'Erzincan'),
(34, 'Erzurum'),
(18, 'Eskişehir'),
(38, 'Gaziantep'),
(69, 'Giresun'),
(37, 'Gümüşhane'),
(75, 'Hakkari'),
(2, 'Hatay'),
(59, 'Iğdır'),
(11, 'Isparta'),
(43, 'İstanbul'),
(46, 'İzmir'),
(3, 'Kahramanmaraş'),
(80, 'Karabük'),
(56, 'Karaman'),
(57, 'Kars'),
(26, 'Kastamonu'),
(48, 'Kayseri'),
(25, 'Kırıkkale'),
(81, 'Kırklareli'),
(52, 'Kırşehir'),
(42, 'Kilis'),
(20, 'Kocaeli'),
(53, 'Konya'),
(19, 'Kütahya'),
(41, 'Malatya'),
(47, 'Manisa'),
(29, 'Mardin'),
(4, 'Mersin'),
(14, 'Muğla'),
(77, 'Muş'),
(50, 'Nevşehir'),
(54, 'Niğde'),
(65, 'Ordu'),
(5, 'Osmaniye'),
(70, 'Rize'),
(60, 'Sakarya'),
(63, 'Samsun'),
(30, 'Siirt'),
(66, 'Sinop'),
(49, 'Sivas'),
(39, 'Şanlıurfa'),
(31, 'Şırnak'),
(45, 'Tekirdağ'),
(67, 'Tokat'),
(68, 'Trabzon'),
(32, 'Tunceli'),
(13, 'Uşak'),
(72, 'Van'),
(22, 'Yalova'),
(51, 'Yozgat'),
(78, 'Zonguldak');
