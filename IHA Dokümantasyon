YAZILIM İÇİN ŞİFRE VE BAZI NOTLAR

 

*** Aşağıdaki şifreler de yazılımda kullanılabilir.

Kullanıcı Kodu   : 10002  

Kullanıcı Adı    : metaanaliz              

Şifre            : iha1091

 

RSS kaynağının linkini de şifreler gömülü şekilde gönderiyoruz. Yazılıma bu linkle haberlerimiz otomatik entegre edilebilir.

 

RSS LİNKİ:

 

Standart haberler :

https://abonerss.iha.com.tr/xml/standartrss?UserCode=10002&UstKategori=0&UserName=metaanaliz&UserPassword=iha1091&Kategori=0&Sehir=0&wp=0&tagp=1&tip=1

 

 

 

A)      İki istek arasında 30 sn süre izin verilmiştir. 30 sn den sık istek yapılamaz.

B)      Bazı haberlerde fotoğraflar sonradan eklenebiliyor. 15 dakikalık tekrar sorgularla sonradan eklenen fotoğraf varsa habere iliştirilmeli.

 

Parametrelerin anlamı :

 

UserCode                                           = kullanıcı kodu

UserName                                         = kullanıcı adı

UserPassword                                  = şifre

UstKategori                                       = 0 hepsi , 1 Ulusal , 2 Yerel , 3 Dış

Sehir                                                     = 0 hepsi veya kod ile filtreleme yapılabilir.

Kategori                                              =0 hepsi veya kod ile filtreleme yapılabilir.

tagp                                                      = 0 normal veya 1 paragraf <p> tagı içinde yer alır.

 

 

Her abonemizin kendi farklı yapısı var

Rss reader programı size yollayacağımız ÖZEL ŞİFRELİ RSS LİNKİ ile login olacak şekilde tasarlanmalı.

 

Kendi yazacağınız program ile haberleri alabilirsiniz. Program haber metinlerini txt halinde seçili klasöre indirmeli ve fotoğrafları da aynı şekilde indirmeli. Fotoğraflar orijinal halinden boyutlandırılarak servis edilmektedir.

 

RSS sayfalarındaki  haberler  5 dakikada bir güncellenir.

 

PHP ve CURL ile program yazan bazı abonelerimiz login sayfasını geçmekte sıkıntı yaşamışlar ama sonra sorunu çözmüşlerdir. Bir müşterimizden bu konu ile ilgili bir kod parçacığı aldık .Aynı problemi yaşayanlara yardımcı olması açısından o da aşağıdadır.

 

<?php

    $ch = curl_init();

    curl_setopt($ch,CURLOPT_COOKIEJAR,NULL);

    curl_setopt($ch,CURLOPT_COOKIESESSION,true);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    curl_setopt($ch,CURLOPT_FOLLOWLOCATION,true);

    curl_setopt($ch,CURLOPT_USERAGENT,'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:5.0) Gecko/20100101 Firefox/5.0');

    curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,10);

    curl_setopt($ch,CURLOPT_TIMEOUT,10);

    curl_setopt($ch,CURLOPT_URL,http://abonerss.iha.com.tr/xml/standartrss?UserCode=…………..&UserName=……………..&UserPassword=…………….&tip=1&UstKategori=0&Kategori=0&Sehir=0&wp=0&tagp=0     );

    $exec = curl_exec($ch);

    print_r($exec);

?>    

 

aspx sayfalarına haberler xml formatında yazdırılır. O sebeple yapılması gereken xml formatındaki aspx sayfalarının kaynağının parse edilip bilgilerin alınmasıdır.

 

 

KATEGORİ VE ŞEHİR KODLARI

 

Kategori ve İl kodları aşağıdadır. Linkte 0 (hepsi) seçili. Linkte bu kodları yazarsanız kategorik haberleri alırsınız.

 

2             Magazin

3             Spor

4             Politika

5             Asayiş

8             Genel

9             Ekonomi

11           Haberde İnsan

12           Sağlık

13           Eğitim

14           Bilim Ve Teknoloji

15           Kültür Sanat

16           Çevre

 

***Hizmet paketiniz Bölgesel aboneli ise; yalnızca sözleşmede yer alan bölgenin/illerin haberini alabilirsiniz. Bunun dışındaki şehir kodlarını yazdığınızda RSS hata verir.

 

 

0 =Hepsi

1 =Adana

40 =Adıyaman

82 =Afyonkarahisar

73 =Ağrı

55 =Aksaray

64 =Amasya

6 =Ankara

7 =Antalya

58 =Ardahan

71 =Artvin

12 =Aydın

16 =Balıkesir

79 =Bartın

33 =Batman

36 =Bayburt

21 =Bilecik

76 =Bingöl

74 =Bitlis

61 =Bolu

10 =Burdur

15 =Bursa

17 =Çanakkale

24 =Çankırı

23 =Çorum

9 =Denizli

27 =Diyarbakır

62 =Düzce

44 =Edirne

28 =Elazığ

35 =Erzincan

34 =Erzurum

18 =Eskişehir

38 =Gaziantep

69 =Giresun

37 =Gümüşhane

75 =Hakkari

2 =Hatay

59 =Iğdır

11 =Isparta

43 =İstanbul

46 =İzmir

3 =Kahramanmaraş

80 =Karabük

56 =Karaman

57 =Kars

26 =Kastamonu

48 =Kayseri

25 =Kırıkkale

81 =Kırklareli

52 =Kırşehir

42 =Kilis

20 =Kocaeli

53 =Konya

19 =Kütahya

41 =Malatya

47 =Manisa

29 =Mardin

4 =Mersin

14 =Muğla

77 =Muş

50 =Nevşehir

54 =Niğde

65 =Ordu

5 =Osmaniye

70 =Rize

60 =Sakarya

63 =Samsun

30 =Siirt

66 =Sinop

49 =Sivas

39 =Şanlıurfa

31 =Şırnak

45 =Tekirdağ

67 =Tokat

68 =Trabzon

32 =Tunceli

13 =Uşak

72 =Van

22 =Yalova

51 =Yozgat

78 =Zonguldak