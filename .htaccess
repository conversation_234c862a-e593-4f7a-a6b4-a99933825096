DirectoryIndex index.php index.html

RewriteEngine On

# Backend API istekleri
RewriteCond %{REQUEST_URI} ^/backend/
RewriteRule ^backend/(.*)$ backend/$1 [L]

# Assets dosyaları
RewriteCond %{REQUEST_URI} ^/assets/
RewriteRule ^assets/(.*)$ assets/$1 [L]

# Uploads dosyaları
RewriteCond %{REQUEST_URI} ^/uploads/
RewriteRule ^uploads/(.*)$ uploads/$1 [L]

# React Router için - tüm diğer istekleri index.php'ye yönlendir
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/backend/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/uploads/
RewriteRule ^(.*)$ index.php [L,QSA]
