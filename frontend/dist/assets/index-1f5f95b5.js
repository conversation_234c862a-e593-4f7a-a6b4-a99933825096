import{r as T,a as Rr,R as Ir,b as oe,g as gt,L as V,N as Ve,u as Lr,c as Mt,d as Mr,e as jr,f as <PERSON>,h as <PERSON>e,B as Fr}from"./vendor-8ad64ce2.js";import{_ as Hr,a as $r}from"./utils-ac32d1bb.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))t(a);new MutationObserver(a=>{for(const s of a)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&t(o)}).observe(document,{childList:!0,subtree:!0});function i(a){const s={};return a.integrity&&(s.integrity=a.integrity),a.referrerPolicy&&(s.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?s.credentials="include":a.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function t(a){if(a.ep)return;a.ep=!0;const s=i(a);fetch(a.href,s)}})();var ur={exports:{}},bt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=T,Qr=Symbol.for("react.element"),Ur=Symbol.for("react.fragment"),zr=Object.prototype.hasOwnProperty,Br=qr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Kr={key:!0,ref:!0,__self:!0,__source:!0};function dr(r,n,i){var t,a={},s=null,o=null;i!==void 0&&(s=""+i),n.key!==void 0&&(s=""+n.key),n.ref!==void 0&&(o=n.ref);for(t in n)zr.call(n,t)&&!Kr.hasOwnProperty(t)&&(a[t]=n[t]);if(r&&r.defaultProps)for(t in n=r.defaultProps,n)a[t]===void 0&&(a[t]=n[t]);return{$$typeof:Qr,type:r,key:s,ref:o,props:a,_owner:Br.current}}bt.Fragment=Ur;bt.jsx=dr;bt.jsxs=dr;ur.exports=bt;var jt=ur.exports;const ie=jt.Fragment,e=jt.jsx,l=jt.jsxs;var Pt={},Kt=Rr;Pt.createRoot=Kt.createRoot,Pt.hydrateRoot=Kt.hydrateRoot;function Xe(r,n){r.prototype=Object.create(n.prototype),r.prototype.constructor=r,Hr(r,n)}var Je=function(){function r(){this.listeners=[]}var n=r.prototype;return n.subscribe=function(t){var a=this,s=t||function(){};return this.listeners.push(s),this.onSubscribe(),function(){a.listeners=a.listeners.filter(function(o){return o!==s}),a.onUnsubscribe()}},n.hasListeners=function(){return this.listeners.length>0},n.onSubscribe=function(){},n.onUnsubscribe=function(){},r}();function te(){return te=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var t in i)({}).hasOwnProperty.call(i,t)&&(r[t]=i[t])}return r},te.apply(null,arguments)}var lt=typeof window>"u";function Oe(){}function Vr(r,n){return typeof r=="function"?r(n):r}function Et(r){return typeof r=="number"&&r>=0&&r!==1/0}function ut(r){return Array.isArray(r)?r:[r]}function hr(r,n){return Math.max(r+(n||0)-Date.now(),0)}function at(r,n,i){return Nt(r)?typeof n=="function"?te({},i,{queryKey:r,queryFn:n}):te({},n,{queryKey:r}):r}function De(r,n,i){return Nt(r)?[te({},n,{queryKey:r}),i]:[r||{},n]}function Yr(r,n){if(r===!0&&n===!0||r==null&&n==null)return"all";if(r===!1&&n===!1)return"none";var i=r??!n;return i?"active":"inactive"}function Vt(r,n){var i=r.active,t=r.exact,a=r.fetching,s=r.inactive,o=r.predicate,c=r.queryKey,u=r.stale;if(Nt(c)){if(t){if(n.queryHash!==Dt(c,n.options))return!1}else if(!dt(n.queryKey,c))return!1}var d=Yr(i,s);if(d==="none")return!1;if(d!=="all"){var p=n.isActive();if(d==="active"&&!p||d==="inactive"&&p)return!1}return!(typeof u=="boolean"&&n.isStale()!==u||typeof a=="boolean"&&n.isFetching()!==a||o&&!o(n))}function Yt(r,n){var i=r.exact,t=r.fetching,a=r.predicate,s=r.mutationKey;if(Nt(s)){if(!n.options.mutationKey)return!1;if(i){if($e(n.options.mutationKey)!==$e(s))return!1}else if(!dt(n.options.mutationKey,s))return!1}return!(typeof t=="boolean"&&n.state.status==="loading"!==t||a&&!a(n))}function Dt(r,n){var i=(n==null?void 0:n.queryKeyHashFn)||$e;return i(r)}function $e(r){var n=ut(r);return Wr(n)}function Wr(r){return JSON.stringify(r,function(n,i){return At(i)?Object.keys(i).sort().reduce(function(t,a){return t[a]=i[a],t},{}):i})}function dt(r,n){return fr(ut(r),ut(n))}function fr(r,n){return r===n?!0:typeof r!=typeof n?!1:r&&n&&typeof r=="object"&&typeof n=="object"?!Object.keys(n).some(function(i){return!fr(r[i],n[i])}):!1}function ht(r,n){if(r===n)return r;var i=Array.isArray(r)&&Array.isArray(n);if(i||At(r)&&At(n)){for(var t=i?r.length:Object.keys(r).length,a=i?n:Object.keys(n),s=a.length,o=i?[]:{},c=0,u=0;u<s;u++){var d=i?u:a[u];o[d]=ht(r[d],n[d]),o[d]===r[d]&&c++}return t===s&&c===t?r:o}return n}function Gr(r,n){if(r&&!n||n&&!r)return!1;for(var i in r)if(r[i]!==n[i])return!1;return!0}function At(r){if(!Wt(r))return!1;var n=r.constructor;if(typeof n>"u")return!0;var i=n.prototype;return!(!Wt(i)||!i.hasOwnProperty("isPrototypeOf"))}function Wt(r){return Object.prototype.toString.call(r)==="[object Object]"}function Nt(r){return typeof r=="string"||Array.isArray(r)}function Xr(r){return new Promise(function(n){setTimeout(n,r)})}function Gt(r){Promise.resolve().then(r).catch(function(n){return setTimeout(function(){throw n})})}function mr(){if(typeof AbortController=="function")return new AbortController}var Jr=function(r){Xe(n,r);function n(){var t;return t=r.call(this)||this,t.setup=function(a){var s;if(!lt&&((s=window)!=null&&s.addEventListener)){var o=function(){return a()};return window.addEventListener("visibilitychange",o,!1),window.addEventListener("focus",o,!1),function(){window.removeEventListener("visibilitychange",o),window.removeEventListener("focus",o)}}},t}var i=n.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var a;(a=this.cleanup)==null||a.call(this),this.cleanup=void 0}},i.setEventListener=function(a){var s,o=this;this.setup=a,(s=this.cleanup)==null||s.call(this),this.cleanup=a(function(c){typeof c=="boolean"?o.setFocused(c):o.onFocus()})},i.setFocused=function(a){this.focused=a,a&&this.onFocus()},i.onFocus=function(){this.listeners.forEach(function(a){a()})},i.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},n}(Je),Ge=new Jr,Zr=function(r){Xe(n,r);function n(){var t;return t=r.call(this)||this,t.setup=function(a){var s;if(!lt&&((s=window)!=null&&s.addEventListener)){var o=function(){return a()};return window.addEventListener("online",o,!1),window.addEventListener("offline",o,!1),function(){window.removeEventListener("online",o),window.removeEventListener("offline",o)}}},t}var i=n.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var a;(a=this.cleanup)==null||a.call(this),this.cleanup=void 0}},i.setEventListener=function(a){var s,o=this;this.setup=a,(s=this.cleanup)==null||s.call(this),this.cleanup=a(function(c){typeof c=="boolean"?o.setOnline(c):o.onOnline()})},i.setOnline=function(a){this.online=a,a&&this.onOnline()},i.onOnline=function(){this.listeners.forEach(function(a){a()})},i.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},n}(Je),it=new Zr;function en(r){return Math.min(1e3*Math.pow(2,r),3e4)}function ft(r){return typeof(r==null?void 0:r.cancel)=="function"}var pr=function(n){this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent};function st(r){return r instanceof pr}var yr=function(n){var i=this,t=!1,a,s,o,c;this.abort=n.abort,this.cancel=function(v){return a==null?void 0:a(v)},this.cancelRetry=function(){t=!0},this.continueRetry=function(){t=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(v,b){o=v,c=b});var u=function(b){i.isResolved||(i.isResolved=!0,n.onSuccess==null||n.onSuccess(b),s==null||s(),o(b))},d=function(b){i.isResolved||(i.isResolved=!0,n.onError==null||n.onError(b),s==null||s(),c(b))},p=function(){return new Promise(function(b){s=b,i.isPaused=!0,n.onPause==null||n.onPause()}).then(function(){s=void 0,i.isPaused=!1,n.onContinue==null||n.onContinue()})},h=function v(){if(!i.isResolved){var b;try{b=n.fn()}catch(m){b=Promise.reject(m)}a=function(w){if(!i.isResolved&&(d(new pr(w)),i.abort==null||i.abort(),ft(b)))try{b.cancel()}catch{}},i.isTransportCancelable=ft(b),Promise.resolve(b).then(u).catch(function(m){var w,P;if(!i.isResolved){var j=(w=n.retry)!=null?w:3,H=(P=n.retryDelay)!=null?P:en,$=typeof H=="function"?H(i.failureCount,m):H,z=j===!0||typeof j=="number"&&i.failureCount<j||typeof j=="function"&&j(i.failureCount,m);if(t||!z){d(m);return}i.failureCount++,n.onFail==null||n.onFail(i.failureCount,m),Xr($).then(function(){if(!Ge.isFocused()||!it.isOnline())return p()}).then(function(){t?d(m):v()})}})}};h()},tn=function(){function r(){this.queue=[],this.transactions=0,this.notifyFn=function(i){i()},this.batchNotifyFn=function(i){i()}}var n=r.prototype;return n.batch=function(t){var a;this.transactions++;try{a=t()}finally{this.transactions--,this.transactions||this.flush()}return a},n.schedule=function(t){var a=this;this.transactions?this.queue.push(t):Gt(function(){a.notifyFn(t)})},n.batchCalls=function(t){var a=this;return function(){for(var s=arguments.length,o=new Array(s),c=0;c<s;c++)o[c]=arguments[c];a.schedule(function(){t.apply(void 0,o)})}},n.flush=function(){var t=this,a=this.queue;this.queue=[],a.length&&Gt(function(){t.batchNotifyFn(function(){a.forEach(function(s){t.notifyFn(s)})})})},n.setNotifyFunction=function(t){this.notifyFn=t},n.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},r}(),he=new tn,vr=console;function mt(){return vr}function rn(r){vr=r}var nn=function(){function r(i){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=i.defaultOptions,this.setOptions(i.options),this.observers=[],this.cache=i.cache,this.queryKey=i.queryKey,this.queryHash=i.queryHash,this.initialState=i.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=i.meta,this.scheduleGc()}var n=r.prototype;return n.setOptions=function(t){var a;this.options=te({},this.defaultOptions,t),this.meta=t==null?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,(a=this.options.cacheTime)!=null?a:5*60*1e3)},n.setDefaultOptions=function(t){this.defaultOptions=t},n.scheduleGc=function(){var t=this;this.clearGcTimeout(),Et(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){t.optionalRemove()},this.cacheTime))},n.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},n.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},n.setData=function(t,a){var s,o,c=this.state.data,u=Vr(t,c);return(s=(o=this.options).isDataEqual)!=null&&s.call(o,c,u)?u=c:this.options.structuralSharing!==!1&&(u=ht(c,u)),this.dispatch({data:u,type:"success",dataUpdatedAt:a==null?void 0:a.updatedAt}),u},n.setState=function(t,a){this.dispatch({type:"setState",state:t,setStateOptions:a})},n.cancel=function(t){var a,s=this.promise;return(a=this.retryer)==null||a.cancel(t),s?s.then(Oe).catch(Oe):Promise.resolve()},n.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},n.reset=function(){this.destroy(),this.setState(this.initialState)},n.isActive=function(){return this.observers.some(function(t){return t.options.enabled!==!1})},n.isFetching=function(){return this.state.isFetching},n.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(t){return t.getCurrentResult().isStale})},n.isStaleByTime=function(t){return t===void 0&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!hr(this.state.dataUpdatedAt,t)},n.onFocus=function(){var t,a=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});a&&a.refetch(),(t=this.retryer)==null||t.continue()},n.onOnline=function(){var t,a=this.observers.find(function(s){return s.shouldFetchOnReconnect()});a&&a.refetch(),(t=this.retryer)==null||t.continue()},n.addObserver=function(t){this.observers.indexOf(t)===-1&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},n.removeObserver=function(t){this.observers.indexOf(t)!==-1&&(this.observers=this.observers.filter(function(a){return a!==t}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},n.getObserversCount=function(){return this.observers.length},n.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},n.fetch=function(t,a){var s=this,o,c,u;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(a!=null&&a.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var d;return(d=this.retryer)==null||d.continueRetry(),this.promise}}if(t&&this.setOptions(t),!this.options.queryFn){var p=this.observers.find(function(H){return H.options.queryFn});p&&this.setOptions(p.options)}var h=ut(this.queryKey),v=mr(),b={queryKey:h,pageParam:void 0,meta:this.meta};Object.defineProperty(b,"signal",{enumerable:!0,get:function(){if(v)return s.abortSignalConsumed=!0,v.signal}});var m=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(b)):Promise.reject("Missing queryFn")},w={fetchOptions:a,options:this.options,queryKey:h,state:this.state,fetchFn:m,meta:this.meta};if((o=this.options.behavior)!=null&&o.onFetch){var P;(P=this.options.behavior)==null||P.onFetch(w)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((c=w.fetchOptions)==null?void 0:c.meta)){var j;this.dispatch({type:"fetch",meta:(j=w.fetchOptions)==null?void 0:j.meta})}return this.retryer=new yr({fn:w.fetchFn,abort:v==null||(u=v.abort)==null?void 0:u.bind(v),onSuccess:function($){s.setData($),s.cache.config.onSuccess==null||s.cache.config.onSuccess($,s),s.cacheTime===0&&s.optionalRemove()},onError:function($){st($)&&$.silent||s.dispatch({type:"error",error:$}),st($)||(s.cache.config.onError==null||s.cache.config.onError($,s),mt().error($)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:w.options.retry,retryDelay:w.options.retryDelay}),this.promise=this.retryer.promise,this.promise},n.dispatch=function(t){var a=this;this.state=this.reducer(this.state,t),he.batch(function(){a.observers.forEach(function(s){s.onQueryUpdate(t)}),a.cache.notify({query:a,type:"queryUpdated",action:t})})},n.getDefaultState=function(t){var a=typeof t.initialData=="function"?t.initialData():t.initialData,s=typeof t.initialData<"u",o=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,c=typeof a<"u";return{data:a,dataUpdateCount:0,dataUpdatedAt:c?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:c?"success":"idle"}},n.reducer=function(t,a){var s,o;switch(a.type){case"failed":return te({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return te({},t,{isPaused:!0});case"continue":return te({},t,{isPaused:!1});case"fetch":return te({},t,{fetchFailureCount:0,fetchMeta:(s=a.meta)!=null?s:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return te({},t,{data:a.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:(o=a.dataUpdatedAt)!=null?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var c=a.error;return st(c)&&c.revert&&this.revertState?te({},this.revertState):te({},t,{error:c,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return te({},t,{isInvalidated:!0});case"setState":return te({},t,a.state);default:return t}},r}(),an=function(r){Xe(n,r);function n(t){var a;return a=r.call(this)||this,a.config=t||{},a.queries=[],a.queriesMap={},a}var i=n.prototype;return i.build=function(a,s,o){var c,u=s.queryKey,d=(c=s.queryHash)!=null?c:Dt(u,s),p=this.get(d);return p||(p=new nn({cache:this,queryKey:u,queryHash:d,options:a.defaultQueryOptions(s),state:o,defaultOptions:a.getQueryDefaults(u),meta:s.meta}),this.add(p)),p},i.add=function(a){this.queriesMap[a.queryHash]||(this.queriesMap[a.queryHash]=a,this.queries.push(a),this.notify({type:"queryAdded",query:a}))},i.remove=function(a){var s=this.queriesMap[a.queryHash];s&&(a.destroy(),this.queries=this.queries.filter(function(o){return o!==a}),s===a&&delete this.queriesMap[a.queryHash],this.notify({type:"queryRemoved",query:a}))},i.clear=function(){var a=this;he.batch(function(){a.queries.forEach(function(s){a.remove(s)})})},i.get=function(a){return this.queriesMap[a]},i.getAll=function(){return this.queries},i.find=function(a,s){var o=De(a,s),c=o[0];return typeof c.exact>"u"&&(c.exact=!0),this.queries.find(function(u){return Vt(c,u)})},i.findAll=function(a,s){var o=De(a,s),c=o[0];return Object.keys(c).length>0?this.queries.filter(function(u){return Vt(c,u)}):this.queries},i.notify=function(a){var s=this;he.batch(function(){s.listeners.forEach(function(o){o(a)})})},i.onFocus=function(){var a=this;he.batch(function(){a.queries.forEach(function(s){s.onFocus()})})},i.onOnline=function(){var a=this;he.batch(function(){a.queries.forEach(function(s){s.onOnline()})})},n}(Je),sn=function(){function r(i){this.options=te({},i.defaultOptions,i.options),this.mutationId=i.mutationId,this.mutationCache=i.mutationCache,this.observers=[],this.state=i.state||on(),this.meta=i.meta}var n=r.prototype;return n.setState=function(t){this.dispatch({type:"setState",state:t})},n.addObserver=function(t){this.observers.indexOf(t)===-1&&this.observers.push(t)},n.removeObserver=function(t){this.observers=this.observers.filter(function(a){return a!==t})},n.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Oe).catch(Oe)):Promise.resolve()},n.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},n.execute=function(){var t=this,a,s=this.state.status==="loading",o=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),o=o.then(function(){t.mutationCache.config.onMutate==null||t.mutationCache.config.onMutate(t.state.variables,t)}).then(function(){return t.options.onMutate==null?void 0:t.options.onMutate(t.state.variables)}).then(function(c){c!==t.state.context&&t.dispatch({type:"loading",context:c,variables:t.state.variables})})),o.then(function(){return t.executeMutation()}).then(function(c){a=c,t.mutationCache.config.onSuccess==null||t.mutationCache.config.onSuccess(a,t.state.variables,t.state.context,t)}).then(function(){return t.options.onSuccess==null?void 0:t.options.onSuccess(a,t.state.variables,t.state.context)}).then(function(){return t.options.onSettled==null?void 0:t.options.onSettled(a,null,t.state.variables,t.state.context)}).then(function(){return t.dispatch({type:"success",data:a}),a}).catch(function(c){return t.mutationCache.config.onError==null||t.mutationCache.config.onError(c,t.state.variables,t.state.context,t),mt().error(c),Promise.resolve().then(function(){return t.options.onError==null?void 0:t.options.onError(c,t.state.variables,t.state.context)}).then(function(){return t.options.onSettled==null?void 0:t.options.onSettled(void 0,c,t.state.variables,t.state.context)}).then(function(){throw t.dispatch({type:"error",error:c}),c})})},n.executeMutation=function(){var t=this,a;return this.retryer=new yr({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:(a=this.options.retry)!=null?a:0,retryDelay:this.options.retryDelay}),this.retryer.promise},n.dispatch=function(t){var a=this;this.state=cn(this.state,t),he.batch(function(){a.observers.forEach(function(s){s.onMutationUpdate(t)}),a.mutationCache.notify(a)})},r}();function on(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function cn(r,n){switch(n.type){case"failed":return te({},r,{failureCount:r.failureCount+1});case"pause":return te({},r,{isPaused:!0});case"continue":return te({},r,{isPaused:!1});case"loading":return te({},r,{context:n.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:n.variables});case"success":return te({},r,{data:n.data,error:null,status:"success",isPaused:!1});case"error":return te({},r,{data:void 0,error:n.error,failureCount:r.failureCount+1,isPaused:!1,status:"error"});case"setState":return te({},r,n.state);default:return r}}var ln=function(r){Xe(n,r);function n(t){var a;return a=r.call(this)||this,a.config=t||{},a.mutations=[],a.mutationId=0,a}var i=n.prototype;return i.build=function(a,s,o){var c=new sn({mutationCache:this,mutationId:++this.mutationId,options:a.defaultMutationOptions(s),state:o,defaultOptions:s.mutationKey?a.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(c),c},i.add=function(a){this.mutations.push(a),this.notify(a)},i.remove=function(a){this.mutations=this.mutations.filter(function(s){return s!==a}),a.cancel(),this.notify(a)},i.clear=function(){var a=this;he.batch(function(){a.mutations.forEach(function(s){a.remove(s)})})},i.getAll=function(){return this.mutations},i.find=function(a){return typeof a.exact>"u"&&(a.exact=!0),this.mutations.find(function(s){return Yt(a,s)})},i.findAll=function(a){return this.mutations.filter(function(s){return Yt(a,s)})},i.notify=function(a){var s=this;he.batch(function(){s.listeners.forEach(function(o){o(a)})})},i.onFocus=function(){this.resumePausedMutations()},i.onOnline=function(){this.resumePausedMutations()},i.resumePausedMutations=function(){var a=this.mutations.filter(function(s){return s.state.isPaused});return he.batch(function(){return a.reduce(function(s,o){return s.then(function(){return o.continue().catch(Oe)})},Promise.resolve())})},n}(Je);function un(){return{onFetch:function(n){n.fetchFn=function(){var i,t,a,s,o,c,u=(i=n.fetchOptions)==null||(t=i.meta)==null?void 0:t.refetchPage,d=(a=n.fetchOptions)==null||(s=a.meta)==null?void 0:s.fetchMore,p=d==null?void 0:d.pageParam,h=(d==null?void 0:d.direction)==="forward",v=(d==null?void 0:d.direction)==="backward",b=((o=n.state.data)==null?void 0:o.pages)||[],m=((c=n.state.data)==null?void 0:c.pageParams)||[],w=mr(),P=w==null?void 0:w.signal,j=m,H=!1,$=n.options.queryFn||function(){return Promise.reject("Missing queryFn")},z=function(_,x,R,W){return j=W?[x].concat(j):[].concat(j,[x]),W?[R].concat(_):[].concat(_,[R])},L=function(_,x,R,W){if(H)return Promise.reject("Cancelled");if(typeof R>"u"&&!x&&_.length)return Promise.resolve(_);var re={queryKey:n.queryKey,signal:P,pageParam:R,meta:n.meta},ae=$(re),G=Promise.resolve(ae).then(function(de){return z(_,R,de,W)});if(ft(ae)){var ue=G;ue.cancel=ae.cancel}return G},D;if(!b.length)D=L([]);else if(h){var ee=typeof p<"u",q=ee?p:Xt(n.options,b);D=L(b,ee,q)}else if(v){var K=typeof p<"u",C=K?p:dn(n.options,b);D=L(b,K,C,!0)}else(function(){j=[];var Z=typeof n.options.getNextPageParam>"u",_=u&&b[0]?u(b[0],0,b):!0;D=_?L([],Z,m[0]):Promise.resolve(z([],m[0],b[0]));for(var x=function(re){D=D.then(function(ae){var G=u&&b[re]?u(b[re],re,b):!0;if(G){var ue=Z?m[re]:Xt(n.options,ae);return L(ae,Z,ue)}return Promise.resolve(z(ae,m[re],b[re]))})},R=1;R<b.length;R++)x(R)})();var B=D.then(function(Z){return{pages:Z,pageParams:j}}),U=B;return U.cancel=function(){H=!0,w==null||w.abort(),ft(D)&&D.cancel()},B}}}}function Xt(r,n){return r.getNextPageParam==null?void 0:r.getNextPageParam(n[n.length-1],n)}function dn(r,n){return r.getPreviousPageParam==null?void 0:r.getPreviousPageParam(n[0],n)}var hn=function(){function r(i){i===void 0&&(i={}),this.queryCache=i.queryCache||new an,this.mutationCache=i.mutationCache||new ln,this.defaultOptions=i.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var n=r.prototype;return n.mount=function(){var t=this;this.unsubscribeFocus=Ge.subscribe(function(){Ge.isFocused()&&it.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())}),this.unsubscribeOnline=it.subscribe(function(){Ge.isFocused()&&it.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())})},n.unmount=function(){var t,a;(t=this.unsubscribeFocus)==null||t.call(this),(a=this.unsubscribeOnline)==null||a.call(this)},n.isFetching=function(t,a){var s=De(t,a),o=s[0];return o.fetching=!0,this.queryCache.findAll(o).length},n.isMutating=function(t){return this.mutationCache.findAll(te({},t,{fetching:!0})).length},n.getQueryData=function(t,a){var s;return(s=this.queryCache.find(t,a))==null?void 0:s.state.data},n.getQueriesData=function(t){return this.getQueryCache().findAll(t).map(function(a){var s=a.queryKey,o=a.state,c=o.data;return[s,c]})},n.setQueryData=function(t,a,s){var o=at(t),c=this.defaultQueryOptions(o);return this.queryCache.build(this,c).setData(a,s)},n.setQueriesData=function(t,a,s){var o=this;return he.batch(function(){return o.getQueryCache().findAll(t).map(function(c){var u=c.queryKey;return[u,o.setQueryData(u,a,s)]})})},n.getQueryState=function(t,a){var s;return(s=this.queryCache.find(t,a))==null?void 0:s.state},n.removeQueries=function(t,a){var s=De(t,a),o=s[0],c=this.queryCache;he.batch(function(){c.findAll(o).forEach(function(u){c.remove(u)})})},n.resetQueries=function(t,a,s){var o=this,c=De(t,a,s),u=c[0],d=c[1],p=this.queryCache,h=te({},u,{active:!0});return he.batch(function(){return p.findAll(u).forEach(function(v){v.reset()}),o.refetchQueries(h,d)})},n.cancelQueries=function(t,a,s){var o=this,c=De(t,a,s),u=c[0],d=c[1],p=d===void 0?{}:d;typeof p.revert>"u"&&(p.revert=!0);var h=he.batch(function(){return o.queryCache.findAll(u).map(function(v){return v.cancel(p)})});return Promise.all(h).then(Oe).catch(Oe)},n.invalidateQueries=function(t,a,s){var o,c,u,d=this,p=De(t,a,s),h=p[0],v=p[1],b=te({},h,{active:(o=(c=h.refetchActive)!=null?c:h.active)!=null?o:!0,inactive:(u=h.refetchInactive)!=null?u:!1});return he.batch(function(){return d.queryCache.findAll(h).forEach(function(m){m.invalidate()}),d.refetchQueries(b,v)})},n.refetchQueries=function(t,a,s){var o=this,c=De(t,a,s),u=c[0],d=c[1],p=he.batch(function(){return o.queryCache.findAll(u).map(function(v){return v.fetch(void 0,te({},d,{meta:{refetchPage:u==null?void 0:u.refetchPage}}))})}),h=Promise.all(p).then(Oe);return d!=null&&d.throwOnError||(h=h.catch(Oe)),h},n.fetchQuery=function(t,a,s){var o=at(t,a,s),c=this.defaultQueryOptions(o);typeof c.retry>"u"&&(c.retry=!1);var u=this.queryCache.build(this,c);return u.isStaleByTime(c.staleTime)?u.fetch(c):Promise.resolve(u.state.data)},n.prefetchQuery=function(t,a,s){return this.fetchQuery(t,a,s).then(Oe).catch(Oe)},n.fetchInfiniteQuery=function(t,a,s){var o=at(t,a,s);return o.behavior=un(),this.fetchQuery(o)},n.prefetchInfiniteQuery=function(t,a,s){return this.fetchInfiniteQuery(t,a,s).then(Oe).catch(Oe)},n.cancelMutations=function(){var t=this,a=he.batch(function(){return t.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(a).then(Oe).catch(Oe)},n.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},n.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},n.getQueryCache=function(){return this.queryCache},n.getMutationCache=function(){return this.mutationCache},n.getDefaultOptions=function(){return this.defaultOptions},n.setDefaultOptions=function(t){this.defaultOptions=t},n.setQueryDefaults=function(t,a){var s=this.queryDefaults.find(function(o){return $e(t)===$e(o.queryKey)});s?s.defaultOptions=a:this.queryDefaults.push({queryKey:t,defaultOptions:a})},n.getQueryDefaults=function(t){var a;return t?(a=this.queryDefaults.find(function(s){return dt(t,s.queryKey)}))==null?void 0:a.defaultOptions:void 0},n.setMutationDefaults=function(t,a){var s=this.mutationDefaults.find(function(o){return $e(t)===$e(o.mutationKey)});s?s.defaultOptions=a:this.mutationDefaults.push({mutationKey:t,defaultOptions:a})},n.getMutationDefaults=function(t){var a;return t?(a=this.mutationDefaults.find(function(s){return dt(t,s.mutationKey)}))==null?void 0:a.defaultOptions:void 0},n.defaultQueryOptions=function(t){if(t!=null&&t._defaulted)return t;var a=te({},this.defaultOptions.queries,this.getQueryDefaults(t==null?void 0:t.queryKey),t,{_defaulted:!0});return!a.queryHash&&a.queryKey&&(a.queryHash=Dt(a.queryKey,a)),a},n.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},n.defaultMutationOptions=function(t){return t!=null&&t._defaulted?t:te({},this.defaultOptions.mutations,this.getMutationDefaults(t==null?void 0:t.mutationKey),t,{_defaulted:!0})},n.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},r}(),fn=function(r){Xe(n,r);function n(t,a){var s;return s=r.call(this)||this,s.client=t,s.options=a,s.trackedProps=[],s.selectError=null,s.bindMethods(),s.setOptions(a),s}var i=n.prototype;return i.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},i.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Jt(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},i.onUnsubscribe=function(){this.listeners.length||this.destroy()},i.shouldFetchOnReconnect=function(){return xt(this.currentQuery,this.options,this.options.refetchOnReconnect)},i.shouldFetchOnWindowFocus=function(){return xt(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},i.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},i.setOptions=function(a,s){var o=this.options,c=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(a),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=o.queryKey),this.updateQuery();var u=this.hasListeners();u&&Zt(this.currentQuery,c,this.options,o)&&this.executeFetch(),this.updateResult(s),u&&(this.currentQuery!==c||this.options.enabled!==o.enabled||this.options.staleTime!==o.staleTime)&&this.updateStaleTimeout();var d=this.computeRefetchInterval();u&&(this.currentQuery!==c||this.options.enabled!==o.enabled||d!==this.currentRefetchInterval)&&this.updateRefetchInterval(d)},i.getOptimisticResult=function(a){var s=this.client.defaultQueryObserverOptions(a),o=this.client.getQueryCache().build(this.client,s);return this.createResult(o,s)},i.getCurrentResult=function(){return this.currentResult},i.trackResult=function(a,s){var o=this,c={},u=function(p){o.trackedProps.includes(p)||o.trackedProps.push(p)};return Object.keys(a).forEach(function(d){Object.defineProperty(c,d,{configurable:!1,enumerable:!0,get:function(){return u(d),a[d]}})}),(s.useErrorBoundary||s.suspense)&&u("error"),c},i.getNextResult=function(a){var s=this;return new Promise(function(o,c){var u=s.subscribe(function(d){d.isFetching||(u(),d.isError&&(a!=null&&a.throwOnError)?c(d.error):o(d))})})},i.getCurrentQuery=function(){return this.currentQuery},i.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},i.refetch=function(a){return this.fetch(te({},a,{meta:{refetchPage:a==null?void 0:a.refetchPage}}))},i.fetchOptimistic=function(a){var s=this,o=this.client.defaultQueryObserverOptions(a),c=this.client.getQueryCache().build(this.client,o);return c.fetch().then(function(){return s.createResult(c,o)})},i.fetch=function(a){var s=this;return this.executeFetch(a).then(function(){return s.updateResult(),s.currentResult})},i.executeFetch=function(a){this.updateQuery();var s=this.currentQuery.fetch(this.options,a);return a!=null&&a.throwOnError||(s=s.catch(Oe)),s},i.updateStaleTimeout=function(){var a=this;if(this.clearStaleTimeout(),!(lt||this.currentResult.isStale||!Et(this.options.staleTime))){var s=hr(this.currentResult.dataUpdatedAt,this.options.staleTime),o=s+1;this.staleTimeoutId=setTimeout(function(){a.currentResult.isStale||a.updateResult()},o)}},i.computeRefetchInterval=function(){var a;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(a=this.options.refetchInterval)!=null?a:!1},i.updateRefetchInterval=function(a){var s=this;this.clearRefetchInterval(),this.currentRefetchInterval=a,!(lt||this.options.enabled===!1||!Et(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(s.options.refetchIntervalInBackground||Ge.isFocused())&&s.executeFetch()},this.currentRefetchInterval))},i.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},i.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},i.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},i.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},i.createResult=function(a,s){var o=this.currentQuery,c=this.options,u=this.currentResult,d=this.currentResultState,p=this.currentResultOptions,h=a!==o,v=h?a.state:this.currentQueryInitialState,b=h?this.currentResult:this.previousQueryResult,m=a.state,w=m.dataUpdatedAt,P=m.error,j=m.errorUpdatedAt,H=m.isFetching,$=m.status,z=!1,L=!1,D;if(s.optimisticResults){var ee=this.hasListeners(),q=!ee&&Jt(a,s),K=ee&&Zt(a,o,s,c);(q||K)&&(H=!0,w||($="loading"))}if(s.keepPreviousData&&!m.dataUpdateCount&&(b!=null&&b.isSuccess)&&$!=="error")D=b.data,w=b.dataUpdatedAt,$=b.status,z=!0;else if(s.select&&typeof m.data<"u")if(u&&m.data===(d==null?void 0:d.data)&&s.select===this.selectFn)D=this.selectResult;else try{this.selectFn=s.select,D=s.select(m.data),s.structuralSharing!==!1&&(D=ht(u==null?void 0:u.data,D)),this.selectResult=D,this.selectError=null}catch(U){mt().error(U),this.selectError=U}else D=m.data;if(typeof s.placeholderData<"u"&&typeof D>"u"&&($==="loading"||$==="idle")){var C;if(u!=null&&u.isPlaceholderData&&s.placeholderData===(p==null?void 0:p.placeholderData))C=u.data;else if(C=typeof s.placeholderData=="function"?s.placeholderData():s.placeholderData,s.select&&typeof C<"u")try{C=s.select(C),s.structuralSharing!==!1&&(C=ht(u==null?void 0:u.data,C)),this.selectError=null}catch(U){mt().error(U),this.selectError=U}typeof C<"u"&&($="success",D=C,L=!0)}this.selectError&&(P=this.selectError,D=this.selectResult,j=Date.now(),$="error");var B={status:$,isLoading:$==="loading",isSuccess:$==="success",isError:$==="error",isIdle:$==="idle",data:D,dataUpdatedAt:w,error:P,errorUpdatedAt:j,failureCount:m.fetchFailureCount,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>v.dataUpdateCount||m.errorUpdateCount>v.errorUpdateCount,isFetching:H,isRefetching:H&&$!=="loading",isLoadingError:$==="error"&&m.dataUpdatedAt===0,isPlaceholderData:L,isPreviousData:z,isRefetchError:$==="error"&&m.dataUpdatedAt!==0,isStale:Ft(a,s),refetch:this.refetch,remove:this.remove};return B},i.shouldNotifyListeners=function(a,s){if(!s)return!0;var o=this.options,c=o.notifyOnChangeProps,u=o.notifyOnChangePropsExclusions;if(!c&&!u||c==="tracked"&&!this.trackedProps.length)return!0;var d=c==="tracked"?this.trackedProps:c;return Object.keys(a).some(function(p){var h=p,v=a[h]!==s[h],b=d==null?void 0:d.some(function(w){return w===p}),m=u==null?void 0:u.some(function(w){return w===p});return v&&!m&&(!d||b)})},i.updateResult=function(a){var s=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Gr(this.currentResult,s)){var o={cache:!0};(a==null?void 0:a.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,s)&&(o.listeners=!0),this.notify(te({},o,a))}},i.updateQuery=function(){var a=this.client.getQueryCache().build(this.client,this.options);if(a!==this.currentQuery){var s=this.currentQuery;this.currentQuery=a,this.currentQueryInitialState=a.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(s==null||s.removeObserver(this),a.addObserver(this))}},i.onQueryUpdate=function(a){var s={};a.type==="success"?s.onSuccess=!0:a.type==="error"&&!st(a.error)&&(s.onError=!0),this.updateResult(s),this.hasListeners()&&this.updateTimers()},i.notify=function(a){var s=this;he.batch(function(){a.onSuccess?(s.options.onSuccess==null||s.options.onSuccess(s.currentResult.data),s.options.onSettled==null||s.options.onSettled(s.currentResult.data,null)):a.onError&&(s.options.onError==null||s.options.onError(s.currentResult.error),s.options.onSettled==null||s.options.onSettled(void 0,s.currentResult.error)),a.listeners&&s.listeners.forEach(function(o){o(s.currentResult)}),a.cache&&s.client.getQueryCache().notify({query:s.currentQuery,type:"observerResultsUpdated"})})},n}(Je);function mn(r,n){return n.enabled!==!1&&!r.state.dataUpdatedAt&&!(r.state.status==="error"&&n.retryOnMount===!1)}function Jt(r,n){return mn(r,n)||r.state.dataUpdatedAt>0&&xt(r,n,n.refetchOnMount)}function xt(r,n,i){if(n.enabled!==!1){var t=typeof i=="function"?i(r):i;return t==="always"||t!==!1&&Ft(r,n)}return!1}function Zt(r,n,i,t){return i.enabled!==!1&&(r!==n||t.enabled===!1)&&(!i.suspense||r.state.status!=="error")&&Ft(r,i)}function Ft(r,n){return r.isStaleByTime(n.staleTime)}var pn=Ir.unstable_batchedUpdates;he.setBatchNotifyFunction(pn);var yn=console;rn(yn);var er=oe.createContext(void 0),gr=oe.createContext(!1);function br(r){return r&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=er),window.ReactQueryClientContext):er}var Nr=function(){var n=oe.useContext(br(oe.useContext(gr)));if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n},vn=function(n){var i=n.client,t=n.contextSharing,a=t===void 0?!1:t,s=n.children;oe.useEffect(function(){return i.mount(),function(){i.unmount()}},[i]);var o=br(a);return oe.createElement(gr.Provider,{value:a},oe.createElement(o.Provider,{value:i},s))};function gn(){var r=!1;return{clearReset:function(){r=!1},reset:function(){r=!0},isReset:function(){return r}}}var bn=oe.createContext(gn()),Nn=function(){return oe.useContext(bn)};function wn(r,n,i){return typeof n=="function"?n.apply(void 0,i):typeof n=="boolean"?n:!!r}function On(r,n){var i=oe.useRef(!1),t=oe.useState(0),a=t[1],s=Nr(),o=Nn(),c=s.defaultQueryObserverOptions(r);c.optimisticResults=!0,c.onError&&(c.onError=he.batchCalls(c.onError)),c.onSuccess&&(c.onSuccess=he.batchCalls(c.onSuccess)),c.onSettled&&(c.onSettled=he.batchCalls(c.onSettled)),c.suspense&&(typeof c.staleTime!="number"&&(c.staleTime=1e3),c.cacheTime===0&&(c.cacheTime=1)),(c.suspense||c.useErrorBoundary)&&(o.isReset()||(c.retryOnMount=!1));var u=oe.useState(function(){return new n(s,c)}),d=u[0],p=d.getOptimisticResult(c);if(oe.useEffect(function(){i.current=!0,o.clearReset();var h=d.subscribe(he.batchCalls(function(){i.current&&a(function(v){return v+1})}));return d.updateResult(),function(){i.current=!1,h()}},[o,d]),oe.useEffect(function(){d.setOptions(c,{listeners:!1})},[c,d]),c.suspense&&p.isLoading)throw d.fetchOptimistic(c).then(function(h){var v=h.data;c.onSuccess==null||c.onSuccess(v),c.onSettled==null||c.onSettled(v,null)}).catch(function(h){o.clearReset(),c.onError==null||c.onError(h),c.onSettled==null||c.onSettled(void 0,h)});if(p.isError&&!o.isReset()&&!p.isFetching&&wn(c.suspense,c.useErrorBoundary,[p.error,d.getCurrentQuery()]))throw p.error;return c.notifyOnChangeProps==="tracked"&&(p=d.trackResult(p,c)),p}function ye(r,n,i){var t=at(r,n,i);return On(t,fn)}var wr={exports:{}},kn="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Sn=kn,Cn=Sn;function Or(){}function kr(){}kr.resetWarningCache=Or;var _n=function(){function r(t,a,s,o,c,u){if(u!==Cn){var d=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw d.name="Invariant Violation",d}}r.isRequired=r;function n(){return r}var i={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:n,element:r,elementType:r,instanceOf:n,node:r,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:kr,resetWarningCache:Or};return i.PropTypes=i,i};wr.exports=_n();var Tn=wr.exports;const I=gt(Tn);var Pn=typeof Element<"u",En=typeof Map=="function",An=typeof Set=="function",xn=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function ot(r,n){if(r===n)return!0;if(r&&n&&typeof r=="object"&&typeof n=="object"){if(r.constructor!==n.constructor)return!1;var i,t,a;if(Array.isArray(r)){if(i=r.length,i!=n.length)return!1;for(t=i;t--!==0;)if(!ot(r[t],n[t]))return!1;return!0}var s;if(En&&r instanceof Map&&n instanceof Map){if(r.size!==n.size)return!1;for(s=r.entries();!(t=s.next()).done;)if(!n.has(t.value[0]))return!1;for(s=r.entries();!(t=s.next()).done;)if(!ot(t.value[1],n.get(t.value[0])))return!1;return!0}if(An&&r instanceof Set&&n instanceof Set){if(r.size!==n.size)return!1;for(s=r.entries();!(t=s.next()).done;)if(!n.has(t.value[0]))return!1;return!0}if(xn&&ArrayBuffer.isView(r)&&ArrayBuffer.isView(n)){if(i=r.length,i!=n.length)return!1;for(t=i;t--!==0;)if(r[t]!==n[t])return!1;return!0}if(r.constructor===RegExp)return r.source===n.source&&r.flags===n.flags;if(r.valueOf!==Object.prototype.valueOf&&typeof r.valueOf=="function"&&typeof n.valueOf=="function")return r.valueOf()===n.valueOf();if(r.toString!==Object.prototype.toString&&typeof r.toString=="function"&&typeof n.toString=="function")return r.toString()===n.toString();if(a=Object.keys(r),i=a.length,i!==Object.keys(n).length)return!1;for(t=i;t--!==0;)if(!Object.prototype.hasOwnProperty.call(n,a[t]))return!1;if(Pn&&r instanceof Element)return!1;for(t=i;t--!==0;)if(!((a[t]==="_owner"||a[t]==="__v"||a[t]==="__o")&&r.$$typeof)&&!ot(r[a[t]],n[a[t]]))return!1;return!0}return r!==r&&n!==n}var Rn=function(n,i){try{return ot(n,i)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}};const In=gt(Rn);var Ln=function(r,n,i,t,a,s,o,c){if(!r){var u;if(n===void 0)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var d=[i,t,a,s,o,c],p=0;u=new Error(n.replace(/%s/g,function(){return d[p++]})),u.name="Invariant Violation"}throw u.framesToPop=1,u}},Mn=Ln;const tr=gt(Mn);var jn=function(n,i,t,a){var s=t?t.call(a,n,i):void 0;if(s!==void 0)return!!s;if(n===i)return!0;if(typeof n!="object"||!n||typeof i!="object"||!i)return!1;var o=Object.keys(n),c=Object.keys(i);if(o.length!==c.length)return!1;for(var u=Object.prototype.hasOwnProperty.bind(i),d=0;d<o.length;d++){var p=o[d];if(!u(p))return!1;var h=n[p],v=i[p];if(s=t?t.call(a,h,v,p):void 0,s===!1||s===void 0&&h!==v)return!1}return!0};const Dn=gt(jn);function pe(){return pe=Object.assign||function(r){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var t in i)Object.prototype.hasOwnProperty.call(i,t)&&(r[t]=i[t])}return r},pe.apply(this,arguments)}function Ht(r,n){r.prototype=Object.create(n.prototype),r.prototype.constructor=r,Rt(r,n)}function Rt(r,n){return Rt=Object.setPrototypeOf||function(i,t){return i.__proto__=t,i},Rt(r,n)}function rr(r,n){if(r==null)return{};var i,t,a={},s=Object.keys(r);for(t=0;t<s.length;t++)n.indexOf(i=s[t])>=0||(a[i]=r[i]);return a}var Q={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title",FRAGMENT:"Symbol(react.fragment)"},Fn={rel:["amphtml","canonical","alternate"]},Hn={type:["application/ld+json"]},$n={charset:"",name:["robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},nr=Object.keys(Q).map(function(r){return Q[r]}),pt={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},qn=Object.keys(pt).reduce(function(r,n){return r[pt[n]]=n,r},{}),ze=function(r,n){for(var i=r.length-1;i>=0;i-=1){var t=r[i];if(Object.prototype.hasOwnProperty.call(t,n))return t[n]}return null},Qn=function(r){var n=ze(r,Q.TITLE),i=ze(r,"titleTemplate");if(Array.isArray(n)&&(n=n.join("")),i&&n)return i.replace(/%s/g,function(){return n});var t=ze(r,"defaultTitle");return n||t||void 0},Un=function(r){return ze(r,"onChangeClientState")||function(){}},kt=function(r,n){return n.filter(function(i){return i[r]!==void 0}).map(function(i){return i[r]}).reduce(function(i,t){return pe({},i,t)},{})},zn=function(r,n){return n.filter(function(i){return i[Q.BASE]!==void 0}).map(function(i){return i[Q.BASE]}).reverse().reduce(function(i,t){if(!i.length)for(var a=Object.keys(t),s=0;s<a.length;s+=1){var o=a[s].toLowerCase();if(r.indexOf(o)!==-1&&t[o])return i.concat(t)}return i},[])},Ye=function(r,n,i){var t={};return i.filter(function(a){return!!Array.isArray(a[r])||(a[r]!==void 0&&console&&typeof console.warn=="function"&&console.warn("Helmet: "+r+' should be of type "Array". Instead found type "'+typeof a[r]+'"'),!1)}).map(function(a){return a[r]}).reverse().reduce(function(a,s){var o={};s.filter(function(h){for(var v,b=Object.keys(h),m=0;m<b.length;m+=1){var w=b[m],P=w.toLowerCase();n.indexOf(P)===-1||v==="rel"&&h[v].toLowerCase()==="canonical"||P==="rel"&&h[P].toLowerCase()==="stylesheet"||(v=P),n.indexOf(w)===-1||w!=="innerHTML"&&w!=="cssText"&&w!=="itemprop"||(v=w)}if(!v||!h[v])return!1;var j=h[v].toLowerCase();return t[v]||(t[v]={}),o[v]||(o[v]={}),!t[v][j]&&(o[v][j]=!0,!0)}).reverse().forEach(function(h){return a.push(h)});for(var c=Object.keys(o),u=0;u<c.length;u+=1){var d=c[u],p=pe({},t[d],o[d]);t[d]=p}return a},[]).reverse()},Bn=function(r,n){if(Array.isArray(r)&&r.length){for(var i=0;i<r.length;i+=1)if(r[i][n])return!0}return!1},Sr=function(r){return Array.isArray(r)?r.join(""):r},St=function(r,n){return Array.isArray(r)?r.reduce(function(i,t){return function(a,s){for(var o=Object.keys(a),c=0;c<o.length;c+=1)if(s[o[c]]&&s[o[c]].includes(a[o[c]]))return!0;return!1}(t,n)?i.priority.push(t):i.default.push(t),i},{priority:[],default:[]}):{default:r}},ar=function(r,n){var i;return pe({},r,((i={})[n]=void 0,i))},Kn=[Q.NOSCRIPT,Q.SCRIPT,Q.STYLE],Ct=function(r,n){return n===void 0&&(n=!0),n===!1?String(r):String(r).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},ir=function(r){return Object.keys(r).reduce(function(n,i){var t=r[i]!==void 0?i+'="'+r[i]+'"':""+i;return n?n+" "+t:t},"")},sr=function(r,n){return n===void 0&&(n={}),Object.keys(r).reduce(function(i,t){return i[pt[t]||t]=r[t],i},n)},ct=function(r,n){return n.map(function(i,t){var a,s=((a={key:t})["data-rh"]=!0,a);return Object.keys(i).forEach(function(o){var c=pt[o]||o;c==="innerHTML"||c==="cssText"?s.dangerouslySetInnerHTML={__html:i.innerHTML||i.cssText}:s[c]=i[o]}),oe.createElement(r,s)})},Ee=function(r,n,i){switch(r){case Q.TITLE:return{toComponent:function(){return a=n.titleAttributes,(s={key:t=n.title})["data-rh"]=!0,o=sr(a,s),[oe.createElement(Q.TITLE,o,t)];var t,a,s,o},toString:function(){return function(t,a,s,o){var c=ir(s),u=Sr(a);return c?"<"+t+' data-rh="true" '+c+">"+Ct(u,o)+"</"+t+">":"<"+t+' data-rh="true">'+Ct(u,o)+"</"+t+">"}(r,n.title,n.titleAttributes,i)}};case"bodyAttributes":case"htmlAttributes":return{toComponent:function(){return sr(n)},toString:function(){return ir(n)}};default:return{toComponent:function(){return ct(r,n)},toString:function(){return function(t,a,s){return a.reduce(function(o,c){var u=Object.keys(c).filter(function(h){return!(h==="innerHTML"||h==="cssText")}).reduce(function(h,v){var b=c[v]===void 0?v:v+'="'+Ct(c[v],s)+'"';return h?h+" "+b:b},""),d=c.innerHTML||c.cssText||"",p=Kn.indexOf(t)===-1;return o+"<"+t+' data-rh="true" '+u+(p?"/>":">"+d+"</"+t+">")},"")}(r,n,i)}}}},It=function(r){var n=r.baseTag,i=r.bodyAttributes,t=r.encode,a=r.htmlAttributes,s=r.noscriptTags,o=r.styleTags,c=r.title,u=c===void 0?"":c,d=r.titleAttributes,p=r.linkTags,h=r.metaTags,v=r.scriptTags,b={toComponent:function(){},toString:function(){return""}};if(r.prioritizeSeoTags){var m=function(w){var P=w.linkTags,j=w.scriptTags,H=w.encode,$=St(w.metaTags,$n),z=St(P,Fn),L=St(j,Hn);return{priorityMethods:{toComponent:function(){return[].concat(ct(Q.META,$.priority),ct(Q.LINK,z.priority),ct(Q.SCRIPT,L.priority))},toString:function(){return Ee(Q.META,$.priority,H)+" "+Ee(Q.LINK,z.priority,H)+" "+Ee(Q.SCRIPT,L.priority,H)}},metaTags:$.default,linkTags:z.default,scriptTags:L.default}}(r);b=m.priorityMethods,p=m.linkTags,h=m.metaTags,v=m.scriptTags}return{priority:b,base:Ee(Q.BASE,n,t),bodyAttributes:Ee("bodyAttributes",i,t),htmlAttributes:Ee("htmlAttributes",a,t),link:Ee(Q.LINK,p,t),meta:Ee(Q.META,h,t),noscript:Ee(Q.NOSCRIPT,s,t),script:Ee(Q.SCRIPT,v,t),style:Ee(Q.STYLE,o,t),title:Ee(Q.TITLE,{title:u,titleAttributes:d},t)}},nt=[],Lt=function(r,n){var i=this;n===void 0&&(n=typeof document<"u"),this.instances=[],this.value={setHelmet:function(t){i.context.helmet=t},helmetInstances:{get:function(){return i.canUseDOM?nt:i.instances},add:function(t){(i.canUseDOM?nt:i.instances).push(t)},remove:function(t){var a=(i.canUseDOM?nt:i.instances).indexOf(t);(i.canUseDOM?nt:i.instances).splice(a,1)}}},this.context=r,this.canUseDOM=n,n||(r.helmet=It({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))},Cr=oe.createContext({}),Vn=I.shape({setHelmet:I.func,helmetInstances:I.shape({get:I.func,add:I.func,remove:I.func})}),Yn=typeof document<"u",Ue=function(r){function n(i){var t;return(t=r.call(this,i)||this).helmetData=new Lt(t.props.context,n.canUseDOM),t}return Ht(n,r),n.prototype.render=function(){return oe.createElement(Cr.Provider,{value:this.helmetData.value},this.props.children)},n}(T.Component);Ue.canUseDOM=Yn,Ue.propTypes={context:I.shape({helmet:I.shape()}),children:I.node.isRequired},Ue.defaultProps={context:{}},Ue.displayName="HelmetProvider";var Qe=function(r,n){var i,t=document.head||document.querySelector(Q.HEAD),a=t.querySelectorAll(r+"[data-rh]"),s=[].slice.call(a),o=[];return n&&n.length&&n.forEach(function(c){var u=document.createElement(r);for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(d==="innerHTML"?u.innerHTML=c.innerHTML:d==="cssText"?u.styleSheet?u.styleSheet.cssText=c.cssText:u.appendChild(document.createTextNode(c.cssText)):u.setAttribute(d,c[d]===void 0?"":c[d]));u.setAttribute("data-rh","true"),s.some(function(p,h){return i=h,u.isEqualNode(p)})?s.splice(i,1):o.push(u)}),s.forEach(function(c){return c.parentNode.removeChild(c)}),o.forEach(function(c){return t.appendChild(c)}),{oldTags:s,newTags:o}},_t=function(r,n){var i=document.getElementsByTagName(r)[0];if(i){for(var t=i.getAttribute("data-rh"),a=t?t.split(","):[],s=[].concat(a),o=Object.keys(n),c=0;c<o.length;c+=1){var u=o[c],d=n[u]||"";i.getAttribute(u)!==d&&i.setAttribute(u,d),a.indexOf(u)===-1&&a.push(u);var p=s.indexOf(u);p!==-1&&s.splice(p,1)}for(var h=s.length-1;h>=0;h-=1)i.removeAttribute(s[h]);a.length===s.length?i.removeAttribute("data-rh"):i.getAttribute("data-rh")!==o.join(",")&&i.setAttribute("data-rh",o.join(","))}},or=function(r,n){var i=r.baseTag,t=r.htmlAttributes,a=r.linkTags,s=r.metaTags,o=r.noscriptTags,c=r.onChangeClientState,u=r.scriptTags,d=r.styleTags,p=r.title,h=r.titleAttributes;_t(Q.BODY,r.bodyAttributes),_t(Q.HTML,t),function(w,P){w!==void 0&&document.title!==w&&(document.title=Sr(w)),_t(Q.TITLE,P)}(p,h);var v={baseTag:Qe(Q.BASE,i),linkTags:Qe(Q.LINK,a),metaTags:Qe(Q.META,s),noscriptTags:Qe(Q.NOSCRIPT,o),scriptTags:Qe(Q.SCRIPT,u),styleTags:Qe(Q.STYLE,d)},b={},m={};Object.keys(v).forEach(function(w){var P=v[w],j=P.newTags,H=P.oldTags;j.length&&(b[w]=j),H.length&&(m[w]=v[w].oldTags)}),n&&n(),c(r,b,m)},We=null,yt=function(r){function n(){for(var t,a=arguments.length,s=new Array(a),o=0;o<a;o++)s[o]=arguments[o];return(t=r.call.apply(r,[this].concat(s))||this).rendered=!1,t}Ht(n,r);var i=n.prototype;return i.shouldComponentUpdate=function(t){return!Dn(t,this.props)},i.componentDidUpdate=function(){this.emitChange()},i.componentWillUnmount=function(){this.props.context.helmetInstances.remove(this),this.emitChange()},i.emitChange=function(){var t,a,s=this.props.context,o=s.setHelmet,c=null,u=(t=s.helmetInstances.get().map(function(d){var p=pe({},d.props);return delete p.context,p}),{baseTag:zn(["href"],t),bodyAttributes:kt("bodyAttributes",t),defer:ze(t,"defer"),encode:ze(t,"encodeSpecialCharacters"),htmlAttributes:kt("htmlAttributes",t),linkTags:Ye(Q.LINK,["rel","href"],t),metaTags:Ye(Q.META,["name","charset","http-equiv","property","itemprop"],t),noscriptTags:Ye(Q.NOSCRIPT,["innerHTML"],t),onChangeClientState:Un(t),scriptTags:Ye(Q.SCRIPT,["src","innerHTML"],t),styleTags:Ye(Q.STYLE,["cssText"],t),title:Qn(t),titleAttributes:kt("titleAttributes",t),prioritizeSeoTags:Bn(t,"prioritizeSeoTags")});Ue.canUseDOM?(a=u,We&&cancelAnimationFrame(We),a.defer?We=requestAnimationFrame(function(){or(a,function(){We=null})}):(or(a),We=null)):It&&(c=It(u)),o(c)},i.init=function(){this.rendered||(this.rendered=!0,this.props.context.helmetInstances.add(this),this.emitChange())},i.render=function(){return this.init(),null},n}(T.Component);yt.propTypes={context:Vn.isRequired},yt.displayName="HelmetDispatcher";var Wn=["children"],Gn=["children"],Se=function(r){function n(){return r.apply(this,arguments)||this}Ht(n,r);var i=n.prototype;return i.shouldComponentUpdate=function(t){return!In(ar(this.props,"helmetData"),ar(t,"helmetData"))},i.mapNestedChildrenToProps=function(t,a){if(!a)return null;switch(t.type){case Q.SCRIPT:case Q.NOSCRIPT:return{innerHTML:a};case Q.STYLE:return{cssText:a};default:throw new Error("<"+t.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")}},i.flattenArrayTypeChildren=function(t){var a,s=t.child,o=t.arrayTypeChildren;return pe({},o,((a={})[s.type]=[].concat(o[s.type]||[],[pe({},t.newChildProps,this.mapNestedChildrenToProps(s,t.nestedChildren))]),a))},i.mapObjectTypeChildren=function(t){var a,s,o=t.child,c=t.newProps,u=t.newChildProps,d=t.nestedChildren;switch(o.type){case Q.TITLE:return pe({},c,((a={})[o.type]=d,a.titleAttributes=pe({},u),a));case Q.BODY:return pe({},c,{bodyAttributes:pe({},u)});case Q.HTML:return pe({},c,{htmlAttributes:pe({},u)});default:return pe({},c,((s={})[o.type]=pe({},u),s))}},i.mapArrayTypeChildrenToProps=function(t,a){var s=pe({},a);return Object.keys(t).forEach(function(o){var c;s=pe({},s,((c={})[o]=t[o],c))}),s},i.warnOnInvalidChildren=function(t,a){return tr(nr.some(function(s){return t.type===s}),typeof t.type=="function"?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":"Only elements types "+nr.join(", ")+" are allowed. Helmet does not support rendering <"+t.type+"> elements. Refer to our API for more information."),tr(!a||typeof a=="string"||Array.isArray(a)&&!a.some(function(s){return typeof s!="string"}),"Helmet expects a string as a child of <"+t.type+">. Did you forget to wrap your children in braces? ( <"+t.type+">{``}</"+t.type+"> ) Refer to our API for more information."),!0},i.mapChildrenToProps=function(t,a){var s=this,o={};return oe.Children.forEach(t,function(c){if(c&&c.props){var u=c.props,d=u.children,p=rr(u,Wn),h=Object.keys(p).reduce(function(b,m){return b[qn[m]||m]=p[m],b},{}),v=c.type;switch(typeof v=="symbol"?v=v.toString():s.warnOnInvalidChildren(c,d),v){case Q.FRAGMENT:a=s.mapChildrenToProps(d,a);break;case Q.LINK:case Q.META:case Q.NOSCRIPT:case Q.SCRIPT:case Q.STYLE:o=s.flattenArrayTypeChildren({child:c,arrayTypeChildren:o,newChildProps:h,nestedChildren:d});break;default:a=s.mapObjectTypeChildren({child:c,newProps:a,newChildProps:h,nestedChildren:d})}}}),this.mapArrayTypeChildrenToProps(o,a)},i.render=function(){var t=this.props,a=t.children,s=rr(t,Gn),o=pe({},s),c=s.helmetData;return a&&(o=this.mapChildrenToProps(a,o)),!c||c instanceof Lt||(c=new Lt(c.context,c.instances)),c?oe.createElement(yt,pe({},o,{context:c.value,helmetData:void 0})):oe.createElement(Cr.Consumer,null,function(u){return oe.createElement(yt,pe({},o,{context:u}))})},n}(T.Component);Se.propTypes={base:I.object,bodyAttributes:I.object,children:I.oneOfType([I.arrayOf(I.node),I.node]),defaultTitle:I.string,defer:I.bool,encodeSpecialCharacters:I.bool,htmlAttributes:I.object,link:I.arrayOf(I.object),meta:I.arrayOf(I.object),noscript:I.arrayOf(I.object),onChangeClientState:I.func,script:I.arrayOf(I.object),style:I.arrayOf(I.object),title:I.string,titleAttributes:I.object,titleTemplate:I.string,prioritizeSeoTags:I.bool,helmetData:I.object},Se.defaultProps={defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1},Se.displayName="Helmet";const Xn="https://metaanalizhaber.com",Ae=$r.create({baseURL:Xn,timeout:15e3,headers:{"Content-Type":"application/json"}}),Ie=!1;Ae.interceptors.request.use(r=>r,r=>Promise.reject(r));Ae.interceptors.response.use(r=>r.data,r=>(console.error("API Error:",r),Promise.reject(r)));const fe={getNews:async(r={})=>{try{const n=Ie?"/backend/mock_api.php":"/backend/api.php",i={sort:"pub_date",order:"DESC",...r};return await Ae.get(n,{params:{action:"get_news",...i}})}catch(n){return console.error("Error fetching news:",n),{success:!1,data:[],total:0,error:n.message}}},getNewsBySlug:async r=>{try{const n=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(n,{params:{action:"get_news_by_slug",slug:r}})}catch(n){throw console.error("Error fetching news by slug:",n),n}},getNewsDetail:async r=>{try{const n=Ie?"/backend/mock_api.php":"/backend/api.php";return/^\d{8}[A-Z]{2}\d{6}$/.test(r)?await Ae.get(n,{params:{action:"get_news_detail",haber_kodu:r}}):await Ae.get(n,{params:{action:"get_news_by_slug",slug:r}})}catch(n){throw console.error("API Error in getNewsDetail:",n),n}},getCategories:async()=>{try{const r=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(r,{params:{action:"get_categories"}})}catch(r){throw console.error("Error fetching categories:",r),r}},getCities:async()=>{try{const r=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(r,{params:{action:"get_cities"}})}catch(r){throw console.error("Error fetching cities:",r),r}},getWorldNews:async(r={})=>{try{const n=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(n,{params:{action:"get_world_news",...r}})}catch(n){throw console.error("Error fetching world news:",n),n}},getVideoNews:async(r={})=>{try{const n=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(n,{params:{action:"get_video_news",...r}})}catch(n){throw console.error("Error fetching video news:",n),n}},searchNews:async(r,n={})=>{try{const i=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(i,{params:{action:"search_news",q:r,...n}})}catch(i){throw console.error("Error searching news:",i),i}},getCurrencyRates:async()=>{try{const r=Ie?"/backend/mock_api.php":"/backend/api.php";return await Ae.get(r,{params:{action:"get_currency_rates"}})}catch(r){throw console.error("Error fetching currency rates:",r),r}}},ke=r=>{if(!r)return"";const n=r.replace(/<[^>]*>/g,""),i=document.createElement("textarea");return i.innerHTML=n,i.value.replace(/\s+/g," ").trim()},Tt=r=>{if(!r)return"";let n=r;const i=document.createElement("textarea");return i.innerHTML=n,n=i.value,n=n.replace(/<\/p>/gi,`

`),n=n.replace(/<p[^>]*>/gi,""),n=n.replace(/<br\s*\/?>/gi,`
`),n=n.replace(/<[^>]*>/g,""),n=n.replace(/[ \t]+/g," "),n=n.replace(/\n\s*\n\s*\n+/g,`

`),n=n.replace(/\n\s+/g,`
`),n=n.replace(/\s+\n/g,`
`),n.trim()},_e=r=>{if(!r)return"";const n=document.createElement("textarea");return n.innerHTML=r,n.value},Jn=()=>{const[r,n]=T.useState(0),i=o=>new Date(o).toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"}),{data:t,isLoading:a}=ye("breaking-news",()=>fe.getNews({breaking:"true",limit:50}),{refetchInterval:6e4,select:o=>{if(o!=null&&o.success&&(o!=null&&o.data)){const c=Array.isArray(o.data)?o.data:[],u=new Date,d=new Date(u.getTime()-6*60*60*1e3);return c.filter(h=>{const v=new Date(h.pub_date),b=h.son_dakika==="Evet",m=v>=d;return b&&m}).sort((h,v)=>new Date(v.pub_date)-new Date(h.pub_date)).slice(0,10)}return[]}});if(T.useEffect(()=>{if(t&&t.length>1){const o=setInterval(()=>{n(c=>(c+1)%t.length)},4e3);return()=>clearInterval(o)}},[t==null?void 0:t.length]),a||!t||t.length===0)return null;const s=t[r];return e("div",{className:"breaking-news-bar",children:e("div",{className:"container",children:l("div",{className:"breaking-news-content",children:[e("div",{className:"breaking-label",children:e("span",{className:"breaking-text",children:"Son Dakika"})}),e("div",{className:"breaking-news-slider",children:s?l(V,{to:`/haber/${s.slug||s.haber_kodu}`,className:"breaking-news-item",children:[e("h4",{className:"breaking-title",children:ke(s.title).replace(/\n/g," ").trim()}),e("span",{className:"breaking-time",children:i(s.pub_date)})]}):e("div",{className:"breaking-news-item",children:e("h4",{className:"breaking-title",children:"Haber yükleniyor..."})})}),t.length>1&&e("div",{className:"breaking-indicators",children:t.map((o,c)=>e("button",{className:`indicator ${c===r?"active":""}`,onClick:()=>n(c),"aria-label":`Son dakika haberi ${c+1}`},`indicator-${o.haber_kodu||c}`))})]})})})};const Zn=()=>{const[r,n]=T.useState([]),[i,t]=T.useState(!0),[a,s]=T.useState(null);return T.useEffect(()=>{const o=async()=>{try{t(!0);const u=await fe.getCurrencyRates();if(u.success&&Array.isArray(u.data))n(u.data);else throw new Error("Döviz verileri işlenemedi");s(null)}catch(u){console.error("Döviz kurları hatası:",u),n([{code:"USD",name:"Dolar",symbol:"$",rate:"34.25",change:"+0.15%",isPositive:!0},{code:"EUR",name:"Euro",symbol:"€",rate:"37.18",change:"-0.08%",isPositive:!1},{code:"GBP",name:"Sterlin",symbol:"£",rate:"43.52",change:"+0.22%",isPositive:!0}]),s(null)}finally{t(!1)}};o();const c=setInterval(o,10*60*1e3);return()=>clearInterval(c)},[]),i?e("div",{className:"currency-ticker",children:e("div",{className:"currency-container",children:e("div",{className:"currency-loading",children:e("span",{children:"Döviz kurları yükleniyor..."})})})}):a?null:e("div",{className:"currency-ticker",children:l("div",{className:"currency-container",children:[e("div",{className:"currency-label",children:e("span",{children:"CANLI DÖVİZ"})}),e("div",{className:"currency-scroll",children:l("div",{className:"currency-items",children:[r.map((o,c)=>l("div",{className:"currency-item",children:[e("span",{className:"currency-symbol",children:o.symbol}),e("span",{className:"currency-name",children:o.name}),l("span",{className:"currency-rate",children:["₺",o.rate]}),e("span",{className:`currency-change ${o.isPositive?"positive":"negative"}`,children:o.change})]},`${o.code}-${c}`)),r.map((o,c)=>l("div",{className:"currency-item",children:[e("span",{className:"currency-symbol",children:o.symbol}),e("span",{className:"currency-name",children:o.name}),l("span",{className:"currency-rate",children:["₺",o.rate]}),e("span",{className:`currency-change ${o.isPositive?"positive":"negative"}`,children:o.change})]},`${o.code}-repeat-${c}`))]})})]})})},cr=r=>{if(!r)return"";const n={ç:"c",Ç:"c",ğ:"g",Ğ:"g",ı:"i",I:"i",İ:"i",ö:"o",Ö:"o",ş:"s",Ş:"s",ü:"u",Ü:"u"};return r.split("").map(i=>n[i]||i).join("").toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()},ea=r=>({asayis:"ASAYIŞ",genel:"GENEL","haberde-insan":"HABERDE İNSAN",cevre:"ÇEVRE",politika:"POLİTİKA",spor:"SPOR","kultur-sanat":"KÜLTÜR SANAT",egitim:"EĞİTİM",ekonomi:"EKONOMİ",saglik:"SAĞLIK","bilim-ve-teknoloji":"BİLİM VE TEKNOLOJİ",magazin:"MAGAZİN"})[r]||r,vt=r=>r?{SAGLIK:"SAĞLIK",ASAYIS:"ASAYIŞ",CEVRE:"ÇEVRE",POLITIKA:"POLİTİKA",EGITIM:"EĞİTİM",EKONOMI:"EKONOMİ","KULTUR SANAT":"KÜLTÜR SANAT","BILIM VE TEKNOLOJI":"BİLİM VE TEKNOLOJİ",MAGAZIN:"MAGAZİN"}[r]||r:"",ta=()=>{const[r,n]=T.useState(!1),[i,t]=T.useState(!1),{data:a}=ye("categories",()=>fe.getCategories(),{staleTime:6e5}),s=a!=null&&a.success&&a.data?(()=>{const o=[...a.data];console.log("Available categories:",o.map(h=>h.name||h));const c=["GENEL","VİDEO HABERLER","DÜNYA","ASAYİŞ"],u=[],d=[];o.forEach(h=>{const v=(h.name||h).toUpperCase(),b=c.indexOf(v);b!==-1?u[b]=h:d.push(h)});const p=u.filter(Boolean);return console.log("Sorted categories:",[...p,...d].map(h=>h.name||h)),[...p,...d]})():[];return l(ie,{children:[e("header",{className:"header-main",children:l("div",{className:"container",children:[l("div",{className:"main-header",children:[l("button",{className:"hamburger-menu",onClick:()=>t(!i),"aria-label":"Menüyü aç/kapat",children:[e("span",{}),e("span",{}),e("span",{})]}),e("div",{className:"logo",children:e(V,{to:"/",children:e("img",{src:"/logo7.webp",alt:"MetaAnaliz Haber",className:"logo-image",style:{height:"50px"}})})})]}),e("nav",{className:"main-nav",children:l("div",{className:"nav-container",children:[l("ul",{className:`nav-menu ${r?"active":""}`,children:[e("li",{className:"nav-item",children:e(Ve,{to:"/kategori/genel",className:"nav-link",onClick:()=>n(!1),children:"GENEL"})}),e("li",{className:"nav-item",children:e(Ve,{to:"/video-haberler",className:"nav-link",onClick:()=>n(!1),children:"VİDEO HABERLER"})}),e("li",{className:"nav-item",children:e(Ve,{to:"/dunya-haberleri",className:"nav-link",onClick:()=>n(!1),children:"DÜNYA"})}),s.filter(o=>!["GENEL","Genel"].includes(o.name)).map(o=>e("li",{className:"nav-item",children:e(Ve,{to:`/kategori/${cr(o.name)}`,className:"nav-link",onClick:()=>n(!1),children:vt(o.name)})},o.name)),e("li",{className:"nav-item",children:e(Ve,{to:"/yerel-haberler",className:"nav-link",onClick:()=>n(!1),children:"YEREL HABERLER"})})]}),l("div",{className:"mobile-menu-toggle",onClick:()=>n(!r),"aria-label":"Menüyü aç/kapat",children:[e("span",{}),e("span",{}),e("span",{})]})]})}),e(Jn,{}),e(Zn,{})]})}),e("div",{className:`sidebar-overlay ${i?"active":""}`,onClick:()=>t(!1),children:l("div",{className:`sidebar ${i?"active":""}`,onClick:o=>o.stopPropagation(),children:[l("div",{className:"sidebar-header",children:[e("h3",{children:"Kategoriler"}),e("button",{className:"sidebar-close",onClick:()=>t(!1),children:e("i",{className:"fas fa-times"})})]}),e("div",{className:"sidebar-content",children:l("ul",{className:"sidebar-menu",children:[e("li",{children:l(V,{to:"/",onClick:()=>t(!1),children:[e("i",{className:"fas fa-home"})," Ana Sayfa"]})}),e("li",{children:e(V,{to:"/kategori/genel",onClick:()=>t(!1),children:"GENEL"})}),e("li",{children:e(V,{to:"/video-haberler",onClick:()=>t(!1),children:"VİDEO HABERLER"})}),e("li",{children:e(V,{to:"/dunya-haberleri",onClick:()=>t(!1),children:"DÜNYA"})}),s.length>0?s.filter(o=>!["GENEL","Genel"].includes(o.name)).map(o=>e("li",{children:e(V,{to:`/kategori/${cr(o.name)}`,onClick:()=>t(!1),children:vt(o.name)})},o.name)):e("li",{children:e("span",{children:"Kategoriler yükleniyor..."})}),e("li",{children:e(V,{to:"/yerel-haberler",onClick:()=>t(!1),children:"YEREL HABERLER"})})]})})]})})]})},ra=()=>{const r=new Date().getFullYear();return e("footer",{className:"footer",children:l("div",{className:"container",children:[l("div",{className:"row align-items-center",children:[e("div",{className:"col-md-8",children:l("div",{className:"footer-widget",children:[e("div",{className:"footer-logo mb-4",children:e("img",{src:"/logo7.webp",alt:"MetaAnaliz Haber",style:{height:"60px"}})}),e("p",{className:"footer-description",children:"En güncel haberleri takip edin."}),l("div",{className:"social-links",children:[e("a",{href:"https://x.com/MetaAnaliz",className:"social-link","aria-label":"X (Twitter)",target:"_blank",rel:"noopener noreferrer",children:e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})}),e("a",{href:"https://www.facebook.com/metaanalizhaber",className:"social-link","aria-label":"Facebook",target:"_blank",rel:"noopener noreferrer",children:e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),e("a",{href:"https://www.instagram.com/metaanalizhaber",className:"social-link","aria-label":"Instagram",target:"_blank",rel:"noopener noreferrer",children:e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),e("a",{href:"https://www.youtube.com/@metaanaliz",className:"social-link","aria-label":"YouTube",target:"_blank",rel:"noopener noreferrer",children:e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})})]})]})}),e("div",{className:"col-md-4",children:l("div",{className:"footer-widget footer-contact",children:[e("h4",{children:"İletişim"}),l("div",{className:"contact-info",children:[l("p",{children:[e("i",{className:"fas fa-envelope"})," <EMAIL>"]}),l("p",{children:[e("i",{className:"fas fa-phone"})," +90 (542) 380 00 50"]}),l("p",{children:[e("i",{className:"fas fa-map-marker-alt"})," Aydın, Türkiye"]})]})]})})]}),e("div",{className:"footer-bottom",children:e("div",{className:"row",children:e("div",{className:"col-12 text-center",children:l("p",{children:["© ",r," MetaAnaliz Haber. Tüm hakları saklıdır."]})})})})]})})},na=()=>{const{pathname:r}=Lr();return T.useEffect(()=>{window.scrollTo(0,0)},[r]),null},_r=T.createContext(),aa=({children:r})=>{const[n,i]=T.useState([]),[t,a]=T.useState(""),{data:s,isLoading:o}=ye("categories",fe.getCategories,{staleTime:6e5,onSuccess:h=>{h&&h.success&&h.data&&i(h.data)}}),c=T.useCallback(h=>new Date(h).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),[]),u=T.useCallback(h=>{if(!h)return"";const v=new Date;let b;if(h.includes(" ")?b=new Date(h.replace(" ","T")):b=new Date(h),isNaN(b.getTime()))return h;const m=Math.floor((v-b)/1e3);return m<0||m<60?"Az önce":m<3600?`${Math.floor(m/60)} dakika önce`:m<86400?`${Math.floor(m/3600)} saat önce`:m<604800?`${Math.floor(m/86400)} gün önce`:b.toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},[]),d=T.useCallback((h,v)=>h?h.length<=v?h:h.substr(0,v)+"...":"",[]),p=T.useMemo(()=>({categories:n,categoriesLoading:o,currentCategory:t,setCurrentCategory:a,formatDate:c,getRelativeTime:u,truncateText:d}),[n,o,t,c,u,d]);return e(_r.Provider,{value:p,children:r})},Re=()=>{const r=T.useContext(_r);if(r===void 0)throw new Error("useNews must be used within a NewsProvider");return r};const ia=()=>{const{getRelativeTime:r}=Re(),{data:n,isLoading:i,error:t}=ye("worldNews",()=>fe.getWorldNews({limit:12}),{refetchInterval:5*60*1e3,staleTime:2*60*1e3,cacheTime:10*60*1e3}),a=n!=null&&n.success?n.data:[];if(i)return e("div",{className:"world-news-section",children:l("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),l("div",{className:"world-loading",children:[e("div",{className:"loading-spinner"}),e("p",{children:"Dünya haberleri yükleniyor..."})]})]})});if(t||!a.length)return e("div",{className:"world-news-section",children:l("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),e("div",{className:"world-error",children:e("p",{children:"Dünya haberleri şu anda yüklenemiyor."})})]})});const s=a.filter(c=>c.main_image),o=a.filter(c=>!c.main_image);return e("div",{className:"world-news-section",children:l("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),l("div",{className:"world-news-layout",children:[s.length>0&&e("div",{className:"world-news-grid",children:s.slice(0,4).map((c,u)=>{var d;return e("div",{className:`world-grid-item ${u===0?"featured":""}`,children:l(V,{to:`/haber/${c.slug||c.haber_kodu}`,className:"world-news-card",children:[l("div",{className:"world-card-image",children:[e("img",{src:c.main_image,alt:c.title}),e("div",{className:"world-card-category",children:c.kategori})]}),l("div",{className:"world-card-content",children:[e("h3",{className:"world-card-title",children:c.title}),u===0&&l("p",{className:"world-card-description",children:[(d=c.description)==null?void 0:d.replace(/<[^>]*>/g,"").substring(0,120),"..."]}),l("div",{className:"world-card-meta",children:[e("span",{className:"world-card-date",children:r(c.pub_date)}),l("span",{className:"world-card-views",children:[e("i",{className:"far fa-eye"}),c.view_count||0]})]})]})]})},c.id)})}),o.length>0&&l("div",{className:"world-news-list",children:[e("h4",{className:"world-list-title",children:"Diğer Dünya Haberleri"}),o.slice(0,6).map((c,u)=>e("div",{className:"world-list-item",children:l(V,{to:`/haber/${c.slug||c.haber_kodu}`,className:"world-list-content",children:[e("span",{className:"world-list-category",children:c.kategori}),e("h4",{className:"world-list-title-text",children:c.title}),l("div",{className:"world-list-meta",children:[e("span",{className:"world-list-date",children:r(c.pub_date)}),l("span",{className:"world-list-views",children:[e("i",{className:"far fa-eye"}),c.view_count||0]})]})]})},c.id))]})]})]})})},ve=({size:r="medium",text:n="Yükleniyor..."})=>{const i={small:"spinner-border-sm",medium:"",large:"spinner-border-lg"}[r];return l("div",{className:"loading-spinner-container",children:[e("div",{className:`spinner-border text-primary ${i}`,role:"status",children:e("span",{className:"visually-hidden",children:n})}),n&&e("div",{className:"loading-text mt-2",children:n})]})};var Tr={exports:{}};(()=>{var r={296:(a,s,o)=>{var c=/^\s+|\s+$/g,u=/^[-+]0x[0-9a-f]+$/i,d=/^0b[01]+$/i,p=/^0o[0-7]+$/i,h=parseInt,v=typeof o.g=="object"&&o.g&&o.g.Object===Object&&o.g,b=typeof self=="object"&&self&&self.Object===Object&&self,m=v||b||Function("return this")(),w=Object.prototype.toString,P=Math.max,j=Math.min,H=function(){return m.Date.now()};function $(L){var D=typeof L;return!!L&&(D=="object"||D=="function")}function z(L){if(typeof L=="number")return L;if(function(q){return typeof q=="symbol"||function(K){return!!K&&typeof K=="object"}(q)&&w.call(q)=="[object Symbol]"}(L))return NaN;if($(L)){var D=typeof L.valueOf=="function"?L.valueOf():L;L=$(D)?D+"":D}if(typeof L!="string")return L===0?L:+L;L=L.replace(c,"");var ee=d.test(L);return ee||p.test(L)?h(L.slice(2),ee?2:8):u.test(L)?NaN:+L}a.exports=function(L,D,ee){var q,K,C,B,U,Z,_=0,x=!1,R=!1,W=!0;if(typeof L!="function")throw new TypeError("Expected a function");function re(y){var F=q,me=K;return q=K=void 0,_=y,B=L.apply(me,F)}function ae(y){var F=y-Z;return Z===void 0||F>=D||F<0||R&&y-_>=C}function G(){var y=H();if(ae(y))return ue(y);U=setTimeout(G,function(F){var me=D-(F-Z);return R?j(me,C-(F-_)):me}(y))}function ue(y){return U=void 0,W&&q?re(y):(q=K=void 0,B)}function de(){var y=H(),F=ae(y);if(q=arguments,K=this,Z=y,F){if(U===void 0)return function(me){return _=me,U=setTimeout(G,D),x?re(me):B}(Z);if(R)return U=setTimeout(G,D),re(Z)}return U===void 0&&(U=setTimeout(G,D)),B}return D=z(D)||0,$(ee)&&(x=!!ee.leading,C=(R="maxWait"in ee)?P(z(ee.maxWait)||0,D):C,W="trailing"in ee?!!ee.trailing:W),de.cancel=function(){U!==void 0&&clearTimeout(U),_=0,q=Z=K=U=void 0},de.flush=function(){return U===void 0?B:ue(H())},de}},96:(a,s,o)=>{var c="Expected a function",u=NaN,d="[object Symbol]",p=/^\s+|\s+$/g,h=/^[-+]0x[0-9a-f]+$/i,v=/^0b[01]+$/i,b=/^0o[0-7]+$/i,m=parseInt,w=typeof o.g=="object"&&o.g&&o.g.Object===Object&&o.g,P=typeof self=="object"&&self&&self.Object===Object&&self,j=w||P||Function("return this")(),H=Object.prototype.toString,$=Math.max,z=Math.min,L=function(){return j.Date.now()};function D(q){var K=typeof q;return!!q&&(K=="object"||K=="function")}function ee(q){if(typeof q=="number")return q;if(function(B){return typeof B=="symbol"||function(U){return!!U&&typeof U=="object"}(B)&&H.call(B)==d}(q))return u;if(D(q)){var K=typeof q.valueOf=="function"?q.valueOf():q;q=D(K)?K+"":K}if(typeof q!="string")return q===0?q:+q;q=q.replace(p,"");var C=v.test(q);return C||b.test(q)?m(q.slice(2),C?2:8):h.test(q)?u:+q}a.exports=function(q,K,C){var B=!0,U=!0;if(typeof q!="function")throw new TypeError(c);return D(C)&&(B="leading"in C?!!C.leading:B,U="trailing"in C?!!C.trailing:U),function(Z,_,x){var R,W,re,ae,G,ue,de=0,y=!1,F=!1,me=!0;if(typeof Z!="function")throw new TypeError(c);function xe(be){var Te=R,Ce=W;return R=W=void 0,de=be,ae=Z.apply(Ce,Te)}function Ze(be){var Te=be-ue;return ue===void 0||Te>=_||Te<0||F&&be-de>=re}function Me(){var be=L();if(Ze(be))return Ke(be);G=setTimeout(Me,function(Te){var Ce=_-(Te-ue);return F?z(Ce,re-(Te-de)):Ce}(be))}function Ke(be){return G=void 0,me&&R?xe(be):(R=W=void 0,ae)}function je(){var be=L(),Te=Ze(be);if(R=arguments,W=this,ue=be,Te){if(G===void 0)return function(Ce){return de=Ce,G=setTimeout(Me,_),y?xe(Ce):ae}(ue);if(F)return G=setTimeout(Me,_),xe(ue)}return G===void 0&&(G=setTimeout(Me,_)),ae}return _=ee(_)||0,D(x)&&(y=!!x.leading,re=(F="maxWait"in x)?$(ee(x.maxWait)||0,_):re,me="trailing"in x?!!x.trailing:me),je.cancel=function(){G!==void 0&&clearTimeout(G),de=0,R=ue=W=G=void 0},je.flush=function(){return G===void 0?ae:Ke(L())},je}(q,K,{leading:B,maxWait:K,trailing:U})}},703:(a,s,o)=>{var c=o(414);function u(){}function d(){}d.resetWarningCache=u,a.exports=function(){function p(b,m,w,P,j,H){if(H!==c){var $=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw $.name="Invariant Violation",$}}function h(){return p}p.isRequired=p;var v={array:p,bigint:p,bool:p,func:p,number:p,object:p,string:p,symbol:p,any:p,arrayOf:h,element:p,elementType:p,instanceOf:h,node:p,objectOf:h,oneOf:h,oneOfType:h,shape:h,exact:h,checkPropTypes:d,resetWarningCache:u};return v.PropTypes=v,v}},697:(a,s,o)=>{a.exports=o(703)()},414:a=>{a.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},n={};function i(a){var s=n[a];if(s!==void 0)return s.exports;var o=n[a]={exports:{}};return r[a](o,o.exports,i),o.exports}i.n=a=>{var s=a&&a.__esModule?()=>a.default:()=>a;return i.d(s,{a:s}),s},i.d=(a,s)=>{for(var o in s)i.o(s,o)&&!i.o(a,o)&&Object.defineProperty(a,o,{enumerable:!0,get:s[o]})},i.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),i.o=(a,s)=>Object.prototype.hasOwnProperty.call(a,s),i.r=a=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})};var t={};(()=>{i.r(t),i.d(t,{LazyLoadComponent:()=>Te,LazyLoadImage:()=>xr,trackWindowScroll:()=>ae});const a=T;var s=i.n(a),o=i(697);function c(){return typeof window<"u"&&"IntersectionObserver"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype}function u(O){return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(f){return typeof f}:function(f){return f&&typeof Symbol=="function"&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},u(O)}function d(O,f){var k=Object.keys(O);if(Object.getOwnPropertySymbols){var S=Object.getOwnPropertySymbols(O);f&&(S=S.filter(function(Y){return Object.getOwnPropertyDescriptor(O,Y).enumerable})),k.push.apply(k,S)}return k}function p(O,f,k){return(f=v(f))in O?Object.defineProperty(O,f,{value:k,enumerable:!0,configurable:!0,writable:!0}):O[f]=k,O}function h(O,f){for(var k=0;k<f.length;k++){var S=f[k];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(O,v(S.key),S)}}function v(O){var f=function(k,S){if(u(k)!=="object"||k===null)return k;var Y=k[Symbol.toPrimitive];if(Y!==void 0){var X=Y.call(k,"string");if(u(X)!=="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(k)}(O);return u(f)==="symbol"?f:String(f)}function b(O,f){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,S){return k.__proto__=S,k},b(O,f)}function m(O){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(f){return f.__proto__||Object.getPrototypeOf(f)},m(O)}var w=function(O){O.forEach(function(f){f.isIntersecting&&f.target.onVisible()})},P={},j=function(O){(function(g,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(N&&N.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),N&&b(g,N)})(ne,O);var f,k,S,Y,X=(S=ne,Y=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,N=m(S);if(Y){var M=m(this).constructor;g=Reflect.construct(N,arguments,M)}else g=N.apply(this,arguments);return function(A,E){if(E&&(u(E)==="object"||typeof E=="function"))return E;if(E!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(J){if(J===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return J}(A)}(this,g)});function ne(g){var N;if(function(A,E){if(!(A instanceof E))throw new TypeError("Cannot call a class as a function")}(this,ne),(N=X.call(this,g)).supportsObserver=!g.scrollPosition&&g.useIntersectionObserver&&c(),N.supportsObserver){var M=g.threshold;N.observer=function(A){return P[A]=P[A]||new IntersectionObserver(w,{rootMargin:A+"px"}),P[A]}(M)}return N}return f=ne,k=[{key:"componentDidMount",value:function(){this.placeholder&&this.observer&&(this.placeholder.onVisible=this.props.onVisible,this.observer.observe(this.placeholder)),this.supportsObserver||this.updateVisibility()}},{key:"componentWillUnmount",value:function(){this.observer&&this.placeholder&&this.observer.unobserve(this.placeholder)}},{key:"componentDidUpdate",value:function(){this.supportsObserver||this.updateVisibility()}},{key:"getPlaceholderBoundingBox",value:function(){var g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props.scrollPosition,N=this.placeholder.getBoundingClientRect(),M=this.placeholder.style,A=parseInt(M.getPropertyValue("margin-left"),10)||0,E=parseInt(M.getPropertyValue("margin-top"),10)||0;return{bottom:g.y+N.bottom+E,left:g.x+N.left+A,right:g.x+N.right+A,top:g.y+N.top+E}}},{key:"isPlaceholderInViewport",value:function(){if(typeof window>"u"||!this.placeholder)return!1;var g=this.props,N=g.scrollPosition,M=g.threshold,A=this.getPlaceholderBoundingBox(N),E=N.y+window.innerHeight,J=N.x,ce=N.x+window.innerWidth,le=N.y;return le-M<=A.bottom&&E+M>=A.top&&J-M<=A.right&&ce+M>=A.left}},{key:"updateVisibility",value:function(){this.isPlaceholderInViewport()&&this.props.onVisible()}},{key:"render",value:function(){var g=this,N=this.props,M=N.className,A=N.height,E=N.placeholder,J=N.style,ce=N.width;if(E&&typeof E.type!="function")return s().cloneElement(E,{ref:function(se){return g.placeholder=se}});var le=function(se){for(var Ne=1;Ne<arguments.length;Ne++){var ge=arguments[Ne]!=null?arguments[Ne]:{};Ne%2?d(Object(ge),!0).forEach(function(we){p(se,we,ge[we])}):Object.getOwnPropertyDescriptors?Object.defineProperties(se,Object.getOwnPropertyDescriptors(ge)):d(Object(ge)).forEach(function(we){Object.defineProperty(se,we,Object.getOwnPropertyDescriptor(ge,we))})}return se}({display:"inline-block"},J);return ce!==void 0&&(le.width=ce),A!==void 0&&(le.height=A),s().createElement("span",{className:M,ref:function(se){return g.placeholder=se},style:le},E)}}],k&&h(f.prototype,k),Object.defineProperty(f,"prototype",{writable:!1}),ne}(s().Component);j.propTypes={onVisible:o.PropTypes.func.isRequired,className:o.PropTypes.string,height:o.PropTypes.oneOfType([o.PropTypes.number,o.PropTypes.string]),placeholder:o.PropTypes.element,threshold:o.PropTypes.number,useIntersectionObserver:o.PropTypes.bool,scrollPosition:o.PropTypes.shape({x:o.PropTypes.number.isRequired,y:o.PropTypes.number.isRequired}),width:o.PropTypes.oneOfType([o.PropTypes.number,o.PropTypes.string])},j.defaultProps={className:"",placeholder:null,threshold:100,useIntersectionObserver:!0};const H=j;var $=i(296),z=i.n($),L=i(96),D=i.n(L),ee=function(O){var f=getComputedStyle(O,null);return f.getPropertyValue("overflow")+f.getPropertyValue("overflow-y")+f.getPropertyValue("overflow-x")};const q=function(O){if(!(O instanceof HTMLElement))return window;for(var f=O;f&&f instanceof HTMLElement;){if(/(scroll|auto)/.test(ee(f)))return f;f=f.parentNode}return window};function K(O){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(f){return typeof f}:function(f){return f&&typeof Symbol=="function"&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},K(O)}var C=["delayMethod","delayTime"];function B(){return B=Object.assign?Object.assign.bind():function(O){for(var f=1;f<arguments.length;f++){var k=arguments[f];for(var S in k)Object.prototype.hasOwnProperty.call(k,S)&&(O[S]=k[S])}return O},B.apply(this,arguments)}function U(O,f){for(var k=0;k<f.length;k++){var S=f[k];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(O,(Y=function(X,ne){if(K(X)!=="object"||X===null)return X;var g=X[Symbol.toPrimitive];if(g!==void 0){var N=g.call(X,"string");if(K(N)!=="object")return N;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(X)}(S.key),K(Y)==="symbol"?Y:String(Y)),S)}var Y}function Z(O,f){return Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,S){return k.__proto__=S,k},Z(O,f)}function _(O,f){if(f&&(K(f)==="object"||typeof f=="function"))return f;if(f!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return x(O)}function x(O){if(O===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O}function R(O){return R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(f){return f.__proto__||Object.getPrototypeOf(f)},R(O)}var W=function(){return typeof window>"u"?0:window.scrollX||window.pageXOffset},re=function(){return typeof window>"u"?0:window.scrollY||window.pageYOffset};const ae=function(O){var f=function(k){(function(M,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function");M.prototype=Object.create(A&&A.prototype,{constructor:{value:M,writable:!0,configurable:!0}}),Object.defineProperty(M,"prototype",{writable:!1}),A&&Z(M,A)})(N,k);var S,Y,X,ne,g=(X=N,ne=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var M,A=R(X);if(ne){var E=R(this).constructor;M=Reflect.construct(A,arguments,E)}else M=A.apply(this,arguments);return _(this,M)});function N(M){var A;if(function(J,ce){if(!(J instanceof ce))throw new TypeError("Cannot call a class as a function")}(this,N),(A=g.call(this,M)).useIntersectionObserver=M.useIntersectionObserver&&c(),A.useIntersectionObserver)return _(A);var E=A.onChangeScroll.bind(x(A));return M.delayMethod==="debounce"?A.delayedScroll=z()(E,M.delayTime):M.delayMethod==="throttle"&&(A.delayedScroll=D()(E,M.delayTime)),A.state={scrollPosition:{x:W(),y:re()}},A.baseComponentRef=s().createRef(),A}return S=N,(Y=[{key:"componentDidMount",value:function(){this.addListeners()}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"componentDidUpdate",value:function(){typeof window>"u"||this.useIntersectionObserver||q(this.baseComponentRef.current)!==this.scrollElement&&(this.removeListeners(),this.addListeners())}},{key:"addListeners",value:function(){typeof window>"u"||this.useIntersectionObserver||(this.scrollElement=q(this.baseComponentRef.current),this.scrollElement.addEventListener("scroll",this.delayedScroll,{passive:!0}),window.addEventListener("resize",this.delayedScroll,{passive:!0}),this.scrollElement!==window&&window.addEventListener("scroll",this.delayedScroll,{passive:!0}))}},{key:"removeListeners",value:function(){typeof window>"u"||this.useIntersectionObserver||(this.scrollElement.removeEventListener("scroll",this.delayedScroll),window.removeEventListener("resize",this.delayedScroll),this.scrollElement!==window&&window.removeEventListener("scroll",this.delayedScroll))}},{key:"onChangeScroll",value:function(){this.useIntersectionObserver||this.setState({scrollPosition:{x:W(),y:re()}})}},{key:"render",value:function(){var M=this.props,A=(M.delayMethod,M.delayTime,function(J,ce){if(J==null)return{};var le,se,Ne=function(we,qe){if(we==null)return{};var He,rt,zt={},Bt=Object.keys(we);for(rt=0;rt<Bt.length;rt++)He=Bt[rt],qe.indexOf(He)>=0||(zt[He]=we[He]);return zt}(J,ce);if(Object.getOwnPropertySymbols){var ge=Object.getOwnPropertySymbols(J);for(se=0;se<ge.length;se++)le=ge[se],ce.indexOf(le)>=0||Object.prototype.propertyIsEnumerable.call(J,le)&&(Ne[le]=J[le])}return Ne}(M,C)),E=this.useIntersectionObserver?null:this.state.scrollPosition;return s().createElement(O,B({forwardRef:this.baseComponentRef,scrollPosition:E},A))}}])&&U(S.prototype,Y),Object.defineProperty(S,"prototype",{writable:!1}),N}(s().Component);return f.propTypes={delayMethod:o.PropTypes.oneOf(["debounce","throttle"]),delayTime:o.PropTypes.number,useIntersectionObserver:o.PropTypes.bool},f.defaultProps={delayMethod:"throttle",delayTime:300,useIntersectionObserver:!0},f};function G(O){return G=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(f){return typeof f}:function(f){return f&&typeof Symbol=="function"&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},G(O)}function ue(O,f){for(var k=0;k<f.length;k++){var S=f[k];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(O,(Y=function(X,ne){if(G(X)!=="object"||X===null)return X;var g=X[Symbol.toPrimitive];if(g!==void 0){var N=g.call(X,"string");if(G(N)!=="object")return N;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(X)}(S.key),G(Y)==="symbol"?Y:String(Y)),S)}var Y}function de(O,f){return de=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,S){return k.__proto__=S,k},de(O,f)}function y(O){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(f){return f.__proto__||Object.getPrototypeOf(f)},y(O)}var F=function(O){(function(g,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(N&&N.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),N&&de(g,N)})(ne,O);var f,k,S,Y,X=(S=ne,Y=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,N=y(S);if(Y){var M=y(this).constructor;g=Reflect.construct(N,arguments,M)}else g=N.apply(this,arguments);return function(A,E){if(E&&(G(E)==="object"||typeof E=="function"))return E;if(E!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(J){if(J===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return J}(A)}(this,g)});function ne(g){return function(N,M){if(!(N instanceof M))throw new TypeError("Cannot call a class as a function")}(this,ne),X.call(this,g)}return f=ne,(k=[{key:"render",value:function(){return s().createElement(H,this.props)}}])&&ue(f.prototype,k),Object.defineProperty(f,"prototype",{writable:!1}),ne}(s().Component);const me=ae(F);function xe(O){return xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(f){return typeof f}:function(f){return f&&typeof Symbol=="function"&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},xe(O)}function Ze(O,f){for(var k=0;k<f.length;k++){var S=f[k];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(O,(Y=function(X,ne){if(xe(X)!=="object"||X===null)return X;var g=X[Symbol.toPrimitive];if(g!==void 0){var N=g.call(X,"string");if(xe(N)!=="object")return N;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(X)}(S.key),xe(Y)==="symbol"?Y:String(Y)),S)}var Y}function Me(O,f){return Me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,S){return k.__proto__=S,k},Me(O,f)}function Ke(O){if(O===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O}function je(O){return je=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(f){return f.__proto__||Object.getPrototypeOf(f)},je(O)}var be=function(O){(function(g,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(N&&N.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),N&&Me(g,N)})(ne,O);var f,k,S,Y,X=(S=ne,Y=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,N=je(S);if(Y){var M=je(this).constructor;g=Reflect.construct(N,arguments,M)}else g=N.apply(this,arguments);return function(A,E){if(E&&(xe(E)==="object"||typeof E=="function"))return E;if(E!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ke(A)}(this,g)});function ne(g){var N;(function(ce,le){if(!(ce instanceof le))throw new TypeError("Cannot call a class as a function")})(this,ne),N=X.call(this,g);var M=g.afterLoad,A=g.beforeLoad,E=g.scrollPosition,J=g.visibleByDefault;return N.state={visible:J},J&&(A(),M()),N.onVisible=N.onVisible.bind(Ke(N)),N.isScrollTracked=!!(E&&Number.isFinite(E.x)&&E.x>=0&&Number.isFinite(E.y)&&E.y>=0),N}return f=ne,(k=[{key:"componentDidUpdate",value:function(g,N){N.visible!==this.state.visible&&this.props.afterLoad()}},{key:"onVisible",value:function(){this.props.beforeLoad(),this.setState({visible:!0})}},{key:"render",value:function(){if(this.state.visible)return this.props.children;var g=this.props,N=g.className,M=g.delayMethod,A=g.delayTime,E=g.height,J=g.placeholder,ce=g.scrollPosition,le=g.style,se=g.threshold,Ne=g.useIntersectionObserver,ge=g.width;return this.isScrollTracked||Ne&&c()?s().createElement(H,{className:N,height:E,onVisible:this.onVisible,placeholder:J,scrollPosition:ce,style:le,threshold:se,useIntersectionObserver:Ne,width:ge}):s().createElement(me,{className:N,delayMethod:M,delayTime:A,height:E,onVisible:this.onVisible,placeholder:J,style:le,threshold:se,width:ge})}}])&&Ze(f.prototype,k),Object.defineProperty(f,"prototype",{writable:!1}),ne}(s().Component);be.propTypes={afterLoad:o.PropTypes.func,beforeLoad:o.PropTypes.func,useIntersectionObserver:o.PropTypes.bool,visibleByDefault:o.PropTypes.bool},be.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},useIntersectionObserver:!0,visibleByDefault:!1};const Te=be;function Ce(O){return Ce=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(f){return typeof f}:function(f){return f&&typeof Symbol=="function"&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},Ce(O)}var Pr=["afterLoad","beforeLoad","delayMethod","delayTime","effect","placeholder","placeholderSrc","scrollPosition","threshold","useIntersectionObserver","visibleByDefault","wrapperClassName","wrapperProps"];function qt(O,f){var k=Object.keys(O);if(Object.getOwnPropertySymbols){var S=Object.getOwnPropertySymbols(O);f&&(S=S.filter(function(Y){return Object.getOwnPropertyDescriptor(O,Y).enumerable})),k.push.apply(k,S)}return k}function Qt(O){for(var f=1;f<arguments.length;f++){var k=arguments[f]!=null?arguments[f]:{};f%2?qt(Object(k),!0).forEach(function(S){Er(O,S,k[S])}):Object.getOwnPropertyDescriptors?Object.defineProperties(O,Object.getOwnPropertyDescriptors(k)):qt(Object(k)).forEach(function(S){Object.defineProperty(O,S,Object.getOwnPropertyDescriptor(k,S))})}return O}function Er(O,f,k){return(f=Ut(f))in O?Object.defineProperty(O,f,{value:k,enumerable:!0,configurable:!0,writable:!0}):O[f]=k,O}function et(){return et=Object.assign?Object.assign.bind():function(O){for(var f=1;f<arguments.length;f++){var k=arguments[f];for(var S in k)Object.prototype.hasOwnProperty.call(k,S)&&(O[S]=k[S])}return O},et.apply(this,arguments)}function Ar(O,f){for(var k=0;k<f.length;k++){var S=f[k];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(O,Ut(S.key),S)}}function Ut(O){var f=function(k,S){if(Ce(k)!=="object"||k===null)return k;var Y=k[Symbol.toPrimitive];if(Y!==void 0){var X=Y.call(k,"string");if(Ce(X)!=="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(k)}(O);return Ce(f)==="symbol"?f:String(f)}function wt(O,f){return wt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,S){return k.__proto__=S,k},wt(O,f)}function tt(O){return tt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(f){return f.__proto__||Object.getPrototypeOf(f)},tt(O)}var Ot=function(O){(function(g,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(N&&N.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),N&&wt(g,N)})(ne,O);var f,k,S,Y,X=(S=ne,Y=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,N=tt(S);if(Y){var M=tt(this).constructor;g=Reflect.construct(N,arguments,M)}else g=N.apply(this,arguments);return function(A,E){if(E&&(Ce(E)==="object"||typeof E=="function"))return E;if(E!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(J){if(J===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return J}(A)}(this,g)});function ne(g){var N;return function(M,A){if(!(M instanceof A))throw new TypeError("Cannot call a class as a function")}(this,ne),(N=X.call(this,g)).state={loaded:!1},N}return f=ne,(k=[{key:"onImageLoad",value:function(){var g=this;return this.state.loaded?null:function(N){g.props.onLoad(N),g.props.afterLoad(),g.setState({loaded:!0})}}},{key:"getImg",value:function(){var g=this.props,N=(g.afterLoad,g.beforeLoad,g.delayMethod,g.delayTime,g.effect,g.placeholder,g.placeholderSrc,g.scrollPosition,g.threshold,g.useIntersectionObserver,g.visibleByDefault,g.wrapperClassName,g.wrapperProps,function(M,A){if(M==null)return{};var E,J,ce=function(se,Ne){if(se==null)return{};var ge,we,qe={},He=Object.keys(se);for(we=0;we<He.length;we++)ge=He[we],Ne.indexOf(ge)>=0||(qe[ge]=se[ge]);return qe}(M,A);if(Object.getOwnPropertySymbols){var le=Object.getOwnPropertySymbols(M);for(J=0;J<le.length;J++)E=le[J],A.indexOf(E)>=0||Object.prototype.propertyIsEnumerable.call(M,E)&&(ce[E]=M[E])}return ce}(g,Pr));return s().createElement("img",et({},N,{onLoad:this.onImageLoad()}))}},{key:"getLazyLoadImage",value:function(){var g=this.props,N=g.beforeLoad,M=g.className,A=g.delayMethod,E=g.delayTime,J=g.height,ce=g.placeholder,le=g.scrollPosition,se=g.style,Ne=g.threshold,ge=g.useIntersectionObserver,we=g.visibleByDefault,qe=g.width;return s().createElement(Te,{beforeLoad:N,className:M,delayMethod:A,delayTime:E,height:J,placeholder:ce,scrollPosition:le,style:se,threshold:Ne,useIntersectionObserver:ge,visibleByDefault:we,width:qe},this.getImg())}},{key:"getWrappedLazyLoadImage",value:function(g){var N=this.props,M=N.effect,A=N.height,E=N.placeholderSrc,J=N.width,ce=N.wrapperClassName,le=N.wrapperProps,se=this.state.loaded,Ne=se?" lazy-load-image-loaded":"",ge=se||!E?{}:{backgroundImage:"url(".concat(E,")"),backgroundSize:"100% 100%"};return s().createElement("span",et({className:ce+" lazy-load-image-background "+M+Ne,style:Qt(Qt({},ge),{},{color:"transparent",display:"inline-block",height:A,width:J})},le),g)}},{key:"render",value:function(){var g=this.props,N=g.effect,M=g.placeholderSrc,A=g.visibleByDefault,E=g.wrapperClassName,J=g.wrapperProps,ce=this.getLazyLoadImage();return(N||M)&&!A||E||J?this.getWrappedLazyLoadImage(ce):ce}}])&&Ar(f.prototype,k),Object.defineProperty(f,"prototype",{writable:!1}),ne}(s().Component);Ot.propTypes={onLoad:o.PropTypes.func,afterLoad:o.PropTypes.func,beforeLoad:o.PropTypes.func,delayMethod:o.PropTypes.string,delayTime:o.PropTypes.number,effect:o.PropTypes.string,placeholderSrc:o.PropTypes.string,threshold:o.PropTypes.number,useIntersectionObserver:o.PropTypes.bool,visibleByDefault:o.PropTypes.bool,wrapperClassName:o.PropTypes.string,wrapperProps:o.PropTypes.object},Ot.defaultProps={onLoad:function(){},afterLoad:function(){return{}},beforeLoad:function(){return{}},delayMethod:"throttle",delayTime:300,effect:"",placeholderSrc:null,threshold:100,useIntersectionObserver:!0,visibleByDefault:!1,wrapperClassName:""};const xr=Ot})(),Tr.exports=t})();var Le=Tr.exports;const sa=()=>{const{getRelativeTime:r,truncateText:n}=Re(),[i,t]=T.useState(0),a=y=>y.slug&&y.slug.trim()!==""?`/haber/${y.slug}`:`/haber/${y.haber_kodu}`,{data:s,isLoading:o}=ye("homepage-news",()=>fe.getNews({limit:100,sort:"pub_date",order:"DESC"}),{staleTime:6e4,cacheTime:3e5,refetchInterval:12e4}),{data:c}=ye("homepage-economy",()=>fe.getNews({kategori:"EKONOMİ",limit:8,sort:"pub_date",order:"DESC"}),{staleTime:3e4,cacheTime:6e4,refetchInterval:6e4,refetchOnWindowFocus:!0}),{data:u}=ye("homepage-sports",()=>fe.getNews({kategori:"SPOR",limit:8,sort:"pub_date",order:"DESC"}),{staleTime:3e4,cacheTime:6e4,refetchInterval:6e4,refetchOnWindowFocus:!0}),p=(s!=null&&s.success&&Array.isArray(s.data)?s.data:[]).filter(y=>y.main_image&&y.main_image!==null&&y.main_image!=="/assets/images/placeholder.svg"&&y.main_image.trim()!==""&&!y.main_image.includes("placeholder")).sort((y,F)=>new Date(F.pub_date)-new Date(y.pub_date)),h=p.slice(0,15),v=p.filter(y=>!h.some(F=>F.haber_kodu===y.haber_kodu)).slice(0,4),b=p.filter(y=>!h.some(F=>F.haber_kodu===y.haber_kodu)&&!v.some(F=>F.haber_kodu===y.haber_kodu)).slice(0,8),m=p.filter(y=>!h.some(F=>F.haber_kodu===y.haber_kodu)&&!v.some(F=>F.haber_kodu===y.haber_kodu)&&!b.some(F=>F.haber_kodu===y.haber_kodu)).slice(0,8),w=c!=null&&c.success&&Array.isArray(c.data)?c.data:[],P=u!=null&&u.success&&Array.isArray(u.data)?u.data:[];T.useEffect(()=>{t(0)},[]),T.useEffect(()=>{if(h.length>1){const y=setInterval(()=>{t(F=>(F+1)%h.length)},5e3);return()=>clearInterval(y)}},[h.length]);const[j,H]=T.useState(null),[$,z]=T.useState(null),[L,D]=T.useState(!1),ee=50,q=y=>{z(null),H(y.targetTouches[0].clientX),D(!0)},K=y=>{z(y.targetTouches[0].clientX)},C=()=>{if(!j||!$){D(!1);return}const y=j-$,F=y>ee,me=y<-ee;F&&h.length>1&&G(),me&&h.length>1&&ue(),D(!1)},[B,U]=T.useState(null),[Z,_]=T.useState(null),[x,R]=T.useState(!1),W=y=>{_(null),U(y.clientX),R(!0)},re=y=>{x&&_(y.clientX)},ae=()=>{if(!B||!Z||!x){R(!1);return}const y=B-Z,F=y>ee,me=y<-ee;F&&h.length>1&&G(),me&&h.length>1&&ue(),R(!1)},G=()=>{t(y=>(y+1)%h.length)},ue=()=>{t(y=>(y-1+h.length)%h.length)},de=y=>{t(y)};return o?e("div",{className:"loading-container",children:e(ve,{})}):l(ie,{children:[l(Se,{children:[e("title",{children:"MetaAnaliz Haber - Son Haberler"}),e("meta",{name:"description",content:"Türkiye'den ve dünyadan en güncel haberler."})]}),l("div",{className:"new-homepage",children:[e("section",{className:"hero-section",children:e("div",{className:"container",children:l("div",{className:"hero-content",children:[e("div",{className:"hero-slider",children:l("div",{className:"slider-container",children:[e("div",{className:"slider-wrapper",style:{transform:`translateX(-${i*100}%)`,width:`${h.length*100}%`},onTouchStart:q,onTouchMove:K,onTouchEnd:C,onMouseDown:W,onMouseMove:re,onMouseUp:ae,onMouseLeave:ae,children:h.map((y,F)=>e("div",{className:"slide",children:e(V,{to:a(y),children:l("div",{className:"slide-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:me=>{me.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"slide-overlay",children:l("div",{className:"slide-content",children:[y.kategori&&e("span",{className:"slide-category",children:y.kategori}),e("h2",{className:"slide-title",children:_e(ke(y.title))}),e("p",{className:"slide-summary",children:_e(n(ke(y.summary||y.content),150))}),e("div",{className:"slide-meta",children:e("span",{className:"slide-date",children:r(y.pub_date)})})]})})]})})},`slide-${y.haber_kodu}-${F}`))}),h.length>1&&l(ie,{children:[e("button",{className:"slider-btn slider-btn-prev",onClick:ue,children:e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e("button",{className:"slider-btn slider-btn-next",onClick:G,children:e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),h.length>1&&e("div",{className:"slider-dots",children:h.map((y,F)=>e("button",{className:`slider-dot ${F===i?"active":""}`,onClick:()=>de(F),"aria-label":`Slide ${F+1}'e git`},`dot-${F}`))})]})}),e("div",{className:"hero-sidebar",children:e("div",{className:"sidebar-news-list",children:v.map((y,F)=>e("article",{className:"sidebar-news-item",children:l(V,{to:a(y),children:[l("div",{className:"sidebar-news-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:me=>{me.target.src="/assets/images/placeholder.svg"}}),y.kategori&&e("span",{className:"sidebar-news-category",children:y.kategori})]}),l("div",{className:"sidebar-news-content",children:[e("h4",{className:"sidebar-news-title",children:_e(ke(n(y.title,80)))}),e("div",{className:"sidebar-news-meta",children:e("span",{className:"sidebar-news-date",children:r(y.pub_date)})})]})]})},y.haber_kodu))})})]})})}),e("section",{className:"featured-section",children:l("div",{className:"container",children:[e("div",{className:"section-header",children:e("h2",{className:"section-title",children:"Öne Çıkan Haberler"})}),e("div",{className:"news-grid",children:b.map(y=>e("article",{className:"news-card",children:l(V,{to:a(y),children:[l("div",{className:"card-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:F=>{F.target.src="/assets/images/placeholder.svg"}}),y.kategori&&e("span",{className:"card-category",children:y.kategori})]}),l("div",{className:"card-content",children:[e("h3",{className:"card-title",children:_e(ke(y.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:r(y.pub_date)})})]})]})},y.haber_kodu))})]})}),e("section",{className:"latest-section",children:l("div",{className:"container",children:[l("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Son Haberler"}),e(V,{to:"/son-haberler",className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid",children:m.map(y=>e("article",{className:"news-card",children:l(V,{to:a(y),children:[l("div",{className:"card-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:F=>{F.target.src="/assets/images/placeholder.svg"}}),y.kategori&&e("span",{className:"card-category",children:y.kategori})]}),l("div",{className:"card-content",children:[e("h3",{className:"card-title",children:_e(ke(y.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:r(y.pub_date)})})]})]})},y.haber_kodu))})]})}),e(ia,{}),w.length>0&&e("section",{className:"category-section",children:l("div",{className:"container",children:[l("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Ekonomi Haberleri"}),e(V,{to:"/kategori/ekonomi",className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid",children:w.slice(0,8).map(y=>e("article",{className:"news-card",children:l(V,{to:a(y),children:[l("div",{className:"card-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:F=>{F.target.src="/assets/images/placeholder.svg"}}),e("span",{className:"card-category",children:"EKONOMİ"})]}),l("div",{className:"card-content",children:[e("h3",{className:"card-title",children:_e(ke(y.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:r(y.pub_date)})})]})]})},y.haber_kodu))})]})}),P.length>0&&e("section",{className:"category-section",children:l("div",{className:"container",children:[l("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Spor Haberleri"}),e(V,{to:"/kategori/spor",className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid",children:P.slice(0,8).map(y=>e("article",{className:"news-card",children:l(V,{to:a(y),children:[l("div",{className:"card-image",children:[e(Le.LazyLoadImage,{src:y.main_image||y.featured_image||"/assets/images/placeholder.svg",alt:y.title,effect:"blur",onError:F=>{F.target.src="/assets/images/placeholder.svg"}}),e("span",{className:"card-category",children:"SPOR"})]}),l("div",{className:"card-content",children:[e("h3",{className:"card-title",children:_e(ke(y.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:r(y.pub_date)})})]})]})},y.haber_kodu))})]})})]})]})};const $t=({news:r,variant:n="default",showCategory:i=!0,showDate:t=!0,className:a=""})=>{const{getRelativeTime:s}=Re();if(!r)return null;const o=()=>{let p="news-card";switch(n){case"large":p+=" news-card--large";break;case"compact":p+=" news-card--compact";break;case"video":p+=" news-card--video";break;case"local":p+=" news-card--local";break}return a&&(p+=` ${a}`),p},c=()=>r.kategori?r.kategori:r.sehir?r.sehir:"Genel",u=p=>{p.target.src="/assets/images/placeholder.svg"},d=()=>r.slug&&r.slug.trim()!==""?`/haber/${r.slug}`:`/haber/${r.haber_kodu}`;return e("article",{className:o(),children:l(V,{to:d(),children:[l("div",{className:"news-card__image",children:[e(Le.LazyLoadImage,{src:r.main_image||r.featured_image||"/assets/images/placeholder.svg",alt:_e(ke(r.title)),effect:"blur",onError:u,placeholderSrc:"/assets/images/placeholder.svg"}),i&&e("span",{className:"news-card__category",children:c()}),r.son_dakika==="Evet"&&e("span",{className:"news-card__breaking",children:"SON DAKİKA"})]}),l("div",{className:"news-card__content",children:[e("h3",{className:"news-card__title",children:_e(ke(r.title))}),t&&e("div",{className:"news-card__meta",children:e("span",{className:"news-card__date",children:s(r.pub_date||r.tarih)})})]})]})})};$t.propTypes={news:I.shape({haber_kodu:I.string.isRequired,title:I.string.isRequired,slug:I.string,main_image:I.string,featured_image:I.string,kategori:I.string,sehir:I.string,pub_date:I.string,tarih:I.string,son_dakika:I.string}).isRequired,variant:I.oneOf(["default","large","compact","video","local"]),showCategory:I.bool,showDate:I.bool,className:I.string};const Fe=({news:r=[],showCategory:n=!1,variant:i="default"})=>!r||r.length===0?e("div",{className:"alert alert-info",children:"Henüz haber bulunmuyor."}):e("div",{className:"news-grid row",children:r.map(t=>e("div",{className:"news-grid-item mb-4",children:e($t,{news:t,variant:i,showCategory:n,showDate:!0})},t.haber_kodu))});Fe.propTypes={news:I.arrayOf(I.shape({haber_kodu:I.string.isRequired,title:I.string.isRequired,featured_image:I.string,kategori:I.string,sehir:I.string,pub_date:I.string,son_dakika:I.string})),showCategory:I.bool,variant:I.string};const Be=({message:r,onRetry:n})=>e("div",{className:"error-message-container",children:l("div",{className:"alert alert-danger",role:"alert",children:[l("h4",{className:"alert-heading",children:[e("i",{className:"fas fa-exclamation-triangle me-2"}),"Hata!"]}),e("p",{children:r||"Bir hata oluştu. Lütfen daha sonra tekrar deneyin."}),n&&e("div",{className:"mt-3",children:l("button",{className:"btn btn-outline-danger",onClick:n,children:[e("i",{className:"fas fa-sync-alt me-2"}),"Tekrar Dene"]})})]})}),oa=()=>{var H,$;const{categoryName:r}=Mt(),[n,i]=T.useState(1),[t,a]=T.useState([]),[s,o]=T.useState(!1),{setCurrentCategory:c}=Re(),u=ea(r),d=vt(u),p=z=>z?z.toLowerCase().split(" ").map(L=>L.charAt(0).toUpperCase()+L.slice(1)).join(" "):"";T.useEffect(()=>(c(d),i(1),a([]),o(!0),()=>c("")),[r,d,c]);const{data:h,isLoading:v,error:b,refetch:m}=ye(["category-news",u,n],()=>fe.getNews({kategori:u,page:n,limit:12}),{keepPreviousData:!0,enabled:!!u&&!!r,retry:3,retryDelay:1e3,onSuccess:z=>{z.success&&Array.isArray(z.data)&&a(n===1?z.data:L=>[...L,...z.data]),o(!1)}}),w=!d||v&&n===1||s,P=()=>{var z,L;((z=h==null?void 0:h.pagination)==null?void 0:z.current_page)<((L=h==null?void 0:h.pagination)==null?void 0:L.total_pages)&&i(D=>D+1)},j=((H=h==null?void 0:h.pagination)==null?void 0:H.current_page)<(($=h==null?void 0:h.pagination)==null?void 0:$.total_pages);return b?e("div",{className:"container",children:e(Be,{message:"Haberler yüklenirken bir hata oluştu.",onRetry:()=>{m()}})}):l(ie,{children:[l(Se,{children:[l("title",{children:[d," Haberleri - MetaAnaliz Haber"]}),e("meta",{name:"description",content:`${d} kategorisindeki en güncel haberler. MetaAnaliz Haber'de ${d} ile ilgili tüm gelişmeleri takip edin.`})]}),l("div",{className:"category-page",children:[e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("span",{className:"category-name",children:p(d)}),e("span",{className:"category-subtitle",children:" Haberleri"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:d})]})})})]})}),e("div",{className:"category-news-section",children:e("div",{className:"container",children:w?e("div",{className:"text-center py-5",children:e(ve,{text:"Haberler yükleniyor..."})}):t.length===0&&!v&&d?l("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Bu kategoride henüz haber bulunmuyor."]}):l(ie,{children:[e(Fe,{news:t,showCategory:!1,variant:"default"}),j&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:P,disabled:v,children:v?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Haber Yükle"})}),v&&n>1&&e("div",{className:"text-center py-3",children:e(ve,{})})]})})})]})]})},ca=()=>{var b;const[r,n]=T.useState(1),[i,t]=T.useState([]),{setCurrentCategory:a}=Re();oe.useEffect(()=>(a("Video Haberler"),()=>a("")),[a]);const{data:s,isLoading:o,error:c,refetch:u}=ye(["video-news",r],()=>fe.getNews({limit:12,page:r,has_videos:!0}),{keepPreviousData:!0,onSuccess:m=>{var w;t(r===1?((w=m==null?void 0:m.data)==null?void 0:w.data)||[]:P=>{var j;return[...P,...((j=m==null?void 0:m.data)==null?void 0:j.data)||[]]})}});if(c)return e("div",{className:"container py-5",children:e(Be,{message:"Videolu haberler yüklenirken bir hata oluştu.",onRetry:u})});const d=((b=s==null?void 0:s.data)==null?void 0:b.total)||0,p=Math.ceil(d/12),h=r<p;return l(ie,{children:[l(Se,{children:[e("title",{children:"Videolu Haberler - MetaAnaliz Haber"}),e("meta",{name:"description",content:"Video içeren güncel haberler ve gelişmeler."})]}),e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("i",{className:"fas fa-play-circle me-3"}),"Videolu Haberler"]}),e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",children:"Videolu Haberler"})]})})]})}),e("div",{className:"container",children:e("div",{className:"category-news-section",children:o&&r===1?e("div",{className:"text-center py-5",children:e(ve,{})}):l(ie,{children:[e(Fe,{news:i}),h&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:()=>{h&&!o&&n(m=>m+1)},disabled:o,children:o?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Video Yükle"})}),o&&r>1&&e("div",{className:"text-center py-3",children:e(ve,{})}),i.length===0&&!o&&e("div",{className:"text-center py-5",children:l("div",{className:"no-results",children:[e("i",{className:"fas fa-video fa-3x text-muted mb-3"}),e("h3",{children:"Videolu haber bulunamadı"}),e("p",{className:"text-muted",children:"Şu anda videolu haber bulunmamaktadır."}),e(V,{to:"/",className:"btn btn-primary",children:"Ana Sayfaya Dön"})]})})]})})})]})},la=()=>{var b;const[r,n]=T.useState(1),[i,t]=T.useState([]),{setCurrentCategory:a}=Re();oe.useEffect(()=>(a("Son Haberler"),()=>a("")),[a]);const{data:s,isLoading:o,error:c,refetch:u}=ye(["latest-news",r],()=>fe.getNews({limit:12,page:r,sort:"pub_date",order:"DESC"}),{keepPreviousData:!0,onSuccess:m=>{const w=m!=null&&m.success&&Array.isArray(m.data)?m.data:[];t(r===1?w:P=>[...P,...w])}});if(c)return e("div",{className:"container py-5",children:e(Be,{message:"Son haberler yüklenirken bir hata oluştu.",onRetry:u})});const d=((b=s==null?void 0:s.data)==null?void 0:b.total)||0,p=Math.ceil(d/12),h=r<p;return l(ie,{children:[l(Se,{children:[e("title",{children:"Son Haberler - MetaAnaliz Haber"}),e("meta",{name:"description",content:"En güncel haberler ve son dakika gelişmeleri."})]}),e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("i",{className:"fas fa-clock me-3"}),"Son Haberler"]}),e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",children:"Son Haberler"})]})})]})}),e("div",{className:"container",children:e("div",{className:"category-news-section",children:o&&r===1?e("div",{className:"text-center py-5",children:e(ve,{})}):l(ie,{children:[e(Fe,{news:i}),h&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:()=>{h&&!o&&n(m=>m+1)},disabled:o,children:o?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Haber Yükle"})}),o&&r>1&&e("div",{className:"text-center py-3",children:e(ve,{})}),i.length===0&&!o&&e("div",{className:"text-center py-5",children:l("div",{className:"no-results",children:[e("i",{className:"fas fa-newspaper fa-3x text-muted mb-3"}),e("h3",{children:"Haber bulunamadı"}),e("p",{className:"text-muted",children:"Şu anda haber bulunmamaktadır."}),e(V,{to:"/",className:"btn btn-primary",children:"Ana Sayfaya Dön"})]})})]})})})]})};const ua=()=>{const[r,n]=T.useState(null),[i,t]=T.useState(null),[a,s]=T.useState(!0),[o,c]=T.useState(null),[u,d]=T.useState(null),p="http://localhost/metaanalizhaber_yeni/backend",h=async()=>{try{const P=await(await fetch(`${p}/cron_monitor.php?action=status`)).json();if(P.success)n(P.data),d(new Date);else throw new Error(P.error||"Veri alınamadı")}catch(w){c("Cron durumu alınamadı: "+w.message)}},v=async()=>{try{const P=await(await fetch(`${p}/cron_monitor.php?action=rss_stats`)).json();if(P.success)t(P.data);else throw new Error(P.error||"RSS istatistikleri alınamadı")}catch(w){console.error("RSS stats error:",w)}};T.useEffect(()=>{(async()=>{s(!0),c(null),await Promise.all([h(),v()]),s(!1)})()},[]),T.useEffect(()=>{const w=setInterval(()=>{h(),v()},3e4);return()=>clearInterval(w)},[]);const b=({status:w})=>{const j=(H=>{switch(H){case"running":return{class:"success",text:"Çalışıyor",icon:"fa-check-circle"};case"delayed":return{class:"warning",text:"Gecikmeli",icon:"fa-exclamation-triangle"};case"stopped":return{class:"danger",text:"Durdu",icon:"fa-times-circle"};default:return{class:"secondary",text:"Bilinmiyor",icon:"fa-question-circle"}}})(w);return l("span",{className:`badge badge-${j.class}`,children:[e("i",{className:`fas ${j.icon} me-1`}),j.text]})},m=({score:w})=>l("div",{className:`health-score health-${(j=>j>=80?"success":j>=60?"warning":"danger")(w)}`,children:[e("div",{className:"health-circle",children:e("span",{className:"health-number",children:w})}),e("small",{children:"Sağlık Skoru"})]});return a?e("div",{className:"container py-5",children:l("div",{className:"text-center",children:[e(ve,{}),e("p",{className:"mt-3",children:"Cron durumu yükleniyor..."})]})}):o?e("div",{className:"container py-5",children:e(Be,{message:o,onRetry:()=>window.location.reload()})}):l(ie,{children:[l(Se,{children:[e("title",{children:"Cron Monitoring - MetaAnaliz Haber"}),e("meta",{name:"description",content:"Cron job'ların durumu ve sistem istatistikleri"})]}),e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("i",{className:"fas fa-cogs me-3"}),"Cron Monitoring"]}),e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",children:"Cron Monitoring"})]})}),u&&l("p",{className:"text-muted mb-0",children:[e("i",{className:"fas fa-sync-alt me-1"}),"Son güncelleme: ",u.toLocaleTimeString("tr-TR")]})]})}),l("div",{className:"container",children:[e("div",{className:"row mb-4",children:e("div",{className:"col-12",children:e("div",{className:"card",children:l("div",{className:"card-body text-center",children:[e("h5",{className:"card-title",children:"Genel Sistem Durumu"}),l("div",{className:`overall-health health-${(r==null?void 0:r.overall_health)||"unknown"}`,children:[e("i",{className:`fas ${(r==null?void 0:r.overall_health)==="good"?"fa-check-circle":(r==null?void 0:r.overall_health)==="warning"?"fa-exclamation-triangle":"fa-times-circle"} fa-3x mb-2`}),e("h4",{children:(r==null?void 0:r.overall_health)==="good"?"İyi":(r==null?void 0:r.overall_health)==="warning"?"Uyarı":"Kritik"})]})]})})})}),l("div",{className:"row",children:[(r==null?void 0:r.news_fetch)&&e("div",{className:"col-lg-4 mb-4",children:l("div",{className:"card h-100",children:[l("div",{className:"card-header d-flex justify-content-between align-items-center",children:[l("h6",{className:"mb-0",children:[e("i",{className:"fas fa-newspaper me-2"}),r.news_fetch.name]}),e(b,{status:r.news_fetch.status})]}),l("div",{className:"card-body",children:[l("div",{className:"row",children:[e("div",{className:"col-8",children:l("div",{className:"stats-grid",children:[l("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:r.news_fetch.news_count_24h}),e("span",{className:"stat-label",children:"Haber (24s)"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:r.news_fetch.image_count_24h}),e("span",{className:"stat-label",children:"Görsel (24s)"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:r.news_fetch.video_count_24h}),e("span",{className:"stat-label",children:"Video (24s)"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:r.news_fetch.error_count_24h}),e("span",{className:"stat-label",children:"Hata (24s)"})]})]})}),e("div",{className:"col-4 text-center",children:e(m,{score:r.news_fetch.health_score})})]}),r.news_fetch.last_run&&e("div",{className:"mt-3",children:l("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",r.news_fetch.last_run.ago]})})]})]})}),(r==null?void 0:r.media_download)&&e("div",{className:"col-lg-4 mb-4",children:l("div",{className:"card h-100",children:[l("div",{className:"card-header d-flex justify-content-between align-items-center",children:[l("h6",{className:"mb-0",children:[e("i",{className:"fas fa-download me-2"}),r.media_download.name]}),e(b,{status:r.media_download.status})]}),l("div",{className:"card-body",children:[l("div",{className:"row",children:[e("div",{className:"col-8",children:l("div",{className:"stats-grid",children:[l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-warning",children:r.media_download.pending_images}),e("span",{className:"stat-label",children:"Bekleyen Görsel"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-warning",children:r.media_download.pending_videos}),e("span",{className:"stat-label",children:"Bekleyen Video"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:r.media_download.failed_images}),e("span",{className:"stat-label",children:"Başarısız Görsel"})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:r.media_download.failed_videos}),e("span",{className:"stat-label",children:"Başarısız Video"})]})]})}),e("div",{className:"col-4 text-center",children:e(m,{score:r.media_download.health_score})})]}),r.media_download.last_run&&e("div",{className:"mt-3",children:l("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",r.media_download.last_run.ago]})})]})]})}),(r==null?void 0:r.system_cleanup)&&e("div",{className:"col-lg-4 mb-4",children:l("div",{className:"card h-100",children:[l("div",{className:"card-header d-flex justify-content-between align-items-center",children:[l("h6",{className:"mb-0",children:[e("i",{className:"fas fa-broom me-2"}),r.system_cleanup.name]}),e(b,{status:r.system_cleanup.status})]}),l("div",{className:"card-body",children:[l("div",{className:"row",children:[l("div",{className:"col-8",children:[r.system_cleanup.disk_usage&&l("div",{className:"mb-3",children:[l("div",{className:"d-flex justify-content-between",children:[e("span",{children:"Disk Kullanımı"}),l("span",{children:[r.system_cleanup.disk_usage.usage_percent,"%"]})]}),e("div",{className:"progress",children:e("div",{className:`progress-bar ${r.system_cleanup.disk_usage.usage_percent>80?"bg-danger":r.system_cleanup.disk_usage.usage_percent>60?"bg-warning":"bg-success"}`,style:{width:`${r.system_cleanup.disk_usage.usage_percent}%`}})}),l("small",{className:"text-muted",children:[r.system_cleanup.disk_usage.used," / ",r.system_cleanup.disk_usage.total]})]}),l("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:r.system_cleanup.error_count_7d}),e("span",{className:"stat-label",children:"Hata (7 gün)"})]})]}),e("div",{className:"col-4 text-center",children:e(m,{score:r.system_cleanup.health_score})})]}),r.system_cleanup.last_run&&e("div",{className:"mt-3",children:l("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",r.system_cleanup.last_run.ago]})})]})]})})]}),i&&e("div",{className:"row mt-4",children:e("div",{className:"col-12",children:l("div",{className:"card",children:[e("div",{className:"card-header",children:l("h6",{className:"mb-0",children:[e("i",{className:"fas fa-rss me-2"}),"RSS İstatistikleri (Son 24 Saat)"]})}),e("div",{className:"card-body",children:l("div",{className:"row",children:[e("div",{className:"col-md-2",children:l("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number",children:i.total_requests_24h}),e("span",{className:"stat-label",children:"Toplam İstek"})]})}),e("div",{className:"col-md-2",children:l("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number text-success",children:i.successful_requests_24h}),e("span",{className:"stat-label",children:"Başarılı"})]})}),e("div",{className:"col-md-2",children:l("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number text-danger",children:i.failed_requests_24h}),e("span",{className:"stat-label",children:"Başarısız"})]})}),e("div",{className:"col-md-2",children:l("div",{className:"stat-item text-center",children:[l("span",{className:"stat-number",children:[i.success_rate,"%"]}),e("span",{className:"stat-label",children:"Başarı Oranı"})]})}),e("div",{className:"col-md-2",children:l("div",{className:"stat-item text-center",children:[l("span",{className:"stat-number",children:[i.avg_response_time,"s"]}),e("span",{className:"stat-label",children:"Ort. Yanıt Süresi"})]})}),e("div",{className:"col-md-2",children:e("div",{className:"stat-item text-center",children:l("button",{className:"btn btn-sm btn-outline-primary",onClick:()=>{h(),v()},children:[e("i",{className:"fas fa-sync-alt"}),"Yenile"]})})})]})})]})})})]})]})},da=({category:r,excludeHaberKodu:n})=>{const{data:i,isLoading:t}=ye(["related-news",r,n],()=>fe.getNews({kategori:r,limit:6}),{enabled:!!r,select:a=>{if(a.success&&a.data){console.log("RelatedNews - Original data:",a.data.length,"items"),console.log("RelatedNews - Excluding haber_kodu:",n);const s=a.data.filter(o=>{const c=o.haber_kodu===n;return c&&console.log("RelatedNews - Excluding item:",o.haber_kodu,o.title),!c});return console.log("RelatedNews - Filtered data:",s.length,"items"),{...a,data:s}}return a}});return t?l("section",{className:"related-news mt-5",children:[e("div",{className:"section-title",children:e("h2",{children:"İlgili Haberler"})}),e("div",{className:"text-center py-3",children:e(ve,{})})]}):!(i!=null&&i.success)||!Array.isArray(i.data)||i.data.length===0?null:l("section",{className:"related-news mt-5",children:[e("div",{className:"section-header",children:e("h2",{className:"section-title",children:"İlgili Haberler"})}),e("div",{className:"related-news-grid",children:i.data.slice(0,3).map(a=>{var s;return e("article",{className:"related-news-item",children:l("a",{href:`/haber/${a.slug||a.haber_kodu}`,className:"related-news-link",children:[l("div",{className:"related-news-image-container",children:[e("img",{src:a.main_image||a.featured_image||"/assets/images/placeholder.svg",alt:a.title,className:"related-news-image",onError:o=>{o.target.src="/assets/images/placeholder.svg"}}),a.son_dakika==="Evet"&&e("span",{className:"breaking-badge",children:"SON DAKİKA"})]}),l("div",{className:"related-news-content",children:[e("h3",{className:"related-news-title",children:(s=a.title)==null?void 0:s.replace(/<[^>]*>/g,"")}),l("div",{className:"related-news-meta",children:[l("span",{className:"related-news-date",children:[e("i",{className:"far fa-clock"}),new Date(a.pub_date).toLocaleDateString("tr-TR",{day:"numeric",month:"long",year:"numeric"})]}),l("span",{className:"related-news-location",children:[e("i",{className:"fas fa-map-marker-alt"}),a.sehir]})]})]})]})},a.haber_kodu)})})]})};const ha=()=>{var C,B,U,Z;const{newsSlug:r}=Mt();Mr();const{formatDate:n}=Re(),[i,t]=T.useState(()=>{const _=localStorage.getItem("news-font-size");return _?parseInt(_):18}),[a,s]=T.useState(!1),o=()=>{const _=Math.min(i+2,24);t(_),localStorage.setItem("news-font-size",_.toString())},c=()=>{const _=Math.max(i-2,14);t(_),localStorage.setItem("news-font-size",_.toString())},u=()=>{t(18),localStorage.setItem("news-font-size","18")},d=()=>{if(a)window.speechSynthesis.cancel(),s(!1);else if("speechSynthesis"in window&&m){const _=Tt(m.description),x=`${m.title}. ${_}`,R=new SpeechSynthesisUtterance(x);R.lang="tr-TR",R.rate=.9,R.pitch=1,R.onstart=()=>s(!0),R.onend=()=>s(!1),R.onerror=()=>s(!1),window.speechSynthesis.speak(R)}},{data:p,isLoading:h,error:v,refetch:b}=ye(["news-detail",r],()=>fe.getNewsDetail(r),{enabled:!!r,onError:_=>{var x,R;console.error("Haber detay hatası:",_),console.error("Error details:",(x=_.response)==null?void 0:x.data),console.error("Error status:",(R=_.response)==null?void 0:R.status)}}),m=p!=null&&p.success?p.data:null,w=((C=p==null?void 0:p.data)==null?void 0:C.images)||[],P=((B=p==null?void 0:p.data)==null?void 0:B.videos)||[],j=(_,x)=>{if(!_||x.length<=1)return{contentElements:Tt(_||"").split(`
`).filter(y=>y.trim()),usedImages:[]};const W=Tt(_).split(`
`).filter(de=>de.trim()),re=[],ae=[];x[0];const G=x.slice(1);if(G.length===0)return{contentElements:W,usedImages:[]};const ue=Math.max(2,Math.floor(W.length/G.length));return W.forEach((de,y)=>{if(re.push({type:"paragraph",content:de,index:y}),G.length>0&&y>0&&(y+1)%ue===0&&y<W.length-1){const F=G.shift();F&&(re.push({type:"image",content:F,index:ae.length}),ae.push(F))}}),{contentElements:re,usedImages:ae}};T.useEffect(()=>{w.length>0&&(console.log("Images array:",w),console.log("Images count:",w.length))},[w]);const{contentElements:H,usedImages:$}=m?j(m.description,w):{contentElements:[],usedImages:[]},z=w.length>0?[w[0],...w.slice(1).filter(_=>!$.includes(_))]:[],[L,D]=T.useState(0),ee=()=>{z.length>1&&D(_=>(_+1)%z.length)},q=()=>{z.length>1&&D(_=>(_-1+z.length)%z.length)},K=_=>{D(_)};return T.useEffect(()=>{m!=null&&m.title&&(document.title=`${_e(m.title)} - MetaAnaliz Haber`)},[m]),h?e("div",{className:"container",children:e("div",{className:"text-center py-5",children:e(ve,{})})}):v?e("div",{className:"container",children:l("div",{className:"alert alert-danger",children:[e("h4",{children:"Haber yüklenirken bir hata oluştu"}),l("p",{children:[e("strong",{children:"Slug:"})," ",r]}),l("p",{children:[e("strong",{children:"Hata:"})," ",v.message]}),l("p",{children:[e("strong",{children:"Status:"})," ",(U=v.response)==null?void 0:U.status]}),l("p",{children:[e("strong",{children:"Response:"})," ",JSON.stringify((Z=v.response)==null?void 0:Z.data)]}),e("button",{className:"btn btn-primary",onClick:b,children:"Tekrar Dene"})]})}):m?l(ie,{children:[l(Se,{children:[l("title",{children:[_e(m.title)," - MetaAnaliz Haber"]}),e("meta",{name:"description",content:ke(m.description).substring(0,160)}),e("meta",{property:"og:title",content:`${_e(m.title)} - MetaAnaliz Haber`}),e("meta",{property:"og:description",content:ke(m.description).substring(0,160)}),e("meta",{property:"og:type",content:"article"}),e("meta",{property:"og:url",content:window.location.href}),e("meta",{property:"og:site_name",content:"MetaAnaliz Haber"}),w.length>0&&w[0]&&l(ie,{children:[e("meta",{property:"og:image",content:w[0]}),e("meta",{property:"og:image:width",content:"1200"}),e("meta",{property:"og:image:height",content:"630"}),e("meta",{property:"og:image:alt",content:_e(m.title)}),e("meta",{property:"og:image:type",content:"image/webp"})]})]}),e("div",{className:"news-detail-page",children:l("div",{className:"container",children:[l("article",{className:"news-article",children:[l("header",{className:"news-header",children:[m.son_dakika==="Evet"&&e("span",{className:"breaking-badge",children:"SON DAKİKA"}),e("h1",{className:"news-title",children:_e(m.title)}),l("div",{className:"news-meta",children:[l("div",{className:"news-meta-left",children:[e("span",{className:"category-badge",children:m.kategori}),l("span",{className:"news-date",children:[e("i",{className:"far fa-clock"}),n(m.pub_date)]}),l("span",{className:"news-location",children:[e("i",{className:"fas fa-map-marker-alt"}),m.sehir]})]}),l("div",{className:"news-meta-right",children:[l("div",{className:"font-controls",children:[e("button",{onClick:c,className:"font-control-btn",title:"Yazıyı küçült",children:"A-"}),e("button",{onClick:u,className:"font-control-btn active",title:"Normal boyut",children:"A"}),e("button",{onClick:o,className:"font-control-btn",title:"Yazıyı büyült",children:"A+"})]}),l("button",{onClick:d,className:`speech-btn ${a?"reading":""}`,title:a?"Okumayı durdur":"Sesli oku",children:[e("i",{className:`fas ${a?"fa-stop":"fa-volume-up"}`}),a?"Durdur":"Sesli Oku"]})]})]})]}),(z.length>0||m.main_image)&&e("div",{className:"news-image-carousel",children:l("div",{className:"carousel-container",children:[l("div",{className:"carousel-main",children:[z.length>0?e("img",{src:z[L],alt:m.title,className:"main-image"}):e("img",{src:m.main_image,alt:m.title,className:"main-image"}),z.length>1&&l(ie,{children:[e("button",{className:"carousel-arrow carousel-arrow-left",onClick:q,"aria-label":"Önceki görsel",children:e("i",{className:"fas fa-chevron-left"})}),e("button",{className:"carousel-arrow carousel-arrow-right",onClick:ee,"aria-label":"Sonraki görsel",children:e("i",{className:"fas fa-chevron-right"})})]})]}),z.length>1&&e("div",{className:"carousel-thumbnails",children:z.map((_,x)=>e("button",{className:`thumbnail ${x===L?"active":""}`,onClick:()=>K(x),children:e("img",{src:_,alt:`Görsel ${x+1}`})},x))})]})}),(P.length>0||m.has_videos)&&l("div",{className:"news-videos news-videos-top",children:[l("h3",{className:"section-title",children:[e("i",{className:"fas fa-play-circle"}),"Haber Videosu"]}),P.length>0?P.map(_=>{var W,re;const x=(W=_.video_url)==null?void 0:W.replace(/&amp;/g,"&"),R=(re=_.poster_url)==null?void 0:re.replace(/&amp;/g,"&");return x?e("div",{className:"video-container",children:l("div",{className:"video-wrapper",children:[l("video",{controls:!0,poster:R,className:"video-player",preload:"metadata",playsInline:!0,onError:ae=>{console.error("Video yükleme hatası:",ae.target.error)},children:[e("source",{src:x,type:"video/mp4"}),e("source",{src:x,type:"video/webm"}),e("source",{src:x,type:"video/ogg"}),"Tarayıcınız video etiketini desteklemiyor."]}),_.description&&l("div",{className:"video-caption",children:[e("i",{className:"fas fa-info-circle"}),_.description]})]})},_.video_kodu||_.video_url||Math.random()):null}):e("div",{className:"video-container",children:e("div",{className:"video-wrapper",children:e("div",{className:"video-placeholder",children:l("div",{className:"placeholder-content",children:[e("i",{className:"fas fa-video"}),e("p",{children:"Video yükleniyor..."}),e("small",{children:"Bu habere ait video içeriği hazırlanıyor."})]})})})})]}),l("div",{className:"news-content",children:[e("h2",{className:"content-title",children:"Haber İçeriği"}),e("div",{className:"content-text",children:H.map((_,x)=>_.type==="paragraph"?e("p",{className:"content-paragraph",style:{fontSize:`${i}px`},children:_.content},`paragraph-${x}`):_.type==="image"?e("div",{className:"content-image",children:e("img",{src:_.content,alt:`Haber görseli ${_.index+1}`,className:"inline-image",loading:"lazy"})},`image-${x}`):null)}),e("div",{className:"news-disclaimer",children:e("p",{className:"disclaimer-text",children:'"Bu içerik IHA Haber Ajansı tarafından hazırlanmıştır. İçerikte yer alan görüş ve ifadeler, editöryal politikamızı yansıtmayabilir"'})})]})]}),l("footer",{className:"news-footer",children:[e("div",{className:"news-tags",children:l("div",{className:"tags-container",children:[e("h4",{className:"tags-title",children:"Etiketler"}),l("div",{className:"tags-list",children:[e(V,{to:`/kategori/${encodeURIComponent(m.kategori)}`,className:"tag-item",children:m.kategori}),e(V,{to:`/arama?q=${encodeURIComponent(m.sehir)}`,className:"tag-item",children:m.sehir})]})]})}),e("div",{className:"news-share",children:l("div",{className:"share-container",children:[e("h4",{className:"share-title",children:"Paylaş"}),l("div",{className:"share-buttons",children:[e("a",{href:`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button facebook","aria-label":"Facebook'ta paylaş",title:"Facebook'ta paylaş",children:e("i",{className:"fab fa-facebook-f"})}),e("a",{href:`https://x.com/intent/tweet?text=${encodeURIComponent(m.title)}&url=${encodeURIComponent(window.location.href)}&hashtags=haber,gündem`,target:"_blank",rel:"noopener noreferrer",className:"share-button twitter","aria-label":"X'te paylaş",title:"X'te paylaş",children:e("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})}),e("a",{href:`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(m.title)}&summary=${encodeURIComponent(ke(m.description).substring(0,200))}&source=MetaAnaliz%20Haber`,target:"_blank",rel:"noopener noreferrer",className:"share-button linkedin","aria-label":"LinkedIn'de paylaş",title:"LinkedIn'de paylaş",children:e("i",{className:"fab fa-linkedin-in"})}),e("a",{href:`https://wa.me/?text=${encodeURIComponent(`${m.title}

${ke(m.description).substring(0,100)}...

Detaylar: ${window.location.href}`)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button whatsapp","aria-label":"WhatsApp'ta paylaş",title:"WhatsApp'ta paylaş",children:e("i",{className:"fab fa-whatsapp"})}),e("a",{href:`https://telegram.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(m.title)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button telegram","aria-label":"Telegram'da paylaş",title:"Telegram'da paylaş",children:e("i",{className:"fab fa-telegram-plane"})}),e("button",{onClick:async _=>{try{if(navigator.share)await navigator.share({title:m.title,text:ke(m.description).substring(0,100)+"...",url:window.location.href});else if(navigator.clipboard){await navigator.clipboard.writeText(window.location.href);const x=_.target.closest("button"),R=x.innerHTML;x.innerHTML='<i class="fas fa-check"></i>',x.style.background="#28a745",x.style.color="#fff",setTimeout(()=>{x.innerHTML=R,x.style.background="",x.style.color=""},2e3)}else{const x=document.createElement("textarea");x.value=window.location.href,document.body.appendChild(x),x.select(),document.execCommand("copy"),document.body.removeChild(x);const R=_.target.closest("button"),W=R.innerHTML;R.innerHTML='<i class="fas fa-check"></i>',R.style.background="#28a745",R.style.color="#fff",setTimeout(()=>{R.innerHTML=W,R.style.background="",R.style.color=""},2e3)}}catch(x){console.error("Paylaşım hatası:",x)}},className:"share-button copy","aria-label":"Linki kopyala veya paylaş",title:"Linki kopyala",children:e("i",{className:"fas fa-copy"})})]})]})})]}),e(da,{category:m.kategori,excludeHaberKodu:m.haber_kodu})]})})]}):e("div",{className:"container",children:e("div",{className:"alert alert-warning",children:"Haber bulunamadı."})})},fa=()=>{const{getRelativeTime:r,truncateText:n}=Re(),{data:i,isLoading:t}=ye("categories",()=>fe.getCategories(),{staleTime:6e5}),{data:a,isLoading:s}=ye("popular-news",()=>fe.getNews({limit:5}),{staleTime:3e5}),{data:o,isLoading:c}=ye("latest-news",()=>fe.getNews({limit:5}),{staleTime:3e5});return l("aside",{className:"sidebar",children:[l("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Kategoriler"}),l("div",{className:"category-list",children:[l(V,{to:"/yerel-haberler",className:"category-item featured-category",children:[l("span",{children:[e("i",{className:"fas fa-map-marker-alt"})," Yerel Haberler"]}),e("span",{className:"category-badge",children:"YENİ"})]}),t?e("div",{className:"text-center py-3",children:e(ve,{})}):i!=null&&i.success&&i.data?i.data.map(u=>l(V,{to:`/kategori/${encodeURIComponent(u.name)}`,className:"category-item",children:[e("span",{children:vt(u.name)}),e("span",{className:"category-count",children:u.count})]},u.name)):e("p",{className:"text-muted",children:"Kategoriler yüklenemedi"})]})]}),l("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Popüler Haberler"}),e("div",{className:"popular-news",children:s?e("div",{className:"text-center py-3",children:e(ve,{})}):a!=null&&a.success&&a.data?a.data.slice(0,5).map((u,d)=>e("article",{className:"news-item",children:l(V,{to:`/haber/${u.haber_kodu}`,className:"news-link",children:[e("div",{className:"news-number",children:d+1}),e(Le.LazyLoadImage,{src:u.featured_image||"/assets/images/placeholder.svg",alt:u.title,className:"news-image",effect:"blur",onError:p=>{p.target.src="/assets/images/placeholder.svg"}}),l("div",{className:"news-content",children:[e("h4",{className:"news-title",children:n(u.title,60)}),l("div",{className:"news-meta",children:[e("span",{className:"news-date",children:r(u.pub_date)}),l("span",{className:"news-views",children:[e("i",{className:"far fa-eye me-1"}),u.view_count]})]})]})]})},u.haber_kodu)):e("div",{className:"alert alert-info",children:"Popüler haber bulunamadı."})})]}),l("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Son Eklenen"}),e("div",{className:"latest-news",children:c?e("div",{className:"text-center py-3",children:e(ve,{})}):o!=null&&o.success&&Array.isArray(o.data)?o.data.slice(0,5).map(u=>e("article",{className:"news-item",children:l(V,{to:`/haber/${u.haber_kodu}`,className:"news-link",children:[e(Le.LazyLoadImage,{src:u.featured_image||"/assets/images/placeholder.svg",alt:u.title,className:"news-image",effect:"blur",onError:d=>{d.target.src="/assets/images/placeholder.svg"}}),l("div",{className:"news-content",children:[e("h4",{className:"news-title",children:n(u.title,60)}),l("div",{className:"news-meta",children:[e("span",{className:"category-badge",children:u.kategori}),e("span",{className:"news-date",children:r(u.pub_date)})]})]})]})},u.haber_kodu)):e("div",{className:"alert alert-info",children:"Son haber bulunamadı."})})]}),l("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:e("span",{className:"breaking-badge",children:"SON DAKİKA"})}),e("div",{className:"breaking-news",children:e("div",{className:"alert alert-warning",children:"Son dakika haberleri yükleniyor..."})})]})]})},ma=()=>{var v,b,m,w;const[r]=jr(),[n,i]=T.useState(1),[t,a]=T.useState([]),s=r.get("q")||"";T.useEffect(()=>{i(1),a([])},[s]);const{data:o,isLoading:c,error:u,refetch:d}=ye(["search",s,n],()=>fe.searchNews(s,{page:n,limit:12}),{enabled:!!s,keepPreviousData:!0,onSuccess:P=>{var j;P.success&&((j=P.data)!=null&&j.data)&&a(n===1?P.data.data:H=>[...H,...P.data.data])}}),p=()=>{var P,j;((P=o==null?void 0:o.pagination)==null?void 0:P.current_page)<((j=o==null?void 0:o.pagination)==null?void 0:j.total_pages)&&i(H=>H+1)},h=((v=o==null?void 0:o.pagination)==null?void 0:v.current_page)<((b=o==null?void 0:o.pagination)==null?void 0:b.total_pages);return s?u?e("div",{className:"container",children:e(Be,{message:"Arama sonuçları yüklenirken bir hata oluştu.",onRetry:d})}):l(ie,{children:[l(Se,{children:[l("title",{children:[s," - Arama Sonuçları | MetaAnaliz Haber"]}),e("meta",{name:"description",content:`"${s}" için arama sonuçları. MetaAnaliz Haber'de ${s} ile ilgili haberler.`})]}),l("div",{className:"container",children:[l("div",{className:"page-header",children:[l("h1",{className:"page-title",children:['"',s,'" için Arama Sonuçları']}),e("div",{className:"page-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Arama"})]})})})]}),l("div",{className:"row",children:[e("div",{className:"col-lg-8",children:e("section",{className:"search-results",children:c&&n===1?e("div",{className:"text-center py-5",children:e(ve,{})}):((w=(m=o==null?void 0:o.data)==null?void 0:m.data)==null?void 0:w.length)===0?l("div",{className:"alert alert-warning",children:[e("h4",{children:"Sonuç bulunamadı"}),l("p",{children:['"',s,'" için herhangi bir haber bulunamadı.']}),e("p",{children:"Öneriler:"}),l("ul",{children:[e("li",{children:"Farklı kelimeler deneyin"}),e("li",{children:"Daha genel terimler kullanın"}),e("li",{children:"Yazım hatası olup olmadığını kontrol edin"})]})]}):l(ie,{children:[(o==null?void 0:o.pagination)&&e("div",{className:"search-info mb-4",children:l("p",{className:"text-muted",children:[e("strong",{children:o.pagination.total_count})," sonuç bulundu",o.pagination.total_pages>1&&l("span",{children:[" (Sayfa ",o.pagination.current_page," / ",o.pagination.total_pages,")"]})]})}),e(Fe,{news:t}),h&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg",onClick:p,disabled:c,children:c?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Sonuç Yükle"})}),c&&n>1&&e("div",{className:"text-center py-3",children:e(ve,{})})]})})}),e("div",{className:"col-lg-4",children:e(fa,{})})]})]})]}):l("div",{className:"container",children:[l("div",{className:"page-header",children:[e("h1",{className:"page-title",children:"Arama"}),e("div",{className:"page-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Arama"})]})})})]}),e("div",{className:"alert alert-info",children:"Arama yapmak için bir kelime girin."})]})};const lr=()=>{const{cityName:r}=Mt(),[n,i]=T.useState(r||""),[t,a]=T.useState([]),[s,o]=T.useState({}),[c,u]=T.useState(1),d=20,p=["Aydın","İstanbul","Ankara","Bursa","İzmir"];T.useEffect(()=>{if(r){const C=decodeURIComponent(r);console.log("URL'den gelen şehir:",C),i(C)}else i("")},[r]),ye("cities-list",()=>fe.getCities(),{staleTime:6e5,onSuccess:C=>{C&&C.success&&Array.isArray(C.data)&&a(C.data)}});const{data:h,isLoading:v,error:b,refetch:m}=ye("local-news",()=>fe.getNews({limit:2e3,sort:"pub_date",order:"DESC"}),{staleTime:3e5,onSuccess:C=>{C&&C.success&&Array.isArray(C.data)&&$(C.data)}}),{data:w,isLoading:P,error:j}=ye(["selected-city-news",n],()=>(console.log("API çağrısı yapılıyor, şehir:",n),fe.getNews({sehir:n,limit:1e3,sort:"pub_date",order:"DESC"})),{enabled:!!n,staleTime:3e5,onSuccess:C=>{var B;console.log("API yanıtı:",C),console.log("Bulunan haber sayısı:",((B=C==null?void 0:C.data)==null?void 0:B.length)||0)}}),H=C=>C.toLowerCase().replace(/ı/g,"i").replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ö/g,"o").replace(/ç/g,"c").trim(),$=C=>{const B=C.filter(Z=>Z.sehir&&Z.sehir.trim()!==""),U={};p.forEach(Z=>{const _=H(Z),x=B.filter(R=>{if(!R.sehir)return!1;const W=H(R.sehir);return W===_||W.includes(_)||_.includes(W)}).sort((R,W)=>new Date(W.pub_date||W.created_at)-new Date(R.pub_date||R.created_at)).slice(0,8);x.length>0&&(U[Z]=x)}),o(U)},z=C=>{i(C.target.value),u(1)},L=C=>{u(C),window.scrollTo({top:0,behavior:"smooth"})},D=()=>{i(""),u(1)},ee=()=>{if(!n)return[];if(w!=null&&w.success&&Array.isArray(w.data)){const C=H(n);return w.data.filter(B=>{if(!B.sehir)return!1;const U=H(B.sehir);return U===C||U.includes(C)||C.includes(U)}).sort((B,U)=>new Date(U.pub_date||U.created_at)-new Date(B.pub_date||B.created_at))}return[]},q=()=>{const C=ee(),B=(c-1)*d,U=B+d;return C.slice(B,U)},K=()=>{const C=ee();return Math.ceil(C.length/d)};return v?e("div",{className:"local-news-container",children:e("div",{className:"container",children:l("div",{className:"loading-state",children:[e(ve,{}),e("p",{children:"Yerel haberler yükleniyor..."})]})})}):b?e("div",{className:"local-news-container",children:e("div",{className:"container",children:e(Be,{message:"Yerel haberler yüklenirken bir hata oluştu."})})}):l(ie,{children:[l(Se,{children:[e("title",{children:"Yerel Haberler - MetaAnaliz Haber"}),e("meta",{name:"description",content:"Türkiye'nin tüm şehirlerinden güncel yerel haberler. İl il, bölge bölge haberleri takip edin."}),e("meta",{name:"keywords",content:"yerel haberler, şehir haberleri, il haberleri, bölgesel haberler, Türkiye haberleri"})]}),l("div",{className:"local-news-page",children:[e("div",{className:"page-header",children:e("div",{className:"container",children:l("div",{className:"d-flex justify-content-between align-items-center",children:[l("div",{children:[l("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Yerel"}),e("span",{className:"category-subtitle",children:" Haberler"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item",children:e(V,{to:"/yerel-haberler",onClick:D,children:"Yerel Haberler"})}),n&&e("li",{className:"breadcrumb-item active",children:n}),!n&&e("li",{className:"breadcrumb-item active",children:"Yerel Haberler"})]})})})]}),l("div",{className:"city-filter-header",children:[e("label",{htmlFor:"city-select",className:"filter-label me-2",children:"Şehir Seçin:"}),l("select",{id:"city-select",className:"form-select city-select",value:n,onChange:z,style:{width:"200px"},children:[e("option",{value:"",children:"Şehir seçiniz"}),t.map(C=>e("option",{value:C,children:C},C))]})]})]})})}),l("div",{className:"container",children:[!n&&Object.keys(s).length>0&&e("div",{className:"main-cities-sections",children:Object.entries(s).map(([C,B])=>l("div",{className:"city-section",children:[l("div",{className:"section-header",children:[l("h2",{className:"section-title",children:[C," Haberleri"]}),e(V,{to:`/yerel-haberler/${encodeURIComponent(C)}`,className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid row",children:B.map(U=>e("div",{className:"news-grid-item",children:e($t,{news:U,variant:"default",showCategory:!0,showDate:!0})},U.haber_kodu))})]},C))}),n&&l("div",{className:"selected-city-news",children:[l("div",{className:"section-header",children:[l("h2",{className:"section-title",children:[n," Haberleri"]}),l("span",{className:"news-count",children:[ee().length," haber - Sayfa ",c,"/",K()]})]}),P?e("div",{className:"text-center py-5",children:e(ve,{text:"Haberler yükleniyor..."})}):j?l("div",{className:"alert alert-danger text-center",children:[e("i",{className:"fas fa-exclamation-triangle me-2"}),"Haberler yüklenirken bir hata oluştu."]}):q().length===0?l("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),n," için haber bulunamadı."]}):l(ie,{children:[e(Fe,{news:q(),showCategory:!0,variant:"local"}),K()>1&&e("div",{className:"pagination-container",children:e("nav",{"aria-label":"Sayfa navigasyonu",children:l("ul",{className:"pagination justify-content-center",children:[e("li",{className:`page-item ${c===1?"disabled":""}`,children:e("button",{className:"page-link",onClick:()=>L(c-1),disabled:c===1,children:"Önceki"})}),Array.from({length:K()},(C,B)=>B+1).map(C=>e("li",{className:`page-item ${c===C?"active":""}`,children:e("button",{className:"page-link",onClick:()=>L(C),children:C})},C)),e("li",{className:`page-item ${c===K()?"disabled":""}`,children:e("button",{className:"page-link",onClick:()=>L(c+1),disabled:c===K(),children:"Sonraki"})})]})})})]})]}),!n&&!v&&Object.keys(s).length===0&&e("div",{className:"no-news",children:e("p",{children:"Henüz yerel haber bulunmuyor."})})]})]})]})},pa=()=>{var h;const[r,n]=T.useState(1),[i,t]=T.useState([]),{setCurrentCategory:a}=Re(),s=Nr();T.useEffect(()=>(a("Video Haberler"),n(1),t([]),s.removeQueries(["video-news-page"]),()=>a("")),[a,s]);const{data:o,isLoading:c}=ye(["video-news-page",r],()=>fe.getVideoNews({page:r,limit:12}),{keepPreviousData:!0,enabled:!0,retry:3,retryDelay:1e3,staleTime:0,cacheTime:1e3*60*5,onSuccess:v=>{v.success&&Array.isArray(v.data)&&t(r===1?v.data:b=>[...b,...v.data])}}),u=(o==null?void 0:o.success)&&((h=o.data)==null?void 0:h.length)===12,d=c&&r===1,p=()=>{u&&!c&&n(v=>v+1)};return T.useEffect(()=>{window.scrollTo(0,0)},[]),l(ie,{children:[l(Se,{children:[e("title",{children:"Video Haberler - MetaAnaliz Haber"}),e("meta",{name:"description",content:"En güncel video haberler ve görüntülü haberler."})]}),l("div",{className:"category-page",children:[e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Video"}),e("span",{className:"category-subtitle",children:" Haberler"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Video Haberler"})]})})})]})}),e("div",{className:"category-news-section",children:e("div",{className:"container",children:d?e("div",{className:"text-center py-5",children:e(ve,{text:"Video haberler yükleniyor..."})}):i.length===0&&!c?l("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Şu anda video haber bulunmuyor."]}):l(ie,{children:[e(Fe,{news:i,showCategory:!0,variant:"video"}),u&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:p,disabled:c,children:c?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Video Haber Yükle"})}),c&&r>1&&e("div",{className:"text-center py-3",children:e(ve,{})})]})})})]})]})},ya=()=>{var v;const[r,n]=T.useState(1),[i,t]=T.useState([]),{setCurrentCategory:a}=Re();T.useEffect(()=>(a("Dünya Haberleri"),n(1),t([]),()=>a("")),[a]);const{data:s,isLoading:o,error:c,refetch:u}=ye(["world-news-page",r],()=>fe.getWorldNews({page:r,limit:12}),{keepPreviousData:!0,enabled:!0,retry:3,retryDelay:1e3,onSuccess:b=>{b.success&&Array.isArray(b.data)&&t(r===1?b.data:m=>[...m,...b.data])}}),d=(s==null?void 0:s.success)&&((v=s.data)==null?void 0:v.length)===12,p=o&&r===1,h=()=>{d&&!o&&n(b=>b+1)};return T.useEffect(()=>{window.scrollTo(0,0)},[]),l(ie,{children:[l(Se,{children:[e("title",{children:"Dünya Haberleri - MetaAnaliz Haber"}),e("meta",{name:"description",content:"Dünyadan en güncel haberler ve uluslararası gelişmeler."})]}),l("div",{className:"category-page",children:[e("div",{className:"page-header",children:l("div",{className:"container",children:[l("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Dünya"}),e("span",{className:"category-subtitle",children:" Haberleri"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:l("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(V,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Dünya Haberleri"})]})})})]})}),e("div",{className:"category-news-section",children:e("div",{className:"container",children:p?e("div",{className:"text-center py-5",children:e(ve,{text:"Dünya haberleri yükleniyor..."})}):i.length===0&&!o?l("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Şu anda dünya haberi bulunmuyor."]}):l(ie,{children:[e(Fe,{news:i,showCategory:!0,variant:"world"}),d&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:h,disabled:o,children:o?l(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Dünya Haberi Yükle"})}),o&&r>1&&e("div",{className:"text-center py-3",children:e(ve,{})})]})})})]})]})},va=()=>l(ie,{children:[l(Se,{children:[e("title",{children:"Sayfa Bulunamadı - MetaAnaliz Haber"}),e("meta",{name:"description",content:"Aradığınız sayfa bulunamadı."})]}),e("div",{className:"container",children:l("div",{className:"not-found-page text-center py-5",children:[e("p",{className:"error-message",children:"Aradığınız sayfa bulunamadı veya taşınmış olabilir."}),e("div",{className:"error-actions mt-4",children:l(V,{to:"/",className:"btn btn-primary btn-lg",children:[e("i",{className:"fas fa-home me-2"}),"Ana Sayfaya Dön"]})})]})})]});function ga(){return e(aa,{children:l("div",{className:"App",children:[l(Se,{children:[e("title",{children:"MetaAnaliz Haber - Güncel Haberler"}),e("meta",{name:"description",content:"Türkiye'den ve dünyadan en güncel haberler"})]}),e(ta,{}),l("main",{className:"main-content",children:[e(na,{}),l(Dr,{children:[e(Pe,{path:"/",element:e(sa,{})}),e(Pe,{path:"/kategori/:categoryName",element:e(oa,{})}),e(Pe,{path:"/kategori/video",element:e(ca,{})}),e(Pe,{path:"/son-haberler",element:e(la,{})}),e(Pe,{path:"/cron-monitor",element:e(ua,{})}),e(Pe,{path:"/yerel-haberler",element:e(lr,{})}),e(Pe,{path:"/yerel-haberler/:cityName",element:e(lr,{})}),e(Pe,{path:"/video-haberler",element:e(pa,{})}),e(Pe,{path:"/dunya-haberleri",element:e(ya,{})}),e(Pe,{path:"/haber/:newsSlug",element:e(ha,{})}),e(Pe,{path:"/arama",element:e(ma,{})}),e(Pe,{path:"*",element:e(va,{})})]})]}),e(ra,{})]})})}const ba=new hn({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:1,staleTime:3e5}}});Pt.createRoot(document.getElementById("root")).render(e(oe.StrictMode,{children:e(Fr,{children:e(vn,{client:ba,children:e(Ue,{children:e(ga,{})})})})}));
