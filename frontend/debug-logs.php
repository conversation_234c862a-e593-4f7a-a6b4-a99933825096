<?php
// Debug log'larını görüntülemek için basit script

$logFile = '/tmp/php_errors.log';
if (!file_exists($logFile)) {
    $logFile = ini_get('error_log');
}

echo "<h2>PHP Error Log - Son 50 satır</h2>";
echo "<pre>";

if (file_exists($logFile)) {
    $lines = file($logFile);
    $lastLines = array_slice($lines, -50);
    foreach ($lastLines as $line) {
        if (strpos($line, 'API Response') !== false || 
            strpos($line, 'Images for slug') !== false || 
            strpos($line, 'Meta tags') !== false ||
            strpos($line, 'Extracted slug') !== false) {
            echo htmlspecialchars($line);
        }
    }
} else {
    echo "Log dosyası bulunamadı: " . $logFile;
}

echo "</pre>";

// Test URL'si
$testSlug = 'gaziantep-te-hali-fabrikasindaki-yangin-sonduruldu';
echo "<h3>Test URL'si:</h3>";
echo "<a href='https://metaanalizhaber.com/haber/{$testSlug}' target='_blank'>https://metaanalizhaber.com/haber/{$testSlug}</a>";

echo "<h3>Facebook Debugger:</h3>";
echo "<a href='https://developers.facebook.com/tools/debug/?q=" . urlencode("https://metaanalizhaber.com/haber/{$testSlug}") . "' target='_blank'>Facebook Debug</a>";
?>
