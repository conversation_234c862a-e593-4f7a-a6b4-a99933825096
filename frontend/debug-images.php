<?php
/**
 * Debug: Haber görsellerini kontrol et
 */

// Son 20 haberi al ve görsel durumlarını kontrol et
function getLatestNews() {
    try {
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news&limit=20";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            return null;
        }

        $data = json_decode($response, true);
        
        if ($data && $data['success'] && isset($data['data'])) {
            return $data['data'];
        }
        
        return null;
    } catch (Exception $e) {
        return null;
    }
}

// Haber detayını al
function getNewsDetail($slug) {
    try {
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            return null;
        }

        $data = json_decode($response, true);
        
        if ($data && $data['success'] && isset($data['data'])) {
            return $data['data'];
        }
        
        return null;
    } catch (Exception $e) {
        return null;
    }
}

// Görsel URL'sini kontrol et
function checkImageUrl($url) {
    $headers = @get_headers($url, 1);
    if (!$headers) {
        return ['status' => 'ERROR', 'message' => 'Headers alınamadı'];
    }
    
    $status = $headers[0];
    if (strpos($status, '200') !== false) {
        return ['status' => 'OK', 'message' => 'Erişilebilir'];
    } else {
        return ['status' => 'ERROR', 'message' => $status];
    }
}

// Görsel boyutlarını al
function getImageDimensions($url) {
    $size = @getimagesize($url);
    if ($size) {
        return ['width' => $size[0], 'height' => $size[1], 'type' => $size['mime']];
    }
    return null;
}

$latestNews = getLatestNews();

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Haber Görselleri Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .ok { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .small { font-size: 12px; }
        img { max-width: 100px; max-height: 60px; }
    </style>
</head>
<body>
    <h1>🔍 Haber Görselleri Debug</h1>
    
    <?php if ($latestNews): ?>
        <table>
            <thead>
                <tr>
                    <th>Haber Başlığı</th>
                    <th>Slug</th>
                    <th>Görsel Durumu</th>
                    <th>Görsel URL</th>
                    <th>Boyut</th>
                    <th>Erişim</th>
                    <th>Önizleme</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($latestNews as $news): ?>
                    <?php 
                    $detail = getNewsDetail($news['slug']);
                    $images = $detail['images'] ?? [];
                    $hasImages = !empty($images);
                    $imageUrl = $hasImages ? $images[0] : null;
                    $imageCheck = $imageUrl ? checkImageUrl($imageUrl) : null;
                    $imageSize = $imageUrl ? getImageDimensions($imageUrl) : null;
                    ?>
                    <tr>
                        <td class="small">
                            <a href="/haber/<?= htmlspecialchars($news['slug']) ?>" target="_blank">
                                <?= htmlspecialchars(substr($news['title'], 0, 50)) ?>...
                            </a>
                        </td>
                        <td class="small"><?= htmlspecialchars($news['slug']) ?></td>
                        <td>
                            <?php if ($hasImages): ?>
                                <span class="ok">✅ Var (<?= count($images) ?>)</span>
                            <?php else: ?>
                                <span class="error">❌ Yok</span>
                            <?php endif; ?>
                        </td>
                        <td class="small">
                            <?php if ($imageUrl): ?>
                                <a href="<?= htmlspecialchars($imageUrl) ?>" target="_blank">
                                    <?= htmlspecialchars(substr($imageUrl, -30)) ?>
                                </a>
                            <?php else: ?>
                                <span class="error">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="small">
                            <?php if ($imageSize): ?>
                                <?= $imageSize['width'] ?>x<?= $imageSize['height'] ?>
                                <br><span style="font-size: 10px;"><?= $imageSize['type'] ?></span>
                                <?php if ($imageSize['width'] < 300 || $imageSize['height'] < 157): ?>
                                    <br><span class="warning">⚠️ Küçük</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="error">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($imageCheck): ?>
                                <?php if ($imageCheck['status'] === 'OK'): ?>
                                    <span class="ok">✅ OK</span>
                                <?php else: ?>
                                    <span class="error">❌ <?= $imageCheck['message'] ?></span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="error">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($imageUrl && $imageCheck && $imageCheck['status'] === 'OK'): ?>
                                <img src="<?= htmlspecialchars($imageUrl) ?>" alt="Önizleme">
                            <?php else: ?>
                                <span class="error">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <h2>📊 Özet</h2>
        <?php
        $totalNews = count($latestNews);
        $newsWithImages = 0;
        $accessibleImages = 0;
        $validSizeImages = 0;
        
        foreach ($latestNews as $news) {
            $detail = getNewsDetail($news['slug']);
            $images = $detail['images'] ?? [];
            if (!empty($images)) {
                $newsWithImages++;
                $imageUrl = $images[0];
                $imageCheck = checkImageUrl($imageUrl);
                if ($imageCheck && $imageCheck['status'] === 'OK') {
                    $accessibleImages++;
                    $imageSize = getImageDimensions($imageUrl);
                    if ($imageSize && $imageSize['width'] >= 300 && $imageSize['height'] >= 157) {
                        $validSizeImages++;
                    }
                }
            }
        }
        ?>
        <ul>
            <li><strong>Toplam Haber:</strong> <?= $totalNews ?></li>
            <li><strong>Görseli Olan:</strong> <?= $newsWithImages ?> (<?= round($newsWithImages/$totalNews*100) ?>%)</li>
            <li><strong>Erişilebilir Görsel:</strong> <?= $accessibleImages ?> (<?= round($accessibleImages/$totalNews*100) ?>%)</li>
            <li><strong>Twitter Uyumlu Boyut:</strong> <?= $validSizeImages ?> (<?= round($validSizeImages/$totalNews*100) ?>%)</li>
        </ul>
        
    <?php else: ?>
        <p class="error">❌ Haberler yüklenemedi!</p>
    <?php endif; ?>
    
    <hr>
    <p><small>Bu sayfa haberlerin görsel durumlarını kontrol eder. Twitter için minimum 300x157 piksel gereklidir.</small></p>
</body>
</html>
