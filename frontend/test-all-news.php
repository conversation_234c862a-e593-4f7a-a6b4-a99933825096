<?php
/**
 * Tüm Haberlerin Twitter Meta Tag Test Sayfası
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Tüm Haberlerin Twitter Meta Tag Testi</h1>";

// Backend API'den son haberleri çek
function getLatestNews() {
    try {
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_homepage_data";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            echo "<div style='color: red;'>❌ API isteği başarısız!</div>";
            return null;
        }

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data']['latest_news'])) {
            return $data['data']['latest_news'];
        }
        
        return null;
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Exception: " . $e->getMessage() . "</div>";
        return null;
    }
}

// Haber verilerini çek
function getNewsDataBySlug($slug) {
    try {
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            return null;
        }

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data'])) {
            return $data['data'];
        }
        
        return null;
    } catch (Exception $e) {
        return null;
    }
}

// Son haberleri al
$latestNews = getLatestNews();

if ($latestNews && is_array($latestNews)) {
    echo "<h3>📰 Son " . count($latestNews) . " Haber Twitter Meta Tag Kontrolü:</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Başlık</th>";
    echo "<th>Slug</th>";
    echo "<th>Görsel Sayısı</th>";
    echo "<th>Görsel URL</th>";
    echo "<th>Görsel Test</th>";
    echo "<th>Twitter Test</th>";
    echo "</tr>";
    
    foreach ($latestNews as $index => $news) {
        if ($index >= 10) break; // İlk 10 haberi test et
        
        $slug = $news['slug'] ?? '';
        $title = $news['title'] ?? 'Başlık Yok';
        
        // Haber detayını çek
        $newsDetail = getNewsDataBySlug($slug);
        
        echo "<tr>";
        echo "<td style='padding: 5px; max-width: 200px;'>" . htmlspecialchars(substr($title, 0, 50)) . "...</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($slug) . "</td>";
        
        if ($newsDetail) {
            $images = $newsDetail['images'] ?? [];
            $imageCount = count($images);
            
            echo "<td style='padding: 5px; text-align: center;'>" . $imageCount . "</td>";
            
            if ($imageCount > 0) {
                $firstImage = $images[0];
                $imageUrl = '';
                
                if (is_string($firstImage)) {
                    $imageUrl = $firstImage;
                } elseif (is_array($firstImage) && isset($firstImage['url'])) {
                    $imageUrl = $firstImage['url'];
                }
                
                // Mutlak URL yap
                if (!empty($imageUrl) && strpos($imageUrl, 'http') !== 0) {
                    if (strpos($imageUrl, '/') === 0) {
                        $imageUrl = 'https://metaanalizhaber.com' . $imageUrl;
                    } else {
                        $imageUrl = 'https://metaanalizhaber.com/' . $imageUrl;
                    }
                }
                
                echo "<td style='padding: 5px; max-width: 300px; word-break: break-all;'>";
                if (!empty($imageUrl)) {
                    echo "<a href='" . htmlspecialchars($imageUrl) . "' target='_blank'>" . htmlspecialchars(substr($imageUrl, -30)) . "</a>";
                } else {
                    echo "<span style='color: red;'>❌ URL Yok</span>";
                }
                echo "</td>";
                
                echo "<td style='padding: 5px; text-align: center;'>";
                if (!empty($imageUrl)) {
                    // Görsel erişilebilirlik testi
                    $headers = @get_headers($imageUrl);
                    if ($headers && strpos($headers[0], '200') !== false) {
                        echo "<span style='color: green;'>✅</span>";
                    } else {
                        echo "<span style='color: red;'>❌</span>";
                    }
                } else {
                    echo "<span style='color: red;'>❌</span>";
                }
                echo "</td>";
            } else {
                echo "<td style='padding: 5px; color: red;'>Görsel Yok</td>";
                echo "<td style='padding: 5px; color: red;'>❌</td>";
            }
        } else {
            echo "<td style='padding: 5px; color: red;'>API Hatası</td>";
            echo "<td style='padding: 5px; color: red;'>❌</td>";
            echo "<td style='padding: 5px; color: red;'>❌</td>";
        }
        
        // Twitter test linki
        $newsUrl = "https://metaanalizhaber.com/haber/" . urlencode($slug);
        echo "<td style='padding: 5px;'>";
        echo "<a href='twitter-debug.php?slug=" . urlencode($slug) . "' target='_blank' style='font-size: 10px;'>🐦 Test</a>";
        echo "</td>";
        
        echo "</tr>";
    }
    
    echo "</table>";
    
} else {
    echo "<div style='color: red;'>❌ Haberler alınamadı!</div>";
}

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin-top: 20px; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";
?>
