{"name": "metaanaliz-haber-frontend", "version": "1.0.0", "description": "MetaAnaliz <PERSON> - Modern React Frontend", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"axios": "^1.3.0", "classnames": "^2.3.2", "date-fns": "^2.29.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-intersection-observer": "^9.4.0", "react-lazy-load-image-component": "^1.5.6", "react-query": "^3.39.0", "react-router-dom": "^6.8.0", "remixicon-react": "^1.0.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "vite": "^4.1.0"}, "keywords": ["react", "news", "frontend", "metaanaliz", "haber"], "author": "MetaAnal<PERSON>", "license": "MIT"}