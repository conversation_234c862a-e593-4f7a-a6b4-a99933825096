<?php
/**
 * Root Index - React Router Desteği
 */



$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestPath = parse_url($requestUri, PHP_URL_PATH);



// Backend API istekleri - sadece yönlendir
if (strpos($requestPath, '/backend/') === 0) {
    $backendPath = substr($requestPath, 9); // '/backend/' kısmını çıkar
    $backendFile = __DIR__ . '/backend/' . $backendPath;

    if (file_exists($backendFile) && is_file($backendFile)) {
        // PHP dosyası ise include et
        if (pathinfo($backendFile, PATHINFO_EXTENSION) === 'php') {
            chdir(__DIR__ . '/backend');
            include $backendFile;
        } else {
            readfile($backendFile);
        }
        exit;
    }

    http_response_code(404);
    echo "Backend file not found: " . $backendPath;
    exit;
}

// Assets dosyaları - root'ta
if (strpos($requestPath, '/assets/') === 0) {
    $assetFile = __DIR__ . $requestPath;  // Direkt root'tan al

    if (file_exists($assetFile) && is_file($assetFile)) {
        // MIME type belirle
        $extension = strtolower(pathinfo($assetFile, PATHINFO_EXTENSION));
        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'ico' => 'image/x-icon'
        ];

        if (isset($mimeTypes[$extension])) {
            header('Content-Type: ' . $mimeTypes[$extension]);
        }

        // Cache headers
        header('Cache-Control: public, max-age=31536000');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');

        readfile($assetFile);
        exit;
    }

    http_response_code(404);
    echo "Asset not found";
    exit;
}

// Favicon - root'ta
if ($requestPath === '/favicon.ico') {
    $faviconFile = __DIR__ . '/favicon.ico';
    if (file_exists($faviconFile)) {
        header('Content-Type: image/x-icon');
        readfile($faviconFile);
    } else {
        http_response_code(404);
    }
    exit;
}

// React Router için - Dinamik Meta Tag Injection
// Sunucudaki dosya yapısına göre index.html'i bul
$frontendIndex = __DIR__ . '/index.html';

// Debug: Dosya yolunu logla


if (!file_exists($frontendIndex)) {
    http_response_code(500);
    echo "Frontend index.html not found at: " . htmlspecialchars($frontendIndex);
    echo "<br>Current directory: " . htmlspecialchars(__DIR__);
    echo "<br>Directory contents:<br>";
    if (is_dir(__DIR__)) {
        $files = scandir(__DIR__);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                echo "- " . htmlspecialchars($file) . (is_dir(__DIR__ . '/' . $file) ? ' (DIR)' : '') . "<br>";
            }
        }
    }
    exit;
}

// Haber detay sayfası için dinamik meta tag'ler (X/Twitter bot'ları için)
error_log("Request Path: " . $requestPath);

if (preg_match('/^\/haber\/(.+)$/', $requestPath, $matches)) {
    $newsSlug = $matches[1];
    error_log("News Slug: " . $newsSlug);

    // Haber bilgilerini veritabanından çek
    $newsData = getNewsDataBySlug($newsSlug);
    error_log("News Data: " . json_encode($newsData));

    if ($newsData) {
        // index.html'i oku ve meta tag'leri değiştir
        $html = file_get_contents($frontendIndex);

        // Meta tag'leri inject et
        $html = injectNewsMetaTags($html, $newsData);

        header('Content-Type: text/html; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        echo $html;
        exit;
    } else {
        error_log("News data not found for slug: " . $newsSlug);
    }
} else {
    error_log("Regex did not match for path: " . $requestPath);
}

// Normal sayfa - statik index.html
header('Content-Type: text/html; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
readfile($frontendIndex);

// Haber verilerini çeken fonksiyon
function getNewsDataBySlug($slug) {
    try {
        // Backend API'ye istek at
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);

        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            return null;
        }

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data'])) {
            return $data['data'];
        }

        return null;
    } catch (Exception $e) {
        return null;
    }
}

// Meta tag injection fonksiyonu
function injectNewsMetaTags($html, $newsData) {
    $title = htmlspecialchars($newsData['title'] ?? 'MetaAnaliz Haber');
    $description = htmlspecialchars(strip_tags($newsData['description'] ?? ''));
    $description = substr($description, 0, 160) . (strlen($description) > 160 ? '...' : '');
    $url = 'https://metaanalizhaber.com/haber/' . ($newsData['slug'] ?? '');

    // Görsel URL'sini al
    $image = 'https://metaanalizhaber.com/logo7.webp'; // Default logo
    $images = $newsData['images'] ?? [];

    if (!empty($images) && is_array($images) && !empty($images[0])) {
        $originalImage = $images[0];

        // Twitter artık WEBP formatını destekliyor, direkt kullan
        $image = $originalImage;
    }

    // Meta tag'leri head'in başına ekle (X/Twitter için kritik)
    $socialMetaTags =
        '<!-- Essential meta tags for X/Twitter -->' . "\n" .
        '<meta name="twitter:card" content="summary_large_image">' . "\n" .
        '<meta name="twitter:title" content="' . $title . '">' . "\n" .
        '<meta name="twitter:description" content="' . $description . '">' . "\n" .
        '<meta name="twitter:image" content="' . htmlspecialchars($image) . '">' . "\n" .
        '<meta name="twitter:image:alt" content="' . $title . '">' . "\n" .
        '<!-- Open Graph meta tags -->' . "\n" .
        '<meta property="og:title" content="' . $title . '">' . "\n" .
        '<meta property="og:description" content="' . $description . '">' . "\n" .
        '<meta property="og:type" content="article">' . "\n" .
        '<meta property="og:url" content="' . htmlspecialchars($url) . '">' . "\n" .
        '<meta property="og:image" content="' . htmlspecialchars($image) . '">' . "\n" .
        '<meta property="og:image:width" content="1200">' . "\n" .
        '<meta property="og:image:height" content="630">' . "\n" .
        '<meta property="og:image:type" content="image/webp">' . "\n" .
        '<meta property="og:site_name" content="MetaAnaliz Haber">' . "\n";

    // Head'in başına ekle
    $html = str_replace('<head>', '<head>' . "\n" . $socialMetaTags, $html);

    // Title'ı güncelle
    $html = preg_replace('/<title>.*?<\/title>/', '<title>' . $title . '</title>', $html);

    // Meta tag'ler zaten head'in başına eklendi

    return $html;
}


