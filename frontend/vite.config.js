import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, existsSync, mkdirSync } from 'fs'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    {
      name: 'copy-assets',
      writeBundle() {
        const assetsDir = resolve(__dirname, 'dist/assets/images')
        if (!existsSync(assetsDir)) {
          mkdirSync(assetsDir, { recursive: true })
        }

        // Copy placeholder images
        const srcDir = resolve(__dirname, 'src/assets/images')
        const files = ['placeholder.svg', 'placeholder-news.svg', 'logo7web.svg']

        files.forEach(file => {
          const src = resolve(srcDir, file)
          const dest = resolve(assetsDir, file)
          if (existsSync(src)) {
            copyFileSync(src, dest)
          }
        })
      }
    }
  ],
  server: {
    port: 3000,
    proxy: {
      '/backend': {
        target: 'https://metaanalizhaber.com',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          utils: ['axios', 'date-fns']
        }
      }
    }
  },
  base: '/',
  publicDir: 'public'
})
