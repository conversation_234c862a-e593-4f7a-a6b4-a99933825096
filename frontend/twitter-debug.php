<?php
/**
 * Twitter Meta Tag Debug Sayfası
 * X (Twitter) paylaşım sorunlarını debug etmek için
 */

// Debug için hata raporlamayı aç
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test için örnek haber slug'ı
$testSlug = $_GET['slug'] ?? 'test-haberi';

echo "<h1>🐦 Twitter Meta Tag Debug - Slug: " . htmlspecialchars($testSlug) . "</h1>";

// Haber verilerini çek
function getNewsDataBySlug($slug) {
    try {
        // Backend API'ye istek at
        $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);
        
        echo "<h3>📡 API İsteği:</h3>";
        echo "<code>" . htmlspecialchars($apiUrl) . "</code><br><br>";

        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz/1.0)\r\n"
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            echo "<div style='color: red;'>❌ API isteği başarısız!</div>";
            echo "<div>Last error: " . print_r(error_get_last(), true) . "</div>";
            return null;
        }

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data'])) {
            echo "<div style='color: green;'>✅ API isteği başarılı!</div>";
            return $data['data'];
        } else {
            echo "<div style='color: red;'>❌ API response hatası!</div>";
            echo "<pre>" . htmlspecialchars(print_r($data, true)) . "</pre>";
            return null;
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Exception: " . $e->getMessage() . "</div>";
        return null;
    }
}

// Twitter meta tag'lerini oluştur
function generateTwitterMetaTags($newsData) {
    if (!$newsData) return '';
    
    $title = htmlspecialchars($newsData['title'] ?? 'Haber');

    // Description'dan HTML tag'leri temizle
    $rawDescription = $newsData['description'] ?? '';
    $cleanDescription = strip_tags($rawDescription); // HTML tag'leri kaldır
    $cleanDescription = html_entity_decode($cleanDescription, ENT_QUOTES, 'UTF-8'); // HTML entity'leri decode et
    $cleanDescription = preg_replace('/\s+/', ' ', $cleanDescription); // Çoklu boşlukları tek boşluk yap
    $cleanDescription = trim($cleanDescription); // Başındaki ve sonundaki boşlukları kaldır
    $description = htmlspecialchars(substr($cleanDescription, 0, 160) . (strlen($cleanDescription) > 160 ? '...' : ''));
    $images = $newsData['images'] ?? [];
    
    // Görsel URL'sini belirle
    $image = 'https://metaanalizhaber.com/logo7.webp'; // Default
    
    if (!empty($images) && is_array($images)) {
        $firstImage = $images[0];
        
        if (is_string($firstImage)) {
            $imageUrl = $firstImage;
        } elseif (is_array($firstImage) && isset($firstImage['url'])) {
            $imageUrl = $firstImage['url'];
        } else {
            $imageUrl = null;
        }
        
        if (!empty($imageUrl)) {
            // Mutlak URL yap
            if (strpos($imageUrl, 'http') !== 0) {
                if (strpos($imageUrl, '/') === 0) {
                    $imageUrl = 'https://metaanalizhaber.com' . $imageUrl;
                } else {
                    $imageUrl = 'https://metaanalizhaber.com/' . $imageUrl;
                }
            }
            $image = $imageUrl;
        }
    }
    
    $url = "https://metaanalizhaber.com/haber/" . ($newsData['slug'] ?? '');
    
    return [
        'title' => $title,
        'description' => $description,
        'image' => $image,
        'url' => $url
    ];
}

// Haber verilerini al
$newsData = getNewsDataBySlug($testSlug);

if ($newsData) {
    echo "<h3>📰 Haber Verisi:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<strong>Başlık:</strong> " . htmlspecialchars($newsData['title'] ?? 'N/A') . "<br>";
    echo "<strong>Açıklama:</strong> " . htmlspecialchars(substr($newsData['description'] ?? '', 0, 100)) . "...<br>";
    echo "<strong>Görsel Sayısı:</strong> " . count($newsData['images'] ?? []) . "<br>";
    echo "</div><br>";
    
    // Twitter meta tag'lerini oluştur
    $twitterMeta = generateTwitterMetaTags($newsData);
    
    echo "<h3>🐦 Twitter Meta Tag'leri:</h3>";
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; font-family: monospace;'>";
    echo '&lt;meta name="twitter:card" content="summary_large_image"&gt;<br>';
    echo '&lt;meta name="twitter:domain" content="https://metaanalizhaber.com/"&gt;<br>';
    echo '&lt;meta name="twitter:url" content="' . $twitterMeta['url'] . '"&gt;<br>';
    echo '&lt;meta name="twitter:title" content="' . $twitterMeta['title'] . '"&gt;<br>';
    echo '&lt;meta name="twitter:description" content="' . $twitterMeta['description'] . '"&gt;<br>';
    echo '&lt;meta name="twitter:image" content="' . $twitterMeta['image'] . '"&gt;<br>';
    echo '&lt;meta name="twitter:image:alt" content="' . $twitterMeta['title'] . '"&gt;<br>';
    echo "</div><br>";

    // Görsel kontrolü
    echo "<h3>🖼️ Görsel Kontrolü:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<strong>Görsel URL:</strong> <a href='" . $twitterMeta['image'] . "' target='_blank'>" . $twitterMeta['image'] . "</a><br>";

    // Görsel boyutunu kontrol et
    $imageInfo = @getimagesize($twitterMeta['image']);
    if ($imageInfo) {
        echo "<strong>Görsel Boyutu:</strong> " . $imageInfo[0] . "x" . $imageInfo[1] . " px<br>";
        echo "<strong>Görsel Tipi:</strong> " . image_type_to_mime_type($imageInfo[2]) . "<br>";

        // Twitter gereksinimleri kontrolü
        if ($imageInfo[0] >= 300 && $imageInfo[1] >= 157) {
            echo "<span style='color: green;'>✅ Boyut uygun (min 300x157px)</span><br>";
        } else {
            echo "<span style='color: red;'>❌ Boyut çok küçük (min 300x157px gerekli)</span><br>";
        }

        if ($imageInfo[0] <= 4096 && $imageInfo[1] <= 4096) {
            echo "<span style='color: green;'>✅ Maksimum boyut uygun (max 4096x4096px)</span><br>";
        } else {
            echo "<span style='color: red;'>❌ Boyut çok büyük (max 4096x4096px)</span><br>";
        }
    } else {
        echo "<span style='color: red;'>❌ Görsel erişilebilir değil veya geçersiz format!</span><br>";
    }

    echo "<img src='" . $twitterMeta['image'] . "' style='max-width: 300px; max-height: 200px; border: 1px solid #ddd; margin-top: 10px;' onerror='this.style.display=\"none\"; this.nextSibling.style.display=\"block\";'>";
    echo "<div style='display: none; color: red; margin-top: 10px;'>❌ Görsel yüklenemedi!</div>";
    echo "</div><br>";
    
    echo "<h3>🔗 Test Linkleri:</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 15px;'>";
    echo "<h4>🐦 Twitter Card Validator Test:</h4>";
    echo "<ol>";
    echo "<li><a href='https://cards-dev.twitter.com/validator' target='_blank'><strong>Twitter Card Validator'ı Aç</strong></a></li>";
    echo "<li>Bu URL'yi yapıştır: <code>" . $twitterMeta['url'] . "</code></li>";
    echo "<li>'Preview card' butonuna bas</li>";
    echo "<li>Sonuçları kontrol et</li>";
    echo "</ol>";
    echo "</div>";

    echo "<ul>";
    echo "<li><a href='https://developers.facebook.com/tools/debug/?q=" . urlencode($twitterMeta['url']) . "' target='_blank'>📘 Facebook Debugger</a></li>";
    echo "<li><a href='" . $twitterMeta['url'] . "' target='_blank'>🔗 Haber Sayfası</a></li>";
    echo "</ul>";

    echo "<h3>⚠️ Twitter Cache Sorunu:</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<strong>Twitter 7 güne kadar cache tutar!</strong><br>";
    echo "• Eğer görsel gelmiyorsa, URL'ye <code>?v=" . time() . "</code> ekle<br>";
    echo "• Veya Twitter Card Validator'da 'Preview card' butonuna bas<br>";
    echo "• Cache temizlenmesi 15-30 dakika sürebilir";
    echo "</div><br>";

    echo "<h3>🔄 Cache Bypass URL:</h3>";
    $cacheBypassUrl = $twitterMeta['url'] . '?v=' . time();
    echo "<code>" . htmlspecialchars($cacheBypassUrl) . "</code><br>";
    echo "<a href='{$cacheBypassUrl}' target='_blank' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-top: 10px; display: inline-block;'>🔄 Cache Bypass URL'yi Aç</a><br><br>";
    
    echo "<h3>📱 Twitter Paylaşım Linki:</h3>";
    $tweetText = urlencode($twitterMeta['title']);
    $tweetUrl = urlencode($twitterMeta['url']);
    $twitterShareUrl = "https://twitter.com/intent/tweet?text={$tweetText}&url={$tweetUrl}";
    echo "<a href='{$twitterShareUrl}' target='_blank' style='background: #1da1f2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🐦 Twitter'da Paylaş</a>";
    
} else {
    echo "<div style='color: red;'>❌ Haber verisi alınamadı!</div>";
}

echo "<hr>";
echo "<h3>🧪 Farklı Slug'lar ile Test:</h3>";
echo "<ul>";
echo "<li><a href='?slug=test-haberi'>Test Haberi</a></li>";
echo "<li><a href='?slug=guncel-haber'>Güncel Haber</a></li>";
echo "<li><a href='?slug=spor-haberi'>Spor Haberi</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
h1, h2, h3 { color: #333; }
code { background: #f5f5f5; padding: 2px 5px; border-radius: 3px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
ul { line-height: 1.6; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
