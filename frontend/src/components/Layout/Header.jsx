import { useState } from 'react';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { useQuery } from 'react-query';
import BreakingNewsBar from '../News/BreakingNewsBar';
import CurrencyTicker from '../News/CurrencyTicker';
import { createSlug, fixCategoryName } from '../../utils/urlUtils';
import { newsAPI } from '../../services/api';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Fetch categories
  const { data: categoriesData } = useQuery(
    'categories',
    () => newsAPI.getCategories(),
    {
      staleTime: 600000, // 10 minutes
    }
  );

  // Kategorileri özel sıralama ile düzenle
  const topCategories = categoriesData?.success && categoriesData.data
    ? (() => {
        const categories = [...categoriesData.data];

        // Debug: Kategori isimlerini konsola yazdır
        console.log('Available categories:', categories.map(cat => cat.name || cat));

        // Öncelikli kategoriler sırası - API'den gelen gerçek isimlerle
        const priorityOrder = ['GENEL', 'VİDEO HABERLER', 'DÜNYA', 'ASAYİŞ'];

        // Öncelikli kategorileri ayır
        const priorityCategories = [];
        const otherCategories = [];

        categories.forEach(category => {
          const categoryName = (category.name || category).toUpperCase();
          const priorityIndex = priorityOrder.indexOf(categoryName);

          if (priorityIndex !== -1) {
            priorityCategories[priorityIndex] = category;
          } else {
            otherCategories.push(category);
          }
        });

        // Boş slotları temizle ve diğer kategorileri ekle
        const sortedPriorityCategories = priorityCategories.filter(Boolean);

        console.log('Sorted categories:', [...sortedPriorityCategories, ...otherCategories].map(cat => cat.name || cat));

        return [...sortedPriorityCategories, ...otherCategories];
      })()
    : [];
  
  return (
    <>
      <header className="header-main">
        <div className="container">
          {/* Main Header */}
          <div className="main-header">
            {/* Hamburger Menu - Sol taraf */}
            <button
              className="hamburger-menu"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              aria-label="Menüyü aç/kapat"
            >
              <span></span>
              <span></span>
              <span></span>
            </button>

            {/* Logo - Merkez */}
            <div className="logo">
              <Link to="/">
                <img src="/logo7.webp" alt="MetaAnaliz Haber" className="logo-image" style={{height: '50px'}} />
              </Link>
            </div>
          </div>
        
        {/* Navigation */}
        <nav className="main-nav">
          <div className="nav-container">
            <ul className={`nav-menu ${isMobileMenuOpen ? 'active' : ''}`}>
              {/* Genel kategorisi */}
              <li className="nav-item">
                <NavLink
                  to="/kategori/genel"
                  className="nav-link"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  GENEL
                </NavLink>
              </li>

              {/* Video Haberler */}
              <li className="nav-item">
                <NavLink
                  to="/video-haberler"
                  className="nav-link"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  VİDEO HABERLER
                </NavLink>
              </li>

              {/* Dünya Haberleri */}
              <li className="nav-item">
                <NavLink
                  to="/dunya-haberleri"
                  className="nav-link"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  DÜNYA
                </NavLink>
              </li>

              {/* Diğer kategoriler */}
              {topCategories.filter(category =>
                !['GENEL', 'Genel'].includes(category.name)
              ).map((category) => (
                <li key={category.name} className="nav-item">
                  <NavLink
                    to={`/kategori/${createSlug(category.name)}`}
                    className="nav-link"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {fixCategoryName(category.name)}
                  </NavLink>
                </li>
              ))}

              <li className="nav-item">
                <NavLink
                  to="/yerel-haberler"
                  className="nav-link"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  YEREL HABERLER
                </NavLink>
              </li>
            </ul>

            {/* Mobile Menu Toggle */}
            <div
              className="mobile-menu-toggle"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Menüyü aç/kapat"
            >
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </nav>

        {/* Breaking News Bar */}
        <BreakingNewsBar />

        {/* Currency Ticker */}
        <CurrencyTicker />
      </div>
    </header>

    {/* Sidebar */}
    <div className={`sidebar-overlay ${isSidebarOpen ? 'active' : ''}`} onClick={() => setIsSidebarOpen(false)}>
      <div className={`sidebar ${isSidebarOpen ? 'active' : ''}`} onClick={(e) => e.stopPropagation()}>
        <div className="sidebar-header">
          <h3>Kategoriler</h3>
          <button className="sidebar-close" onClick={() => setIsSidebarOpen(false)}>
            <i className="fas fa-times"></i>
          </button>
        </div>
        <div className="sidebar-content">
          <ul className="sidebar-menu">
            <li>
              <Link to="/" onClick={() => setIsSidebarOpen(false)}>
                <i className="fas fa-home"></i> Ana Sayfa
              </Link>
            </li>
            {/* Genel kategorisi */}
            <li>
              <Link
                to="/kategori/genel"
                onClick={() => setIsSidebarOpen(false)}
              >
                GENEL
              </Link>
            </li>

            {/* Video Haberler */}
            <li>
              <Link
                to="/video-haberler"
                onClick={() => setIsSidebarOpen(false)}
              >
                VİDEO HABERLER
              </Link>
            </li>

            {/* Dünya Haberleri */}
            <li>
              <Link
                to="/dunya-haberleri"
                onClick={() => setIsSidebarOpen(false)}
              >
                DÜNYA
              </Link>
            </li>

            {/* Diğer kategoriler - Genel hariç topCategories sıralamasını kullan */}
            {topCategories.length > 0 ? (
              topCategories.filter(category =>
                !['GENEL', 'Genel'].includes(category.name)
              ).map((category) => (
                <li key={category.name}>
                  <Link
                    to={`/kategori/${createSlug(category.name)}`}
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    {fixCategoryName(category.name)}
                  </Link>
                </li>
              ))
            ) : (
              <li><span>Kategoriler yükleniyor...</span></li>
            )}

            {/* Yerel Haberler */}
            <li>
              <Link
                to="/yerel-haberler"
                onClick={() => setIsSidebarOpen(false)}
              >
                YEREL HABERLER
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
    </>
  );
};

export default Header;
