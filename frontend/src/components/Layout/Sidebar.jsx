import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { newsAPI } from '../../services/api';
import { useNews } from '../../context/NewsContext';
import { fixCategoryName, createSlug } from '../../utils/urlUtils';

import LoadingSpinner from '../UI/LoadingSpinner';

const Sidebar = () => {
  const { getRelativeTime, truncateText } = useNews();

  // Fetch categories
  const { data: categoriesData, isLoading: categoriesLoading } = useQuery(
    'categories',
    () => newsAPI.getCategories(),
    {
      staleTime: 600000, // 10 minutes
    }
  );
  
  // Fetch popular news (by view count)
  const { data: popularNews, isLoading: popularLoading } = useQuery(
    'popular-news',
    () => newsAPI.getNews({ limit: 5 }),
    {
      staleTime: 300000, // 5 minutes
    }
  );
  
  // Fetch latest news
  const { data: latestNews, isLoading: latestLoading } = useQuery(
    'latest-news',
    () => newsAPI.getNews({ limit: 5 }),
    {
      staleTime: 300000, // 5 minutes
    }
  );
  
  return (
    <aside className="sidebar">
      {/* Categories Widget */}
      <div className="sidebar-widget">
        <h3 className="widget-title">Kategoriler</h3>
        <div className="category-list">
          {/* Yerel Haberler - Özel Link */}
          <Link
            to="/yerel-haberler"
            className="category-item featured-category"
          >
            <span><i className="fas fa-map-marker-alt"></i> Yerel Haberler</span>
            <span className="category-badge">YENİ</span>
          </Link>
          
          {categoriesLoading ? (
            <div className="text-center py-3">
              <LoadingSpinner />
            </div>
          ) : categoriesData?.success && categoriesData.data ? (
            categoriesData.data.map((category) => (
              <Link
                key={category.name}
                to={`/kategori/${encodeURIComponent(category.name)}`}
                className="category-item"
              >
                <span>{fixCategoryName(category.name)}</span>
                <span className="category-count">{category.count}</span>
              </Link>
            ))
          ) : (
            <p className="text-muted">Kategoriler yüklenemedi</p>
          )}
        </div>
      </div>
      
      {/* Popular News Widget */}
      <div className="sidebar-widget">
        <h3 className="widget-title">Popüler Haberler</h3>
        <div className="popular-news">
          {popularLoading ? (
            <div className="text-center py-3">
              <LoadingSpinner />
            </div>
          ) : popularNews?.success && popularNews.data ? (
            popularNews.data.slice(0, 5).map((item, index) => (
              <article key={item.haber_kodu} className="news-item">
                <Link to={`/haber/${item.haber_kodu}`} className="news-link">
                  <div className="news-number">{index + 1}</div>
                  <LazyLoadImage
                    src={item.featured_image || '/assets/images/placeholder.svg'}
                    alt={item.title}
                    className="news-image"
                    effect="blur"
                    onError={(e) => {
                      e.target.src = '/assets/images/placeholder.svg';
                    }}
                  />
                  <div className="news-content">
                    <h4 className="news-title">
                      {truncateText(item.title, 60)}
                    </h4>
                    <div className="news-meta">
                      <span className="news-date">
                        {getRelativeTime(item.pub_date)}
                      </span>
                      <span className="news-views">
                        <i className="far fa-eye me-1"></i>
                        {item.view_count}
                      </span>
                    </div>
                  </div>
                </Link>
              </article>
            ))
          ) : (
            <div className="alert alert-info">
              Popüler haber bulunamadı.
            </div>
          )}
        </div>
      </div>
      
      {/* Latest News Widget */}
      <div className="sidebar-widget">
        <h3 className="widget-title">Son Eklenen</h3>
        <div className="latest-news">
          {latestLoading ? (
            <div className="text-center py-3">
              <LoadingSpinner />
            </div>
          ) : latestNews?.success && Array.isArray(latestNews.data) ? (
            latestNews.data.slice(0, 5).map((item) => (
              <article key={item.haber_kodu} className="news-item">
                <Link to={`/haber/${item.haber_kodu}`} className="news-link">
                  <LazyLoadImage
                    src={item.featured_image || '/assets/images/placeholder.svg'}
                    alt={item.title}
                    className="news-image"
                    effect="blur"
                    onError={(e) => {
                      e.target.src = '/assets/images/placeholder.svg';
                    }}
                  />
                  <div className="news-content">
                    <h4 className="news-title">
                      {truncateText(item.title, 60)}
                    </h4>
                    <div className="news-meta">
                      <span className="category-badge">{item.kategori}</span>
                      <span className="news-date">
                        {getRelativeTime(item.pub_date)}
                      </span>
                    </div>
                  </div>
                </Link>
              </article>
            ))
          ) : (
            <div className="alert alert-info">
              Son haber bulunamadı.
            </div>
          )}
        </div>
      </div>
      
      {/* Breaking News Widget */}
      <div className="sidebar-widget">
        <h3 className="widget-title">
          <span className="breaking-badge">SON DAKİKA</span>
        </h3>
        <div className="breaking-news">
          {/* Breaking news will be loaded here */}
          <div className="alert alert-warning">
            Son dakika haberleri yükleniyor...
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
