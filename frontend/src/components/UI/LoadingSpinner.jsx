import React from 'react';

const LoadingSpinner = ({ size = 'medium', text = 'Yükleniyor...' }) => {
  const sizeClass = {
    small: 'spinner-border-sm',
    medium: '',
    large: 'spinner-border-lg'
  }[size];
  
  return (
    <div className="loading-spinner-container">
      <div className={`spinner-border text-primary ${sizeClass}`} role="status">
        <span className="visually-hidden">{text}</span>
      </div>
      {text && (
        <div className="loading-text mt-2">
          {text}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
