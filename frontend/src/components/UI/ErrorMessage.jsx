import React from 'react';

const ErrorMessage = ({ message, onRetry }) => {
  return (
    <div className="error-message-container">
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">
          <i className="fas fa-exclamation-triangle me-2"></i>
          Hata!
        </h4>
        <p>{message || 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.'}</p>
        {onRetry && (
          <div className="mt-3">
            <button
              className="btn btn-outline-danger"
              onClick={onRetry}
            >
              <i className="fas fa-sync-alt me-2"></i>
              Te<PERSON><PERSON>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;
