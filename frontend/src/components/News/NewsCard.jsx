import React from 'react';
import { Link } from 'react-router-dom';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import PropTypes from 'prop-types';
import { useNews } from '../../context/NewsContext';
import { stripHtmlTags, decodeHtmlEntities } from '../../utils/textUtils';

const NewsCard = ({
  news,
  variant = 'default',
  showCategory = true,
  showDate = true,
  className = ''
}) => {
  const { getRelativeTime } = useNews();

  if (!news) return null;

  // Determine card classes based on variant
  const getCardClasses = () => {
    let classes = 'news-card';
    
    switch (variant) {
      case 'large':
        classes += ' news-card--large';
        break;
      case 'compact':
        classes += ' news-card--compact';
        break;
      case 'video':
        classes += ' news-card--video';
        break;
      case 'local':
        classes += ' news-card--local';
        break;
      default:
        break;
    }
    
    if (className) {
      classes += ` ${className}`;
    }
    
    return classes;
  };

  // Get category display name
  const getCategoryName = () => {
    if (news.kategori) return news.kategori;
    if (news.sehir) return news.sehir;
    return 'Genel';
  };

  // Handle image error
  const handleImageError = (e) => {
    e.target.src = '/assets/images/placeholder.svg';
  };

  // Create proper URL - prefer slug, fallback to haber_kodu with proper encoding
  const getNewsUrl = () => {
    if (news.slug && news.slug.trim() !== '') {
      return `/haber/${news.slug}`;
    }
    // If no slug, use haber_kodu but ensure it's properly formatted
    return `/haber/${news.haber_kodu}`;
  };

  return (
    <article className={getCardClasses()}>
      <Link to={getNewsUrl()}>
        <div className="news-card__image">
          <LazyLoadImage
            src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
            alt={decodeHtmlEntities(stripHtmlTags(news.title))}
            effect="blur"
            onError={handleImageError}
            placeholderSrc="/assets/images/placeholder.svg"
          />
          
          {/* Category Badge */}
          {showCategory && (
            <span className="news-card__category">
              {getCategoryName()}
            </span>
          )}
          
          {/* Breaking News Badge */}
          {news.son_dakika === 'Evet' && (
            <span className="news-card__breaking">
              SON DAKİKA
            </span>
          )}
        </div>

        <div className="news-card__content">
          <h3 className="news-card__title">
            {decodeHtmlEntities(stripHtmlTags(news.title))}
          </h3>
          
          {showDate && (
            <div className="news-card__meta">
              <span className="news-card__date">
                {getRelativeTime(news.pub_date || news.tarih)}
              </span>
            </div>
          )}
        </div>
      </Link>
    </article>
  );
};

// PropTypes validation
NewsCard.propTypes = {
  news: PropTypes.shape({
    haber_kodu: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    slug: PropTypes.string,
    main_image: PropTypes.string,
    featured_image: PropTypes.string,
    kategori: PropTypes.string,
    sehir: PropTypes.string,
    pub_date: PropTypes.string,
    tarih: PropTypes.string,
    son_dakika: PropTypes.string
  }).isRequired,
  variant: PropTypes.oneOf(['default', 'large', 'compact', 'video', 'local']),
  showCategory: PropTypes.bool,
  showDate: PropTypes.bool,
  className: PropTypes.string
};



export default NewsCard;
