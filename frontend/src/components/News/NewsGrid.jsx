import React from 'react';
import NewsCard from './NewsCard';
import PropTypes from 'prop-types';

const NewsGrid = ({ news = [], showCategory = false, variant = "default" }) => {
  if (!news || news.length === 0) {
    return (
      <div className="alert alert-info">
        Hen<PERSON>z haber bulunmuyor.
      </div>
    );
  }

  return (
    <div className="news-grid row">
      {news.map((item) => (
        <div key={item.haber_kodu} className="news-grid-item mb-4">
          <NewsCard
            news={item}
            variant={variant}
            showCategory={showCategory}
            showDate={true}
          />
        </div>
      ))}
    </div>
  );
};

// PropTypes validation
NewsGrid.propTypes = {
  news: PropTypes.arrayOf(
    PropTypes.shape({
      haber_kodu: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      featured_image: PropTypes.string,
      kategori: PropTypes.string,
      sehir: PropTypes.string,
      pub_date: PropTypes.string,
      son_dakika: PropTypes.string
    })
  ),
  showCategory: PropTypes.bool,
  variant: PropTypes.string
};

// Default props removed - using JavaScript default parameters instead

export default NewsGrid;
