import React, { useState, useEffect } from 'react';
import { newsAPI } from '../../services/api';
import '../../assets/css/currency-ticker.css';

const CurrencyTicker = () => {
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Döviz kurlarını çek
  useEffect(() => {
    const fetchCurrencies = async () => {
      try {
        setLoading(true);

        // Kendi backend API'mizi kullan
        const data = await newsAPI.getCurrencyRates();

        if (data.success && Array.isArray(data.data)) {
          setCurrencies(data.data);
        } else {
          throw new Error('Döviz verileri işlenemedi');
        }
        setError(null);
      } catch (err) {
        console.error('Döviz kurları hatası:', err);

        // Hata durumunda varsayılan değ<PERSON>ler
        const fallbackData = [
          {
            code: 'USD',
            name: '<PERSON><PERSON>',
            symbol: '$',
            rate: '34.25',
            change: '+0.15%',
            isPositive: true
          },
          {
            code: 'EUR',
            name: 'Euro',
            symbol: '€',
            rate: '37.18',
            change: '-0.08%',
            isPositive: false
          },
          {
            code: 'GBP',
            name: 'Sterlin',
            symbol: '£',
            rate: '43.52',
            change: '+0.22%',
            isPositive: true
          }
        ];

        setCurrencies(fallbackData);
        setError(null); // Kullanıcıya hata gösterme, fallback kullan
      } finally {
        setLoading(false);
      }
    };

    fetchCurrencies();

    // Her 10 dakikada bir güncelle
    const interval = setInterval(fetchCurrencies, 10 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="currency-ticker">
        <div className="currency-container">
          <div className="currency-loading">
            <span>Döviz kurları yükleniyor...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return null; // Hata durumunda gizle
  }

  return (
    <div className="currency-ticker">
      <div className="currency-container">
        <div className="currency-label">
          <span>CANLI DÖVİZ</span>
        </div>
        <div className="currency-scroll">
          <div className="currency-items">
            {currencies.map((currency, index) => (
              <div key={`${currency.code}-${index}`} className="currency-item">
                <span className="currency-symbol">{currency.symbol}</span>
                <span className="currency-name">{currency.name}</span>
                <span className="currency-rate">₺{currency.rate}</span>
                <span className={`currency-change ${currency.isPositive ? 'positive' : 'negative'}`}>
                  {currency.change}
                </span>
              </div>
            ))}
            {/* Döngü için tekrar */}
            {currencies.map((currency, index) => (
              <div key={`${currency.code}-repeat-${index}`} className="currency-item">
                <span className="currency-symbol">{currency.symbol}</span>
                <span className="currency-name">{currency.name}</span>
                <span className="currency-rate">₺{currency.rate}</span>
                <span className={`currency-change ${currency.isPositive ? 'positive' : 'negative'}`}>
                  {currency.change}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrencyTicker;
