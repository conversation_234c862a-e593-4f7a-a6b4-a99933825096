import React from 'react';
import { useQuery } from 'react-query';
import { newsAPI } from '../../services/api';
import { Link } from 'react-router-dom';
import NewsCard from './NewsCard';
import LoadingSpinner from '../UI/LoadingSpinner';
import '../../assets/css/video-news.css';

const VideoNewsSection = () => {
    // formatDate removed - now handled by NewsCard component

    const {
        data: videoNewsData,
        isLoading,
        error
    } = useQuery(
        'video-news',
        () => newsAPI.getVideoNews({ limit: 5 }), // 5'li grid için
        {
            staleTime: 60000, // 1 dakika - daha sık güncelle
            refetchInterval: 120000, // 2 dakikada bir otomatik yenile
        }
    );

    if (isLoading) {
        return (
            <section className="video-news-section">
                <div className="container">
                    <div className="section-header">
                        <h2 className="section-title">
                            <PERSON><PERSON>
                        </h2>
                    </div>
                    <div className="text-center py-5">
                        <LoadingSpinner />
                    </div>
                </div>
            </section>
        );
    }

    // Hata varsa veya veri yoksa section'u gösterme
    if (!isLoading && (error || !videoNewsData?.success)) {
        return null;
    }

    // Veriyi kontrol et
    const videoNews = videoNewsData?.data?.data || [];

    // Video haberleri yoksa section'u gösterme
    if (videoNews.length === 0) {
        return null;
    }

    return (
        <section className="video-news-section">
            <div className="container">
                <div className="section-header">
                    <h2 className="section-title">
                        Videolu Haberler
                    </h2>
                    <Link to="/kategori/video" className="section-link">
                        Tümünü Gör
                    </Link>
                </div>

                <div className="row">
                    {videoNews.map((news) => (
                        <div key={news.haber_kodu} className="col-lg-2 col-md-6 mb-4">
                            <NewsCard
                                news={news}
                                variant="video"
                                showCategory={true}
                                showDate={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default VideoNewsSection;
