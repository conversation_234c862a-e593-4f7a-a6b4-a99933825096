import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { newsAPI } from '../../services/api';
import { stripHtmlTags } from '../../utils/textUtils';



const BreakingNewsBar = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Sadece saat formatı için özel fonksiyon
  const formatTimeOnly = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  


  // Fetch breaking news - sadece son 6 saatteki son dakika haberleri
  const { data: breakingNews, isLoading } = useQuery(
    'breaking-news',
    () => newsAPI.getNews({ breaking: 'true', limit: 50 }),
    {
      refetchInterval: 60000, // Refresh every minute
      select: (data) => {
        if (data?.success && data?.data) {
          const newsArray = Array.isArray(data.data) ? data.data : [];
          const now = new Date();
          const sixHoursAgo = new Date(now.getTime() - (6 * 60 * 60 * 1000));

          // Sadece son 6 saatteki son dakika haberlerini filtrele
          const filteredNews = newsArray.filter(news => {
            const newsDate = new Date(news.pub_date);
            const isBreaking = news.son_dakika === 'Evet';
            const isRecent = newsDate >= sixHoursAgo;
            return isBreaking && isRecent;
          });

          // En yeni haberler önce, maksimum 10 haber
          return filteredNews
            .sort((a, b) => new Date(b.pub_date) - new Date(a.pub_date))
            .slice(0, 10);
        }
        return [];
      }
    }
  );
  
  // Auto-scroll breaking news with proper cleanup
  useEffect(() => {
    if (breakingNews && breakingNews.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % breakingNews.length);
      }, 4000);

      return () => clearInterval(interval);
    }
  }, [breakingNews?.length]); // Only depend on length to prevent unnecessary re-renders



  // Loading durumunda hiçbir şey gösterme
  if (isLoading) {
    return null;
  }

  // Son 6 saatte son dakika haberi yoksa kartı gösterme
  if (!breakingNews || breakingNews.length === 0) {
    return null;
  }
  
  const currentNews = breakingNews[currentIndex];

  return (
    <div className="breaking-news-bar">
      <div className="container">
        <div className="breaking-news-content">
          <div className="breaking-label">
            <span className="breaking-text">Son Dakika</span>
          </div>
          <div className="breaking-news-slider">
            {currentNews ? (
              <Link
                to={`/haber/${currentNews.slug || currentNews.haber_kodu}`}
                className="breaking-news-item"
              >
                <h4 className="breaking-title">
                  {stripHtmlTags(currentNews.title).replace(/\n/g, ' ').trim()}
                </h4>
                <span className="breaking-time">
                  {formatTimeOnly(currentNews.pub_date)}
                </span>
              </Link>
            ) : (
              <div className="breaking-news-item">
                <h4 className="breaking-title">Haber yükleniyor...</h4>
              </div>
            )}
          </div>
          {breakingNews.length > 1 && (
            <div className="breaking-indicators">
              {breakingNews.map((news, index) => (
                <button
                  key={`indicator-${news.haber_kodu || index}`}
                  className={`indicator ${index === currentIndex ? 'active' : ''}`}
                  onClick={() => setCurrentIndex(index)}
                  aria-label={`Son dakika haberi ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Bu komponent prop almıyor - kendi verilerini çekiyor

export default BreakingNewsBar;
