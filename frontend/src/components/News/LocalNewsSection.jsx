import React from 'react';
import { useQuery } from 'react-query';
import { newsAPI } from '../../services/api';
import { Link } from 'react-router-dom';
import NewsCard from './NewsCard';
import LoadingSpinner from '../UI/LoadingSpinner';

const LocalNewsSection = () => {
    const {
        data: localNewsData,
        isLoading,
        error
    } = useQuery(
        'local-news-section',
        () => newsAPI.getNews({ limit: 30 }),
        {
            staleTime: 60000, // 1 dakika - daha sık güncelle
            refetchInterval: 120000, // 2 dakikada bir otomatik yenile
        }
    );



    if (isLoading) {
        return (
            <section className="local-news-section">
                <div className="container">
                    <div className="section-header">
                        <h2 className="section-title">
                            <PERSON>rel Haberler
                        </h2>
                    </div>
                    <div className="text-center py-5">
                        <LoadingSpinner />
                    </div>
                </div>
            </section>
        );
    }

    if (error || !localNewsData?.success || !Array.isArray(localNewsData?.data) || localNewsData.data.length === 0) {
        return null; // Hata varsa veya yerel haber yoksa section'u gösterme
    }

    // Şehir bilgisi olan haberleri filtrele ve şehirlere göre grupla
    const newsWithCities = localNewsData.data.filter(item =>
        item.sehir && item.sehir.trim() !== ''
    );

    if (newsWithCities.length === 0) {
        return null;
    }

    // Son 5 haberi al (5'li grid için)
    const recentLocalNews = newsWithCities.slice(0, 5);

    return (
        <section className="local-news-section">
            <div className="container">
                <div className="section-header">
                    <h2 className="section-title">
                        Yerel Haberler
                    </h2>
                    <Link to="/yerel-haberler" className="section-link">
                        Tümünü Gör
                    </Link>
                </div>

                <div className="row">
                    {recentLocalNews.map((news) => (
                        <div key={news.haber_kodu} className="col-lg-2 col-md-6 col-sm-6 mb-4">
                            <NewsCard
                                news={news}
                                variant="local"
                                showCategory={true}
                                showDate={true}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default LocalNewsSection;
