import React from 'react';
import { useQuery } from 'react-query';
import { newsAPI } from '../../services/api';
import NewsGrid from './NewsGrid';
import LoadingSpinner from '../UI/LoadingSpinner';


const RelatedNews = ({ category, excludeHaberKodu }) => {
  // Fetch related news from the same category
  const { data: relatedData, isLoading } = useQuery(
    ['related-news', category, excludeHaberKodu],
    () => newsAPI.getNews({ kategori: category, limit: 6 }),
    {
      enabled: !!category,
      select: (data) => {
        if (data.success && data.data) {
          // Debug: Log the filtering process
          console.log('RelatedNews - Original data:', data.data.length, 'items');
          console.log('RelatedNews - Excluding haber_kodu:', excludeHaberKodu);

          // Exclude current news from related news
          const filteredData = data.data.filter(item => {
            const shouldExclude = item.haber_kodu === excludeHaberKodu;
            if (shouldExclude) {
              console.log('RelatedNews - Excluding item:', item.haber_kodu, item.title);
            }
            return !shouldExclude;
          });

          console.log('RelatedNews - Filtered data:', filteredData.length, 'items');

          return {
            ...data,
            data: filteredData
          };
        }
        return data;
      }
    }
  );
  
  if (isLoading) {
    return (
      <section className="related-news mt-5">
        <div className="section-title">
          <h2>İlgili Haberler</h2>
        </div>
        <div className="text-center py-3">
          <LoadingSpinner />
        </div>
      </section>
    );
  }
  
  if (!relatedData?.success || !Array.isArray(relatedData.data) || relatedData.data.length === 0) {
    return null;
  }

  return (
    <section className="related-news mt-5">
      <div className="section-header">
        <h2 className="section-title">İlgili Haberler</h2>
      </div>
      <div className="related-news-grid">
        {relatedData.data.slice(0, 3).map((item) => (
          <article key={item.haber_kodu} className="related-news-item">
            <a href={`/haber/${item.slug || item.haber_kodu}`} className="related-news-link">
              <div className="related-news-image-container">
                <img
                  src={item.main_image || item.featured_image || '/assets/images/placeholder.svg'}
                  alt={item.title}
                  className="related-news-image"
                  onError={(e) => {
                    e.target.src = '/assets/images/placeholder.svg';
                  }}
                />
                {item.son_dakika === 'Evet' && (
                  <span className="breaking-badge">SON DAKİKA</span>
                )}
              </div>

              <div className="related-news-content">
                <h3 className="related-news-title">
                  {item.title?.replace(/<[^>]*>/g, '')}
                </h3>

                <div className="related-news-meta">
                  <span className="related-news-date">
                    <i className="far fa-clock"></i>
                    {new Date(item.pub_date).toLocaleDateString('tr-TR', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric'
                    })}
                  </span>
                  <span className="related-news-location">
                    <i className="fas fa-map-marker-alt"></i>
                    {item.sehir}
                  </span>
                </div>
              </div>
            </a>
          </article>
        ))}
      </div>
    </section>
  );
};

export default RelatedNews;
