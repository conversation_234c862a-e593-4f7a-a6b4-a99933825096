import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { newsAPI } from '../../services/api';
import { useNews } from '../../context/NewsContext';
import '../../assets/css/world-news-slider.css';

const WorldNewsSlider = () => {
  const { getRelativeTime } = useNews();

  // Dünya haberlerini çek
  const { data: worldNewsData, isLoading, error } = useQuery(
    'worldNews',
    () => newsAPI.getWorldNews({ limit: 12 }),
    {
      refetchInterval: 5 * 60 * 1000, // 5 dakikada bir güncelle
      staleTime: 2 * 60 * 1000, // 2 dakika fresh tut
      cacheTime: 10 * 60 * 1000 // 10 dakika cache'te tut
    }
  );

  const worldNews = worldNewsData?.success ? worldNewsData.data : [];

  if (isLoading) {
    return (
      <div className="world-news-section">
        <div className="container">
          <div className="world-section-header">
            <h2 className="world-section-title">Dünya Haberleri</h2>
          </div>
          <div className="world-loading">
            <div className="loading-spinner"></div>
            <p>Dünya haberleri yükleniyor...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !worldNews.length) {
    return (
      <div className="world-news-section">
        <div className="container">
          <div className="world-section-header">
            <h2 className="world-section-title">Dünya Haberleri</h2>
          </div>
          <div className="world-error">
            <p>Dünya haberleri şu anda yüklenemiyor.</p>
          </div>
        </div>
      </div>
    );
  }

  // Haberleri görselli ve görselsiz olarak ayır
  const newsWithImages = worldNews.filter(news => news.main_image);
  const newsWithoutImages = worldNews.filter(news => !news.main_image);

  return (
    <div className="world-news-section">
      <div className="container">
        <div className="world-section-header">
          <h2 className="world-section-title">
            Dünya Haberleri
          </h2>
        </div>

        <div className="world-news-layout">
          {/* Sol taraf - Görselli haberler (Grid) */}
          {newsWithImages.length > 0 && (
            <div className="world-news-grid">
              {newsWithImages.slice(0, 4).map((news, index) => (
                <div key={news.id} className={`world-grid-item ${index === 0 ? 'featured' : ''}`}>
                  <Link
                    to={`/haber/${news.slug || news.haber_kodu}`}
                    className="world-news-card"
                  >
                    <div className="world-card-image">
                      <img src={news.main_image} alt={news.title} />
                      <div className="world-card-category">{news.kategori}</div>
                    </div>
                    <div className="world-card-content">
                      <h3 className="world-card-title">{news.title}</h3>
                      {index === 0 && (
                        <p className="world-card-description">
                          {news.description?.replace(/<[^>]*>/g, '').substring(0, 120)}...
                        </p>
                      )}
                      <div className="world-card-meta">
                        <span className="world-card-date">{getRelativeTime(news.pub_date)}</span>
                        <span className="world-card-views">
                          <i className="far fa-eye"></i>
                          {news.view_count || 0}
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}

          {/* Sağ taraf - Görselsiz haberler (Liste) */}
          {newsWithoutImages.length > 0 && (
            <div className="world-news-list">
              <h4 className="world-list-title">Diğer Dünya Haberleri</h4>
              {newsWithoutImages.slice(0, 6).map((news, index) => (
                <div key={news.id} className="world-list-item">
                  <Link
                    to={`/haber/${news.slug || news.haber_kodu}`}
                    className="world-list-content"
                  >
                    <span className="world-list-category">{news.kategori}</span>
                    <h4 className="world-list-title-text">{news.title}</h4>
                    <div className="world-list-meta">
                      <span className="world-list-date">{getRelativeTime(news.pub_date)}</span>
                      <span className="world-list-views">
                        <i className="far fa-eye"></i>
                        {news.view_count || 0}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorldNewsSlider;
