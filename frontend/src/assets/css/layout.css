/* Layout Styles - Meta<PERSON><PERSON><PERSON> */

/* Header */
.header,
.header-main {
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: none; /* <PERSON><PERSON>lge efektini kaldır */
    width: 100%;
    margin: 0;
    padding: 0;
}

.header-container,
.main-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    position: relative;
}

.header-main .container {
    max-width: 100%;
    padding: 0 24px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.logo:focus,
.logo:active,
.logo:visited {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.logo img:focus,
.logo img:active {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.logo img {
    height: 30px;
    width: auto;
    margin-right: 12px;
}

/* Navigation */
.nav {
    display: flex;
    align-items: center;
    gap: 24px;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 8px 16px;
    color: var(--text-color);
    font-weight: 500;
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: var(--light-color);
    color: var(--primary-color);
}

.nav-link:focus {
    outline: none;
    box-shadow: none;
}

/* Hamburger Menu */
.hamburger-menu {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 10;
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
}

.hamburger-menu span {
    width: 20px;
    height: 2.5px;
    background: var(--text-color);
    border-radius: 10px;
    transition: all 0.3s linear;
    position: relative;
    transform-origin: 1px;
}

/* Navigation */
.main-nav {
    background: var(--white); /* Açık gri yerine beyaz */
    padding: 8px 0;
    display: block;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 6px;
    flex-wrap: nowrap;
    justify-content: center;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
    padding-left: 16px;
    padding-right: 16px;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-menu .nav-item {
    position: relative;
}

.nav-menu .nav-link {
    display: block;
    padding: 6px 10px;
    color: var(--text-color);
    font-weight: 500;
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    font-size: 13px;
    white-space: nowrap;
    min-width: fit-content;
}

.nav-menu .nav-link:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-color);
    cursor: pointer;
    padding: 8px;
}

/* Footer */
.footer {
    background: var(--white);
    color: var(--text-color);
    padding: 40px 0 20px;
    margin-top: 60px;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    color: var(--text-color);
    margin-bottom: 20px;
    font-size: 18px;
}

.footer-section p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 16px;
}

.social-links {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 14px;
}

/* Main Content Layout */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 20px 0;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 40px;
    align-items: start;
}

.primary-content {
    min-width: 0; /* Prevents overflow */
}

/* Sidebar */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.sidebar {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    transition: left 0.3s ease;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
}

.sidebar.active {
    left: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--gray-50);
}

.sidebar-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 18px;
}

.sidebar-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.sidebar-close:hover {
    background: var(--gray-200);
}

.sidebar-content {
    padding: 0;
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
}

.sidebar-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.sidebar-menu li {
    border-bottom: 1px solid var(--border-color);
}

.sidebar-menu li:last-child {
    border-bottom: none;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar-menu a:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

.category-count {
    margin-left: auto;
    background: var(--gray-200);
    color: var(--text-light);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Sticky sidebar for desktop layout */
.content-sidebar {
    position: sticky;
    top: 100px;
}

/* Responsive Layout */
@media (max-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .sidebar {
        position: static;
        order: -1;
    }
}

/* ===== BREAKING NEWS BAR ===== */
.breaking-news-bar {
    background: white;
    color: #333;
    padding: 8px 0;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0;
}

.breaking-news-content {
    display: flex;
    align-items: center;
    gap: 15px;
    max-width: 100%;
    width: 100%;
    padding: 0 20px;
}

.breaking-label {
    flex-shrink: 0;
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 700;
    font-size: 12px;
    font-family: 'Times New Roman', Times, serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: heartbeat 3s ease-in-out infinite;
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    20% {
        transform: scale(1.03);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }
    40% {
        transform: scale(1);
        box-shadow: 0 0 0 6px rgba(220, 53, 69, 0.1);
    }
    60% {
        transform: scale(1.03);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }
    80% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.breaking-news-slider {
    flex: 1;
    overflow: hidden;
}

.breaking-news-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #333;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.breaking-news-item:hover {
    color: #333;
    text-decoration: none;
    opacity: 0.8;
}

.breaking-title {
    flex: 1;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    margin: 0;
    line-height: 1.4;
    white-space: normal;
    text-overflow: unset;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 2.8em;
}

.breaking-time {
    flex-shrink: 0;
    font-size: 12px;
    font-weight: 400;
    font-family: 'Times New Roman', Times, serif;
    opacity: 0.7;
    background: rgba(0,0,0,0.05);
    padding: 4px 8px;
    border-radius: 3px;
    color: #666;
}

.breaking-indicators {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(0,0,0,0.2);
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.indicator.active {
    background: #dc3545;
}

@media (max-width: 768px) {
    .header-container,
    .main-header {
        padding: 12px 16px 8px 16px; /* Üstten daha fazla padding */
        gap: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .hamburger-menu {
        flex-shrink: 0;
        margin-right: 0;
        order: 1;
        z-index: 1000;
        left: 4px; /* Daha sola kaydır */
        width: 20px;
        height: 20px;
    }

    .hamburger-menu span {
        width: 16px;
        height: 2px;
    }

    .logo {
        flex-shrink: 0;
        margin-left: 0;
        order: 2;
        flex-grow: 1;
        display: flex;
        justify-content: center;
    }

    .logo-image {
        height: 30px !important; /* Mobilde logoyu daha küçük yap */
        max-width: 200px;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* Son dakika kartını mobilde gizle */
    .breaking-news-bar {
        display: none !important;
    }

    /* Breaking news section'u da mobilde gizle */
    .breaking-news-section {
        display: none !important;
    }

    .nav {
        display: none;
    }

    .main-nav {
        display: block;
    }

    .nav-container {
        padding: 0; /* Padding'i kaldır */
    }

    .nav-menu {
        justify-content: flex-start;
        padding-left: 0; /* Padding'i kaldır */
        padding-right: 0; /* Padding'i kaldır */
        gap: 6px;
    }

    .nav-menu .nav-link {
        padding: 8px 14px;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
        white-space: nowrap;
        min-width: fit-content;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .nav-menu .nav-link:hover,
    .nav-menu .nav-link.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        background: transparent; /* Mavi background'u kaldır */
        box-shadow: none; /* Gölge efektini kaldır */
        transform: none; /* Transform efektini kaldır */
    }

    .nav-menu .nav-link:focus,
    .nav-menu .nav-link:active {
        outline: none;
        box-shadow: none;
        background: transparent; /* Focus/active durumunda da mavi background yok */
        transform: none; /* Transform efektini kaldır */
    }

    .mobile-menu-toggle {
        display: block;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Footer mobil düzenlemeleri */
    .footer-logo img {
        height: 40px !important; /* Logo boyutunu küçült */
    }

    .footer-description {
        text-align: center !important; /* Yazıyı ortala */
        margin-bottom: 20px !important;
    }

    .social-links {
        justify-content: center !important; /* Sosyal linkleri ortala */
    }

    /* İletişim sütununu mobilde gizle */
    .footer .col-md-4 {
        display: none !important;
    }

    .footer .col-md-8 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        text-align: center !important;
    }

    .main-content {
        padding: 16px 0;
    }
}

@media (max-width: 576px) {
    .logo {
        font-size: 20px;
    }
    
    .logo img {
        height: 32px;
        margin-right: 8px;
    }
}
