/* ===== NEW HOMEPAGE STYLES ===== */

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

/* New Homepage Container */
.new-homepage {
  background: #ffffff;
  min-height: 100vh;
}

/* ===== HERO SECTION ===== */
.hero-section {
  margin-bottom: 40px;
  margin-top: 0;
}

.hero-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: start;
}

.hero-slider {
  position: relative;
  height: 500px;
  overflow: hidden;
  background: #000;
  border-radius: 12px;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slider-wrapper {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease;
  will-change: transform;
  cursor: grab;
  touch-action: pan-x;
}

.slider-wrapper:active {
  cursor: grabbing;
}

.slide {
  flex: none;
  width: 100%;
  height: 100%;
  position: relative;
  min-width: 100%;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.slide-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #000;
}

.slide-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.slide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  padding: 60px 40px 40px;
  color: white;
}

.slide-category {
  display: inline-block;
  background: #e74c3c;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Times New Roman', Times, serif;
  text-transform: uppercase;
  margin-bottom: 12px;
}

.slide-title {
  font-size: 28px;
  font-weight: 700;
  font-family: 'Times New Roman', Times, serif;
  line-height: 1.3;
  margin-bottom: 12px;
  color: white;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 120px;
}

.slide-summary {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 12px;
  opacity: 0.9;
}

.slide-meta {
  font-size: 14px;
  opacity: 0.8;
}

/* ===== HERO SIDEBAR ===== */
.hero-sidebar {
  background: transparent;
  padding: 0;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  display: none;
}

.sidebar-news-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 100%;
  justify-content: flex-start;
}

.sidebar-news-item {
  background: transparent;
  border-radius: 0;
  padding: 16px 0;
  box-shadow: none;
  border: none;
  border-bottom: 1px solid #e9ecef;
  height: 125px;
  display: flex;
  margin-bottom: 0;
}

.sidebar-news-item:last-child {
  border-bottom: none;
}

.sidebar-news-item a {
  display: flex;
  gap: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  width: 100%;
  align-items: center;
}

.sidebar-news-item:hover {
  background: transparent;
}

.sidebar-news-image {
  position: relative;
  width: 140px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  flex-shrink: 0;
}

.sidebar-news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.sidebar-news-category {
  position: absolute;
  top: 4px;
  left: 4px;
  background: #e74c3c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 8px;
  font-weight: 600;
  font-family: 'Times New Roman', Times, serif;
  text-transform: uppercase;
}

.sidebar-news-content {
  flex: 1;
  min-width: 0;
}

.sidebar-news-title {
  font-size: 15px;
  font-weight: 600;
  font-family: 'Times New Roman', Times, serif;
  line-height: 1.4;
  color: #2c3e50;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sidebar-news-meta {
  font-size: 12px;
  color: #6c757d;
}

.sidebar-news-date {
  font-weight: 400;
  font-family: 'Times New Roman', Times, serif;
}

/* Slider Controls */
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.6);
  border: 2px solid rgba(255,255,255,0.8);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.slider-btn:hover {
  background: rgba(0,0,0,0.8);
  border-color: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 15px rgba(0,0,0,0.4);
}

.slider-btn-prev {
  left: 20px;
}

.slider-btn-next {
  right: 20px;
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255,255,255,0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: white;
  transform: scale(1.2);
}

/* ===== SECTIONS ===== */
.featured-section,
.latest-section,
.category-section {
  padding: 40px 0;
  background: white;
  margin-bottom: 0;
  margin-bottom: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Section Headers */
.new-homepage .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 5px;
  border-bottom: 3px solid #e74c3c;
}

.new-homepage .section-title {
  font-size: 28px !important;
  font-weight: 700;
  font-family: 'Times New Roman', Times, serif;
  color: #2c3e50;
  margin: 0;
  line-height: 1.2;
  position: relative;
  z-index: 10;
}

.section-link {
  color: #e74c3c;
  font-weight: 600;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.section-link:hover {
  color: #c0392b;
}

/* ===== NEWS GRID ===== */
.news-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

/* News Cards */
.news-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.news-card a {
  text-decoration: none;
  color: inherit;
  display: block;
}

.card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  background: #f8f9fa;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .card-image img {
  transform: scale(1.05);
}

.card-category {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  font-family: 'Times New Roman', Times, serif;
  text-transform: uppercase;
}

.card-content {
  padding: 8px 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Times New Roman', Times, serif;
  line-height: 1.3;
  color: #2c3e50;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-meta {
  font-size: 12px;
  color: #6c757d;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 1024px) {
  .news-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .slide-title {
    font-size: 24px;
    font-family: 'Times New Roman', Times, serif;
  }
  
  .slide-summary {
    font-size: 14px;
  }
  
  .slide-overlay {
    padding: 40px 30px 30px;
  }
}

/* Tablet */
@media (max-width: 992px) and (min-width: 769px) {
  .slide-title {
    font-size: 24px;
    font-family: 'Times New Roman', Times, serif;
    max-height: 100px;
    -webkit-line-clamp: 2;
  }

  .slide-summary {
    font-size: 15px;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 300px;
    border-radius: 8px;
  }

  .hero-sidebar {
    padding: 0;
    height: 300px;
  }

  .sidebar-title {
    display: none;
  }

  .sidebar-news-image {
    width: 100px;
    height: 70px;
  }

  .sidebar-news-title {
    font-size: 13px;
    font-family: 'Times New Roman', Times, serif;
  }

  .sidebar-news-item {
    padding: 12px 0;
    margin-bottom: 0;
    height: 75px;
  }
  
  .slide-overlay {
    padding: 20px 15px 15px;
  }

  .slide-title {
    font-size: 16px;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 8px;
    max-height: 64px;
    -webkit-line-clamp: 2;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    hyphens: auto;
  }

  .slide-summary {
    font-size: 12px;
    margin-bottom: 8px;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .slide-category {
    font-size: 7px;
    padding: 2px 4px;
    margin-bottom: 8px;
  }

  .card-category {
    font-size: 8px;
    padding: 3px 6px;
    top: 8px;
    left: 8px;
  }
  
  .slider-btn {
    width: 32px;
    height: 32px;
  }
  
  .slider-btn-prev {
    left: 8px;
  }

  .slider-btn-next {
    right: 8px;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .featured-section,
  .latest-section,
  .category-section {
    padding: 30px 0;
    margin-bottom: 15px;
  }
  
  .section-header {
    margin-bottom: 20px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 8px;
  }

  .section-title {
    font-size: 22px;
    font-family: 'Times New Roman', Times, serif;
  }
  
  /* Mobile Grid - 2 columns */
  .news-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  
  .card-image {
    height: 120px;
  }

  .card-content {
    padding: 8px 12px;
  }
  
  .card-title {
    font-size: 18px;
    font-family: 'Times New Roman', Times, serif;
    -webkit-line-clamp: 2;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .hero-slider {
    height: 250px;
  }

  .hero-sidebar {
    padding: 0;
    height: 250px;
  }

  .sidebar-news-list {
    gap: 0;
  }

  .sidebar-news-image {
    width: 80px;
    height: 60px;
  }

  .sidebar-news-title {
    font-size: 12px;
    font-family: 'Times New Roman', Times, serif;
    -webkit-line-clamp: 2;
  }

  .sidebar-news-item {
    padding: 10px 0;
    margin-bottom: 0;
    height: 62.5px;
  }
  
  .slide-title {
    font-size: 16px;
    font-family: 'Times New Roman', Times, serif;
    max-height: 60px;
    -webkit-line-clamp: 2;
    line-height: 1.2;
    word-wrap: break-word;
    hyphens: auto;
  }

  .slide-category {
    font-size: 6px;
    padding: 2px 4px;
  }
  
  .slide-summary {
    display: none; /* Hide summary on very small screens */
  }
  
  .news-grid {
    gap: 12px;
  }
  
  .card-image {
    height: 100px;
  }

  .card-content {
    padding: 6px 10px;
  }
  
  .card-title {
    font-size: 17px;
    font-family: 'Times New Roman', Times, serif;
  }
  
  .section-title {
    font-size: 20px;
    font-family: 'Times New Roman', Times, serif;
  }
}

/* ===== CREATIVE HERO SECTION ===== */
.creative-hero-section {
  margin-bottom: 50px;
}

.hero-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  height: 600px;
}

/* Main Hero Card */
.hero-main-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15);
  transition: all 0.4s ease;
}

.hero-main-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0,0,0,0.25);
}

.hero-main-link {
  display: block;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.hero-main-image {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.hero-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.hero-main-card:hover .hero-main-image img {
  transform: scale(1.08);
}

.hero-main-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0,0,0,0.1) 30%,
    rgba(0,0,0,0.7) 70%,
    rgba(0,0,0,0.9) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40px;
}

.hero-breaking-badge {
  position: absolute;
  top: 25px;
  left: 25px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: pulse 2s infinite;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.hero-category {
  display: inline-block;
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
  color: white;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 15px;
  border: 1px solid rgba(255,255,255,0.3);
}

.hero-main-title {
  font-size: 32px;
  font-weight: 800;
  color: white;
  line-height: 1.2;
  margin-bottom: 15px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.hero-main-excerpt {
  font-size: 16px;
  color: rgba(255,255,255,0.9);
  line-height: 1.5;
  margin-bottom: 20px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hero-main-meta {
  display: flex;
  gap: 20px;
  align-items: center;
}

.hero-date, .hero-location {
  color: rgba(255,255,255,0.8);
  font-size: 14px;
  font-weight: 500;
}

.hero-location::before {
  content: '📍';
  margin-right: 5px;
}

/* Side Grid */
.hero-side-grid {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.hero-side-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 25px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.hero-side-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.18);
}

.hero-side-card.featured {
  grid-row: 1 / 3;
}

.hero-side-link {
  display: flex;
  flex-direction: column;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.hero-side-image {
  position: relative;
  height: 140px;
  overflow: hidden;
}

.hero-side-card.featured .hero-side-image {
  height: 200px;
}

.hero-side-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.hero-side-card:hover .hero-side-image img {
  transform: scale(1.05);
}

.hero-side-breaking {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.hero-side-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.hero-side-category {
  display: inline-block;
  background: #f8f9fa;
  color: #e74c3c;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 10px;
  width: fit-content;
}

.hero-side-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin: 0 0 auto 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hero-side-card.featured .hero-side-title {
  font-size: 18px;
  -webkit-line-clamp: 4;
}

.hero-side-meta {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f1f3f4;
}

.hero-side-date {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Responsive for Creative Hero */
@media (max-width: 1200px) {
  .hero-container {
    height: 550px;
  }

  .hero-main-title {
    font-size: 28px;
  }
}

@media (max-width: 992px) {
  .hero-container {
    grid-template-columns: 1fr;
    height: auto;
    gap: 25px;
  }

  .hero-main-card {
    height: 400px;
  }

  .hero-side-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
  }

  .hero-side-card.featured {
    grid-row: auto;
    grid-column: 1 / 3;
  }
}

@media (max-width: 768px) {
  .hero-side-grid {
    grid-template-columns: 1fr;
  }

  .hero-side-card.featured {
    grid-column: auto;
  }

  .hero-main-title {
    font-size: 24px;
  }

  .hero-main-overlay {
    padding: 25px;
  }
}
