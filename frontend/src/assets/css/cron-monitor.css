/* Cron Monitor Styles */

.overall-health {
  padding: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.overall-health.health-good {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
}

.overall-health.health-warning {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
}

.overall-health.health-critical {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
}

.overall-health.health-unknown {
  background: linear-gradient(135deg, #e2e3e5, #d6d8db);
  color: #383d41;
}

/* Status Badges */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

/* Health Score */
.health-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.health-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  border: 3px solid;
  transition: all 0.3s ease;
}

.health-success .health-circle {
  background-color: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.health-warning .health-circle {
  background-color: #fff3cd;
  border-color: #ffc107;
  color: #856404;
}

.health-danger .health-circle {
  background-color: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.health-number {
  font-size: 18px;
  font-weight: 700;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Card Enhancements */
.card {
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 12px 12px 0 0 !important;
  padding: 15px 20px;
}

.card-body {
  padding: 20px;
}

/* Progress Bar */
.progress {
  height: 8px;
  border-radius: 4px;
  background-color: #e9ecef;
  overflow: hidden;
}

.progress-bar {
  transition: width 0.6s ease;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  margin-bottom: 30px;
}

.page-header .page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.page-header .breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
}

.page-header .breadcrumb-item a {
  color: rgba(255,255,255,0.8);
  text-decoration: none;
}

.page-header .breadcrumb-item.active {
  color: white;
}

.page-header .breadcrumb-item + .breadcrumb-item::before {
  color: rgba(255,255,255,0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header .page-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .health-circle {
    width: 50px;
    height: 50px;
    font-size: 14px;
  }
  
  .health-number {
    font-size: 16px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .card-body {
    padding: 15px;
  }
  
  .card-header {
    padding: 12px 15px;
  }
}

@media (max-width: 576px) {
  .overall-health {
    padding: 15px;
  }
  
  .page-header {
    padding: 30px 0;
  }
  
  .page-header .page-title {
    font-size: 1.75rem;
  }
}

/* Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.health-circle:hover {
  animation: pulse 1s infinite;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-indicator.running {
  background-color: #28a745;
  animation: pulse-green 2s infinite;
}

.status-indicator.delayed {
  background-color: #ffc107;
  animation: pulse-yellow 2s infinite;
}

.status-indicator.stopped {
  background-color: #dc3545;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

@keyframes pulse-yellow {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}
