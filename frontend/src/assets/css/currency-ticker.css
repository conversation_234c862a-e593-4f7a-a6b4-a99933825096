/* Currency Ticker Styles */
.currency-ticker {
    background: #ffffff;
    border: none;
    border-radius: 6px;
    margin: 12px 0 0 0;
    overflow: hidden;
    box-shadow: none;
    position: relative;
}

.currency-container {
    display: flex;
    align-items: center;
    height: 50px;
    position: relative;
}

.currency-label {
    background: #e9ecef;
    padding: 0 12px;
    height: 100%;
    align-items: center;
    flex-shrink: 0;
    border-right: 1px solid #dee2e6;
    display: none; /* <PERSON><PERSON><PERSON> Döviz yazısını gizle */
}

.currency-label span {
    color: #495057;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
    font-family: 'Times New Roman', Times, serif;
}

.currency-scroll {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.currency-items {
    display: flex;
    align-items: center;
    height: 100%;
    animation: scroll-left 45s linear infinite;
    white-space: nowrap;
}

.currency-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 20px;
    flex-shrink: 0;
    height: 100%;
    border-right: 1px solid #dee2e6;
}

.currency-item:last-child {
    border-right: none;
}

.currency-symbol {
    color: #007bff;
    font-size: 16px;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.currency-name {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    font-family: 'Times New Roman', Times, serif;
}

.currency-rate {
    color: #212529;
    font-size: 15px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
}

.currency-change {
    font-size: 13px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 6px;
    font-family: 'Times New Roman', Times, serif;
}

.currency-change.positive {
    color: #00ff88;
    background: rgba(0, 255, 136, 0.15);
}

.currency-change.negative {
    color: #ff4757;
    background: rgba(255, 71, 87, 0.15);
}

.currency-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 11px;
    font-family: 'Times New Roman', Times, serif;
}

/* Scroll Animation */
@keyframes scroll-left {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Hover Effects */
.currency-ticker:hover .currency-items {
    animation-play-state: paused;
}

.currency-item:hover {
    background: #e9ecef;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .currency-ticker {
        margin: 10px 0;
    }

    .currency-container {
        height: 36px;
    }

    .currency-items {
        animation: scroll-left 30s linear infinite; /* Mobilde daha hızlı */
    }

    .currency-label {
        padding: 0 10px;
    }

    .currency-label span {
        font-size: 10px;
    }

    .currency-item {
        padding: 0 12px;
        gap: 4px;
    }

    .currency-symbol {
        font-size: 12px;
    }

    .currency-name {
        font-size: 10px;
    }

    .currency-rate {
        font-size: 11px;
    }

    .currency-change {
        font-size: 9px;
        padding: 1px 3px;
    }
}

@media (max-width: 480px) {
    .currency-container {
        height: 32px;
    }

    .currency-items {
        animation: scroll-left 25s linear infinite; /* Çok küçük ekranlarda daha da hızlı */
    }

    .currency-label {
        padding: 0 8px;
    }

    .currency-label span {
        font-size: 9px;
    }

    .currency-item {
        padding: 0 10px;
        gap: 3px;
    }

    .currency-symbol {
        font-size: 11px;
    }

    .currency-name {
        font-size: 9px;
    }

    .currency-rate {
        font-size: 10px;
    }

    .currency-change {
        font-size: 8px;
        padding: 1px 2px;
    }
}

/* Dark mode support - Disabled to keep white background */
/* @media (prefers-color-scheme: dark) {
    .currency-ticker {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
} */
