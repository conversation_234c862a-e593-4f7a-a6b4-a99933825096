/* Video News Page Styles */
.video-news-page {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 20px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 36px;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    z-index: 2;
}

.page-title i {
    color: #ffd700;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.page-description {
    font-size: 18px;
    opacity: 0.9;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
    position: relative;
    z-index: 2;
}

/* Video News Grid */
.video-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.video-news-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.video-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-news-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.video-card-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.video-news-card:hover .video-card-image img {
    transform: scale(1.05);
}

.video-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.video-news-card:hover .video-play-overlay {
    background: rgba(0, 0, 0, 0.5);
}

.play-button {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e74c3c;
    font-size: 24px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.video-news-card:hover .play-button {
    transform: scale(1.1);
    background: white;
}

.video-duration {
    position: absolute;
    bottom: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.video-card-content {
    padding: 20px;
}

.video-card-title {
    font-size: 18px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.4;
    margin: 0 0 12px 0;
    color: #2c3e50;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #666;
}

.video-card-category,
.video-card-date,
.video-card-views {
    display: flex;
    align-items: center;
    gap: 4px;
    font-family: 'Times New Roman', Times, serif;
}

.video-card-category {
    color: #e74c3c;
    font-weight: 600;
}

.video-card-excerpt {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin: 40px 0;
}

.load-more-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.load-more-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.load-more-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* No News State */
.no-news {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-news-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.no-news h3 {
    font-size: 24px;
    font-family: 'Times New Roman', Times, serif;
    margin: 0 0 12px 0;
    color: #333;
}

.no-news p {
    font-size: 16px;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
}

.no-more-news {
    text-align: center;
    padding: 20px;
    color: #666;
    font-family: 'Times New Roman', Times, serif;
}

/* Error State */
.error-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    margin: 40px 0;
}

.error-message h2 {
    color: #e74c3c;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 12px;
}

.error-message p {
    color: #666;
    margin-bottom: 20px;
    font-family: 'Times New Roman', Times, serif;
}

.retry-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Times New Roman', Times, serif;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-news-page {
        padding: 10px 0;
    }
    
    .page-header {
        margin-bottom: 20px;
        padding: 30px 20px;
    }
    
    .page-title {
        font-size: 28px;
        flex-direction: column;
        gap: 8px;
    }
    
    .page-description {
        font-size: 16px;
    }
    
    .video-news-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .video-card-image {
        height: 180px;
    }
    
    .play-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .video-card-content {
        padding: 16px;
    }
    
    .video-card-title {
        font-size: 16px;
    }
    
    .video-card-meta {
        font-size: 12px;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 20px 15px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .video-card-image {
        height: 160px;
    }
    
    .video-card-content {
        padding: 12px;
    }
    
    .video-card-title {
        font-size: 15px;
    }
}
