/* Video News Section Styles */
.video-news-section {
    margin-bottom: 48px;
}

/* Video news section artık standart section-header kullanıyor */

/* Video news grid artık standart Bootstrap row/col sistemi kullanıyor */

/* Video News Card - Uses unified news-card system from components.css */
.video-news-card {
    /* Inherits from .news-card base styles */
}

.video-news-card:hover {
    /* Inherits from .news-card:hover base styles */
}

.video-news-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.video-news-image {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
}

.video-news-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-news-card:hover .video-news-image img {
    transform: scale(1.05);
}

/* Video category badge - positioned at top right, always visible */
.video-category-badge,
.video-news-card .news-card__category {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    opacity: 1;
    visibility: visible;
}

.video-news-content {
    padding: 1rem;
}

.video-news-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-news-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.video-news-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .video-news-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .video-news-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    /* Video news mobilde 2'li grid - homepage.css'den kontrol ediliyor */
    .video-news-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .video-news-grid {
        grid-template-columns: 1fr;
    }

    .video-news-section {
        padding: 1rem 0;
        margin: 24px 0;
    }

    .video-play-icon {
        font-size: 36px;
    }
}

/* Video news category badge - inline style */
.video-news-meta .category-badge {
    position: static;
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: none;
}

.news-date {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.news-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 12px;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-excerpt {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
}

.news-location {
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
}

.read-more {
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .video-news-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .video-news-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Duplicate 768px media query removed - handled by homepage.css */
