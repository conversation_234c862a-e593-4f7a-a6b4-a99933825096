/* Components - Meta<PERSON><PERSON><PERSON> */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #1557b0;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #4a4d52;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--gray-50);
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--gray-50);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
}

.card-text {
    color: var(--text-light);
    line-height: 1.6;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.badge-success {
    background: #34a853;
    color: var(--white);
}

.badge-warning {
    background: #fbbc04;
    color: var(--dark-color);
}

.badge-danger {
    background: var(--accent-color);
    color: var(--white);
}

.badge-light {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* Breaking News Badge */
.breaking-badge {
    background: var(--accent-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background: #fef7f0;
    border: 1px solid #fcd34d;
    border-radius: var(--border-radius);
    padding: 16px;
    color: #92400e;
    margin: 20px 0;
}

.error-message .error-title {
    font-weight: 600;
    margin-bottom: 8px;
}

/* Success Message */
.success-message {
    background: #f0fdf4;
    border: 1px solid #86efac;
    border-radius: var(--border-radius);
    padding: 16px;
    color: #166534;
    margin: 20px 0;
}

/* Alert */
.alert {
    padding: 16px;
    border-radius: var(--border-radius);
    margin: 16px 0;
    border: 1px solid transparent;
}

.alert-info {
    background: #eff6ff;
    border-color: #93c5fd;
    color: #1e40af;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fcd34d;
    color: #92400e;
}

.alert-danger {
    background: #fef2f2;
    border-color: #fca5a5;
    color: #dc2626;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 40px 0;
}

.pagination-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination-item:hover,
.pagination-item.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== NEWS CARD COMPONENT SYSTEM ===== */

/* Base News Card */
.news-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    text-decoration: none;
    color: inherit;
}

.news-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    color: inherit;
}

/* News Card Image */
.news-card__image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: var(--gray-100);
    flex-shrink: 0;
}

.news-card__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.news-card:hover .news-card__image img {
    transform: scale(1.05);
}

/* News Card Content */
.news-card__content {
    padding: 12px;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 100px;
    max-height: 140px;
}

.news-card__title {
    font-size: 18px;
    font-weight: 400;
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.4;
    margin: 0 0 12px 0;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.2s ease;
    flex: 1;
}

.news-card:hover .news-card__title {
    color: var(--primary-color);
}

.news-card__meta {
    margin-top: auto;
    padding-top: 12px;
}

.news-card__date {
    font-size: 14px;
    color: #666;
    font-weight: 400;
    font-family: 'Times New Roman', Times, serif;
    display: block;
}

/* Category Badge */
.news-card__category {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 3;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Breaking News Badge */
.news-card__breaking {
    position: absolute;
    top: 8px;
    left: 8px;
    background: #dc3545;
    color: var(--white);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 3;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ===== NEWS CARD MODIFIERS ===== */

/* Large variant (for hero sections) */
.news-card--large .news-card__image {
    height: 250px;
}

.news-card--large .news-card__content {
    padding: 20px;
    min-height: 120px;
    max-height: 160px;
}

.news-card--large .news-card__title {
    font-size: 20px;
}

/* Compact variant (for sidebars) */
.news-card--compact {
    flex-direction: row;
    height: auto;
}

.news-card--compact .news-card__image {
    width: 120px;
    height: 100px;
    flex-shrink: 0;
}

.news-card--compact .news-card__content {
    padding: 12px;
    min-height: auto;
    max-height: none;
}

.news-card--compact .news-card__title {
    font-size: 15px;
    -webkit-line-clamp: 2;
}

/* Video variant */
.news-card--video .news-card__category {
    background: var(--accent-color);
}

/* Local news variant */
.news-card--local .news-card__category {
    background: var(--secondary-color);
}

/* ===== RESPONSIVE NEWS CARDS ===== */

/* Mobile First Approach */
@media (max-width: 768px) {
    .news-card__content {
        padding: 16px;
        min-height: 80px;
        max-height: 120px;
    }

    .news-card__title {
        font-size: 16px;
        -webkit-line-clamp: 2;
    }

    .news-card__category {
        font-size: 10px;
        padding: 3px 8px;
    }

    /* Compact cards on mobile */
    .news-card--compact .news-card__image {
        width: 100px;
        height: 80px;
    }

    .news-card--compact .news-card__content {
        padding: 10px;
    }

    .news-card--compact .news-card__title {
        font-size: 14px;
    }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    .news-card__content {
        padding: 14px;
    }

    .news-card__title {
        font-size: 17px;
    }
}

/* Desktop */
@media (min-width: 1025px) {
    .news-card--large .news-card__content {
        padding: 24px;
        min-height: 140px;
        max-height: 180px;
    }

    .news-card--large .news-card__title {
        font-size: 22px;
    }
}
