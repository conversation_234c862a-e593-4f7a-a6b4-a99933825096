/* Components - Meta<PERSON><PERSON><PERSON> */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #1557b0;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #4a4d52;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--gray-50);
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--gray-50);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
}

.card-text {
    color: var(--text-light);
    line-height: 1.6;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.badge-success {
    background: #34a853;
    color: var(--white);
}

.badge-warning {
    background: #fbbc04;
    color: var(--dark-color);
}

.badge-danger {
    background: var(--accent-color);
    color: var(--white);
}

.badge-light {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* Breaking News Badge */
.breaking-badge {
    background: var(--accent-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background: #fef7f0;
    border: 1px solid #fcd34d;
    border-radius: var(--border-radius);
    padding: 16px;
    color: #92400e;
    margin: 20px 0;
}

.error-message .error-title {
    font-weight: 600;
    margin-bottom: 8px;
}

/* Success Message */
.success-message {
    background: #f0fdf4;
    border: 1px solid #86efac;
    border-radius: var(--border-radius);
    padding: 16px;
    color: #166534;
    margin: 20px 0;
}

/* Alert */
.alert {
    padding: 16px;
    border-radius: var(--border-radius);
    margin: 16px 0;
    border: 1px solid transparent;
}

.alert-info {
    background: #eff6ff;
    border-color: #93c5fd;
    color: #1e40af;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fcd34d;
    color: #92400e;
}

.alert-danger {
    background: #fef2f2;
    border-color: #fca5a5;
    color: #dc2626;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 40px 0;
}

.pagination-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination-item:hover,
.pagination-item.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== NEWS CARD COMPONENT SYSTEM ===== */

/* Base News Card */
.news-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #f0f2f5;
    position: relative;
}

.news-card__link {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #e1e8ed;
}

/* News Card Image Container */
.news-card__image-container {
    position: relative;
    flex-shrink: 0;
}

.news-card__image {
    position: relative;
    height: 180px;
    overflow: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.news-card__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.news-card:hover .news-card__img {
    transform: scale(1.08);
}

/* News Card Content */
.news-card__content {
    padding: 16px;
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;
    min-height: 120px;
}

.news-card__title {
    font-size: 16px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
    margin: 0 0 12px 0;
    color: #1a202c;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.2s ease;
    flex: 1;
}

.news-card:hover .news-card__title {
    color: #2563eb;
}

.news-card__meta {
    margin-top: auto;
    padding-top: 8px;
    border-top: 1px solid #f7fafc;
}

.news-card__date {
    font-size: 13px;
    color: #64748b;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 6px;
}

.news-card__date i {
    font-size: 12px;
    opacity: 0.7;
}

/* Category Badge */
.news-card__category {
    position: absolute;
    bottom: 12px;
    left: 12px;
    background: rgba(37, 99, 235, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 3;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Breaking News Badge */
.news-card__breaking {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 4;
    display: flex;
    align-items: center;
    gap: 4px;
    animation: pulse 2s infinite;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.news-card__breaking i {
    font-size: 8px;
}

/* Video Badge */
.news-card__video-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.news-card__video-badge i {
    font-size: 12px;
    margin-left: 2px;
}

.news-card:hover .news-card__video-badge {
    background: rgba(239, 68, 68, 0.9);
    transform: scale(1.1);
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
    100% { opacity: 1; transform: scale(1); }
}

/* ===== NEWS CARD MODIFIERS ===== */

/* Large variant (for hero sections) */
.news-card--large .news-card__image {
    height: 250px;
}

.news-card--large .news-card__content {
    padding: 20px;
    min-height: 120px;
    max-height: 160px;
}

.news-card--large .news-card__title {
    font-size: 20px;
}

/* Compact variant (for sidebars) */
.news-card--compact {
    flex-direction: row;
    height: auto;
}

.news-card--compact .news-card__image {
    width: 120px;
    height: 100px;
    flex-shrink: 0;
}

.news-card--compact .news-card__content {
    padding: 12px;
    min-height: auto;
    max-height: none;
}

.news-card--compact .news-card__title {
    font-size: 15px;
    -webkit-line-clamp: 2;
}

/* Video variant */
.news-card--video .news-card__category {
    background: var(--accent-color);
}

/* Local news variant */
.news-card--local .news-card__category {
    background: var(--secondary-color);
}

/* ===== RESPONSIVE NEWS CARDS ===== */

/* Mobile First Approach */
@media (max-width: 768px) {
    .news-card__image {
        height: 160px;
    }

    .news-card__content {
        padding: 14px;
        min-height: 100px;
    }

    .news-card__title {
        font-size: 15px;
        -webkit-line-clamp: 2;
        line-height: 1.3;
    }

    .news-card__category {
        font-size: 10px;
        padding: 4px 10px;
        bottom: 10px;
        left: 10px;
    }

    .news-card__video-badge {
        width: 32px;
        height: 32px;
        top: 10px;
        right: 10px;
    }

    .news-card__video-badge i {
        font-size: 10px;
    }

    .news-card__breaking {
        top: 10px;
        left: 10px;
        padding: 4px 8px;
        font-size: 9px;
    }

    /* Compact cards on mobile */
    .news-card--compact .news-card__image {
        width: 100px;
        height: 80px;
    }

    .news-card--compact .news-card__content {
        padding: 10px;
    }

    .news-card--compact .news-card__title {
        font-size: 14px;
    }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    .news-card__image {
        height: 170px;
    }

    .news-card__content {
        padding: 15px;
        min-height: 110px;
    }

    .news-card__title {
        font-size: 15px;
    }

    .news-card__category {
        font-size: 10px;
        padding: 5px 11px;
    }
}

/* Desktop */
@media (min-width: 1025px) {
    .news-card--large .news-card__content {
        padding: 24px;
        min-height: 140px;
        max-height: 180px;
    }

    .news-card--large .news-card__title {
        font-size: 22px;
    }
}
