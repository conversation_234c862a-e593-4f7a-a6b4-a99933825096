/* News Detail Page Styles */

/* Main Container */
.news-detail-page {
    background: #fff;
    min-height: 100vh;
    padding: 20px 0;
    zoom: 0.9;
    -moz-transform: scale(0.9);
    -moz-transform-origin: 0 0;
}

/* News Article Container */
.news-article {
    background: #fff;
    border-radius: 12px;
    overflow: visible;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 40px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 95%;
    position: relative;
    z-index: 1;
}

/* News Header */
.news-header {
    padding: 32px 40px 24px 40px;
    border-bottom: 1px solid #eee;
}

.breaking-badge {
    display: inline-block;
    background: #dc3545;
    color: #fff;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 16px;
    letter-spacing: 0.5px;
}

.news-title {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.3;
    margin-bottom: 20px;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.news-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.news-meta-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.news-meta-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Font Control Buttons */
.font-controls {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 4px;
    border: 1px solid #e9ecef;
}

.font-control-btn {
    background: transparent;
    border: none;
    padding: 6px 10px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 32px;
}

.font-control-btn:hover {
    background: #e9ecef;
    color: #333;
}

.font-control-btn.active {
    background: var(--primary-color, #007bff);
    color: white;
}

/* Speech Button */
.speech-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.speech-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.speech-btn.reading {
    background: #dc3545;
    animation: pulse 1.5s infinite;
}

.speech-btn.reading:hover {
    background: #c82333;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.category-badge {
    background: var(--primary-color, #007bff);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.news-date,
.news-location {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 14px;
}

.news-date i,
.news-location i {
    color: var(--primary-color, #007bff);
}

/* Image Carousel */
.news-image-carousel {
    position: relative;
    width: 100%;
    background: #f8f9fa;
}

.carousel-container {
    max-width: 100%;
}

.carousel-main {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-image {
    width: 100%;
    height: 500px;
    display: block;
    object-fit: contain;
    background: #f8f9fa;
}

/* Carousel Navigation Arrows */
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
    z-index: 10;
}

.carousel-arrow:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
    left: 20px;
}

.carousel-arrow-right {
    right: 20px;
}

/* Image Caption */
.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: #fff;
    padding: 30px 20px 20px;
    font-size: 14px;
    line-height: 1.4;
}

/* Carousel Thumbnails */
.carousel-thumbnails {
    display: flex !important;
    gap: 10px;
    padding: 20px;
    justify-content: center;
    flex-wrap: wrap;
    background: #fff;
    border-top: 1px solid #eee;
    min-height: 100px;
    visibility: visible !important;
}

.thumbnail {
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: none;
    padding: 0;
    width: 80px;
    height: 60px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.thumbnail:hover {
    border-color: var(--primary-color, #007bff);
    transform: scale(1.05);
}

.thumbnail.active {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

/* News Content */
.news-content {
    padding: 32px 40px;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    overflow: visible;
    background: #fff;
    position: relative;
    margin: 0;
    height: auto;
    min-height: fit-content;
}

.content-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color, #007bff);
}

.content-text {
    line-height: 1.8;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    height: auto;
    overflow: visible;
}

.content-paragraph {
    font-size: 18px;
    line-height: 1.8;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: left;
    font-family: 'Times New Roman', Times, serif;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    font-weight: 400;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    white-space: normal;
}

.content-paragraph:last-child {
    margin-bottom: 0;
}

/* Inline Images in Content */
.content-image {
    margin: 30px 0;
    text-align: center;
}

.inline-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.inline-image:hover {
    transform: scale(1.02);
}

/* IHA Disclaimer */
.news-disclaimer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.disclaimer-text {
    font-size: 12px;
    font-family: 'Times New Roman', Times, serif;
    color: #888;
    font-style: italic;
    text-align: center;
    margin: 0;
    opacity: 0.8;
}

/* Additional Images */
.news-additional-images {
    padding: 30px 40px;
    border-top: 1px solid #eee;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 20px;
}

.additional-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.additional-image {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.additional-image-item {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.additional-image-caption {
    padding: 12px;
    font-size: 14px;
    color: #666;
    background: #f8f9fa;
    line-height: 1.4;
}

/* News Videos */
.news-videos {
    padding: 30px 40px;
    border-top: 1px solid #eee;
    background: #fafbfc;
    border-radius: 12px;
    margin: 20px 0;
}

.news-videos-top {
    border-top: none;
    margin-top: 0;
    margin-bottom: 30px;
}

.news-videos .section-title {
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.news-videos .section-title i {
    color: #e74c3c;
    font-size: 22px;
}

.video-container {
    margin-bottom: 20px;
}

.video-wrapper {
    position: relative;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
}

.video-player {
    width: 100%;
    height: auto;
    min-height: 300px;
    max-height: 500px;
    background: #000;
    object-fit: contain;
    outline: none;
    display: block;
}

.video-player:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.video-caption {
    padding: 15px 20px;
    font-size: 14px;
    color: #5a6c7d;
    line-height: 1.5;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.video-caption i {
    color: #6c757d;
    margin-top: 2px;
    flex-shrink: 0;
}

/* Video Placeholder */
.video-placeholder {
    width: 100%;
    min-height: 300px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #dee2e6;
}

.placeholder-content {
    text-align: center;
    color: #6c757d;
}

.placeholder-content i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 16px;
    display: block;
}

.placeholder-content p {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.placeholder-content small {
    font-size: 14px;
    color: #6c757d;
}

/* News Footer */
.news-footer {
    padding: 30px 40px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    margin-top: 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
    clear: both;
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 95%;
}

.news-tags {
    margin-bottom: 0;
}

.tags-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

.tags-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
    flex-shrink: 0;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag-item {
    background: var(--primary-color, #007bff);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: var(--accent-color, #0056b3);
    color: #fff;
    transform: translateY(-2px);
}

.news-share {
    margin-bottom: 0;
}

.share-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

.share-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
    flex-shrink: 0;
}

.share-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.share-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    background: #fff;
    color: #666;
    border: 2px solid #e9ecef;
    cursor: pointer;
    font-size: 20px;
}

.share-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.share-button.twitter:hover {
    background: #1da1f2;
    color: #fff;
    border-color: #1da1f2;
}

.share-button.facebook:hover {
    background: #4267b2;
    color: #fff;
    border-color: #4267b2;
}

.share-button.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    color: #fff;
    border-color: #e6683c;
}

.share-button.telegram:hover {
    background: #0088cc;
    color: #fff;
    border-color: #0088cc;
}

.share-button.twitter:hover {
    background: #000;
    color: #fff;
    border-color: #000;
}

.share-button.linkedin:hover {
    background: #0077b5;
    color: #fff;
    border-color: #0077b5;
}

.share-button.whatsapp:hover {
    background: #25d366;
    color: #fff;
    border-color: #25d366;
}

.share-button.copy:hover {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Container ve sayfa düzeni */
    .news-detail-page .container {
        padding: 0 12px;
    }

    .news-article {
        width: 100%;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
    }

    /* Header bölümü */
    .news-header {
        padding: 16px 16px 12px 16px;
    }

    .news-title {
        font-size: 22px;
        line-height: 1.3;
        margin-bottom: 12px;
    }

    .news-meta {
        gap: 8px;
        flex-wrap: wrap;
        flex-direction: column;
        align-items: flex-start;
    }

    .news-meta-left {
        gap: 8px;
    }

    .news-meta-right {
        gap: 10px;
        margin-top: 8px;
    }

    .font-controls {
        padding: 3px;
        gap: 3px;
    }

    .font-control-btn {
        padding: 5px 8px;
        font-size: 12px;
        min-width: 28px;
    }

    .speech-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .category-badge,
    .news-date,
    .news-location {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* Image Carousel Mobil Optimizasyonu */
    .news-image-carousel {
        margin: 0 -12px 20px -12px; /* Container padding'ini iptal et */
    }

    .carousel-main {
        height: 250px; /* Mobilde daha küçük */
    }

    .main-image {
        height: 250px;
        object-fit: contain;
    }

    /* Carousel Navigation */
    .carousel-arrow {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }

    .carousel-arrow-left {
        left: 8px;
    }

    .carousel-arrow-right {
        right: 8px;
    }

    /* Thumbnail Navigation - Mobilde daha kompakt */
    .carousel-thumbnails {
        padding: 8px 12px;
        gap: 6px;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
    }

    .carousel-thumbnails::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
    }

    .thumbnail {
        min-width: 50px;
        height: 35px;
        border-radius: 4px;
    }

    .thumbnail img {
        border-radius: 4px;
    }

    /* Content bölümü */
    .news-content {
        padding: 16px;
        margin-top: 0;
    }

    .content-title {
        font-size: 18px;
        margin-bottom: 12px;
    }

    .content-paragraph {
        font-size: 15px;
        font-family: 'Times New Roman', Times, serif;
        line-height: 1.6;
        margin-bottom: 14px;
    }

    .disclaimer-text {
        font-size: 11px;
    }

    /* Diğer bölümler */
    .news-additional-images,
    .news-videos,
    .news-footer {
        padding: 16px;
    }

    /* Video player mobil düzeni */
    .news-videos .section-title {
        font-size: 18px;
        margin-bottom: 16px;
    }

    .video-wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }

    .video-player {
        min-height: 200px;
        max-height: 300px;
    }

    .video-caption {
        padding: 12px 16px;
        font-size: 13px;
    }

    /* Video placeholder mobil */
    .video-placeholder {
        min-height: 200px;
    }

    .placeholder-content i {
        font-size: 36px;
        margin-bottom: 12px;
    }

    .placeholder-content p {
        font-size: 15px;
    }

    .placeholder-content small {
        font-size: 13px;
    }

    .news-footer {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .share-buttons {
        justify-content: center;
        gap: 8px;
    }

    .share-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .tags-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .share-button {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    /* Related News mobil düzeni */
    .related-news {
        margin-top: 24px;
        padding: 16px;
    }

    .related-news h3 {
        font-size: 18px;
        margin-bottom: 16px;
    }
}

@media (max-width: 480px) {
    .news-detail-page {
        padding: 10px 0;
    }

    .news-header {
        padding: 16px 16px 12px 16px;
    }

    .news-title {
        font-size: 20px;
    }

    .news-content {
        padding: 16px;
    }

    .content-title {
        font-size: 18px;
    }

    .content-paragraph {
        font-size: 15px;
        font-family: 'Times New Roman', Times, serif;
        margin-bottom: 14px;
    }

    .news-additional-images,
    .news-videos,
    .news-footer {
        padding: 16px;
    }

    /* Video player tablet düzeni */
    .video-player {
        min-height: 250px;
        max-height: 400px;
    }
}

/* Related News Section */
.related-news {
    padding: 40px 0;
}

.related-news .section-header {
    text-align: left;
    max-width: 1200px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
}

.related-news .section-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.related-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.related-news-item {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.related-news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.related-news-link {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.related-news-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.related-news-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-news-item:hover .related-news-image {
    transform: scale(1.05);
}

.related-news-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.related-news-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-news-meta {
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.related-news-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.related-news-location {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--primary-color, #007bff);
    font-weight: 500;
}

.breaking-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #dc3545;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive for Related News */
@media (max-width: 768px) {
    .related-news {
        padding: 30px 0;
    }

    .related-news .section-header {
        padding: 0 15px;
    }

    .related-news .section-title {
        font-size: 24px;
    }

    .related-news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
    }

    .related-news-image-container {
        height: 180px;
    }

    .related-news-content {
        padding: 15px;
    }

    .related-news-title {
        font-size: 16px;
    }
}