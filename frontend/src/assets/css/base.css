/* Base Styles - MetaA<PERSON><PERSON> */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    overflow-x: hidden;
    max-width: 100%;
}

body {
    font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 16px;
    color: var(--text-color);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 16px;
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 40px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 40px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.py-1 { padding-top: 8px; padding-bottom: 8px; }
.py-2 { padding-top: 16px; padding-bottom: 16px; }
.py-3 { padding-top: 24px; padding-bottom: 24px; }
.py-4 { padding-top: 32px; padding-bottom: 32px; }
.py-5 { padding-top: 40px; padding-bottom: 40px; }

/* Responsive Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
}

.col {
    flex: 1;
    padding: 0 12px;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

/* Responsive breakpoints */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
}

@media (max-width: 576px) {
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
}

/* ===== HOMEPAGE HERO SECTION ===== */
.hero-main-section {
    margin-bottom: 40px;
    margin-top: 20px;
}

.main-featured-news {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.main-news-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.main-news-image {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.main-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.main-news-link:hover .main-news-image img {
    transform: scale(1.05);
}

.news-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 30px 30px;
}

.news-content {
    color: white;
}

.hero-news-title {
    font-size: 24px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.3;
    margin-bottom: 12px;
}

.hero-news-title span {
    display: block;
}

/* Side News */
.side-news-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 400px;
}

.side-news-item {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.side-news-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.side-news-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.side-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.side-news-link:hover .side-news-image img {
    transform: scale(1.05);
}

.side-news-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 15px 15px;
}

.side-news-title {
    color: white;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive */
@media (max-width: 992px) {
    .hero-main-section .row {
        flex-direction: column;
    }

    .main-news-image {
        height: 300px;
    }

    .side-news-list {
        flex-direction: row;
        height: auto;
        margin-top: 20px;
    }

    .side-news-item {
        height: 150px;
    }

    .hero-news-title {
        font-size: 20px;
    }

    .side-news-title {
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .main-news-image {
        height: 250px;
    }

    .hero-news-title {
        font-size: 18px;
    }

    .side-news-list {
        flex-direction: column;
    }

    .side-news-item {
        height: 120px;
    }
}
