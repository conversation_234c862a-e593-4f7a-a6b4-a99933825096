/* <PERSON><PERSON>ı - MetaAnaliz <PERSON> */

/* Header Şehir Seçici */
.city-filter-header {
    display: flex;
    align-items: center;
}

.city-filter-header .filter-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.city-filter-header .city-select {
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--text-color);
    background: var(--white);
    transition: all 0.3s ease;
}

.city-filter-header .city-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* YEREL HABERLER SAYFASI - HABER KARTLARI GRİD SİSTEMİ */
/* Kategori sayfası ile aynı kurallar */

.local-news-page .news-grid.row {
    margin: 0 -8px; /* <PERSON><PERSON> az margin */
    display: flex;
    flex-wrap: wrap;
}

.local-news-page .news-grid .news-grid-item {
    padding: 0 8px; /* <PERSON><PERSON> az padding, kartlar daha geniş */
    margin-bottom: 24px; /* Kartlar arası dikey boşluk */
    display: flex; /* Kartların eşit yükseklikte olması için */
}

.local-news-page .news-grid .news-grid-item .news-card {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Desktop - 5'li grid (10 haber için 2 satır x 5 kolon) */
@media (min-width: 992px) {
    .local-news-page .news-grid .news-grid-item {
        flex: 0 0 20% !important; /* 5 kart yan yana: 100% / 5 = 20% */
        max-width: 20% !important;
        width: 20% !important; /* Ekstra güvence için */
        flex-basis: 20% !important; /* Bootstrap override için */
        min-width: 0 !important; /* Flexbox shrink için */
    }

    /* Haber kartlarını 10 haber için optimize et */
    .local-news-page .news-grid .news-card {
        width: 100%;
        min-height: 360px; /* Biraz daha kompakt */
    }

    .local-news-page .news-grid .news-card__image {
        height: 200px; /* Biraz daha küçük resim */
    }

    .local-news-page .news-grid .news-card__content {
        padding: 16px; /* Daha az padding */
        min-height: 140px; /* Daha kompakt içerik */
    }

    .local-news-page .news-grid .news-card__title {
        font-size: 15px; /* Daha küçük başlık */
        line-height: 1.3;
        -webkit-line-clamp: 3; /* 3 satıra kadar başlık */
        font-weight: 600;
    }
}

/* Tablet - 2'li grid */
@media (min-width: 768px) and (max-width: 991.98px) {
    .local-news-page .news-grid .news-grid-item {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .local-news-page .news-grid .news-card {
        min-height: 320px;
    }

    .local-news-page .news-grid .news-card__image {
        height: 180px;
    }

    .local-news-page .news-grid .news-card__content {
        padding: 16px;
        min-height: 120px;
    }

    .local-news-page .news-grid .news-card__title {
        font-size: 15px;
        -webkit-line-clamp: 3;
    }
}

/* Mobil - 2'li grid (10 haber için 2-2-2-2-2 = 5 satır) */
@media (max-width: 767.98px) {
    .local-news-page .news-grid.row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin: 0;
    }

    .local-news-page .news-grid .news-grid-item {
        padding: 0;
    }

    .local-news-page .news-grid .news-card {
        min-height: 280px; /* Biraz daha kompakt */
    }

    .local-news-page .news-grid .news-card__image {
        height: 140px; /* Daha küçük resim */
    }

    .local-news-page .news-grid .news-card__content {
        padding: 12px; /* Daha az padding */
        min-height: 120px;
    }

    .local-news-page .news-grid .news-card__title {
        font-size: 13px; /* Daha küçük font */
        line-height: 1.3;
        -webkit-line-clamp: 3;
    }
}

/* Ana Konteyner */
.local-news-page {
    background: var(--white);
    min-height: 100vh;
    padding: 0;
}

/* Şehir Filtresi */
.city-filter {
    margin-bottom: 40px;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-container {
    display: flex;
    align-items: center;
    gap: 16px;
    max-width: 300px;
    margin-left: auto;
    margin-top: 4px;
}

.filter-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.city-select {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-bottom: none;
    border-radius: 8px;
    font-size: 14px;
    color: var(--text-color);
    background: var(--white);
    transition: all 0.3s ease;
}

.city-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

/* Page Header Styles - Kategori sayfası ile aynı */
.local-news-page .page-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 0;
    margin-bottom: 30px;
}

.local-news-page .page-header .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
}

.local-news-page .page-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 12px 0;
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.local-news-page .category-name {
    color: var(--primary-color);
}

.local-news-page .category-subtitle {
    color: var(--text-light);
    font-weight: 400;
}

.local-news-page .category-breadcrumb {
    margin: 0;
}

.local-news-page .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

/* Breadcrumb Styles for Local News Page */
.local-news-page .breadcrumb-item {
    color: #666 !important;
}

.local-news-page .breadcrumb-item a {
    color: #666666 !important;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.local-news-page .breadcrumb-item a:hover {
    color: #333333 !important;
    text-decoration: none;
}

.local-news-page .breadcrumb-item.active {
    color: #333 !important;
    font-weight: 500;
}

.local-news-page .breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #999 !important;
    margin: 0 8px;
}

/* Ana Şehirler Bölümleri */
.main-cities-sections {
    display: flex;
    flex-direction: column;
    gap: 48px;
}

.city-section {
    margin-bottom: 40px;
}

/* Section Headers - Anasayfa stilinde */
.local-news-page .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 5px;
    border-bottom: 3px solid #e74c3c;
}

.local-news-page .section-title {
    font-size: 28px !important;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    line-height: 1.2;
}

.local-news-page .section-link {
    color: #e74c3c;
    font-weight: 600;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.2;
    transition: color 0.3s ease;
}

.local-news-page .section-link:hover {
    color: #c0392b;
}

/* Seçili Şehir Haberleri */
.selected-city-news {
    margin-top: 32px;
}

/* Haber Grid'i - Sadece yerel haberler sayfası için */
.local-news-page .news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 24px;
}

/* Haber Kartı - Unified news-card system kullanıyor */
.news-item,
.news-card {
    /* Base styles inherited from components.css .news-card */
}

.news-item:hover,
.news-card:hover {
    /* Hover styles inherited from components.css .news-card:hover */
}

/* Haber Görseli */
.news-image-container {
    position: relative;
    height: 0;
    overflow: hidden;
    background: #f5f5f5;
    padding-bottom: 65%;
    flex-shrink: 0;
}

.news-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image {
    transform: scale(1.05);
}

/* Kategori Badge - Sağ üstte konumlandırılmış */
.news-category-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 3;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Haber İçeriği */
.news-content {
    padding: 12px;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 100px;
    max-height: 140px;
}

.news-meta-top {
    margin-bottom: 12px;
}

.news-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.3s ease;
    text-align: left;
    flex: 1;
}

.news-item:hover .news-title {
    color: var(--primary-color);
}

.news-meta,
.news-meta-bottom {
    margin-top: auto;
    padding-top: 12px;
}

.news-date {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    display: block;
}

.news-city {
    background: var(--gray-100);
    color: var(--text-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 11px;
}

/* Haber Yok Mesajı */
.no-news {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-light);
}

.no-news p {
    font-size: 14px;
    color: var(--text-light);
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 60px 20px;
    min-height: 300px;
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 16px;
}

/* Duplicate styles removed - using main styles above */

/* Additional news image styles */
.news-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image-container img {
    transform: scale(1.05);
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .local-news-page {
        padding: 16px 0;
    }

    .city-filter {
        margin-bottom: 32px;
        padding: 16px 0;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        max-width: 100%;
        gap: 12px;
    }

    .filter-label {
        font-size: 14px;
    }

    .city-select {
        padding: 10px 12px;
        font-size: 14px;
    }

    .main-cities-sections {
        gap: 32px;
    }

    .city-section {
        margin-bottom: 32px;
    }

    .local-news-page .news-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-top: 20px;
    }

    /* Mobile news image height override */
    .news-image-container {
        height: 160px;
    }

    .news-content {
        padding: 14px;
    }

    .news-title {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .news-meta {
        font-size: 11px;
    }

    .news-city {
        font-size: 10px;
        padding: 2px 4px;
    }

    .news-category {
        font-size: 10px;
        padding: 3px 6px;
    }

    .no-news {
        padding: 40px 16px;
    }

    .no-news p {
        font-size: 14px;
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .local-news-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }
    
    .cities-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .local-news-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .local-news-container {
        padding: 16px 0;
    }
    
    .local-news-header {
        padding: 24px 0;
        margin-bottom: 24px;
    }
    
    /* Additional mobile overrides */
    .city-filter {
        flex-direction: row;
        align-items: center;
        padding: 16px 0;
    }

    .filter-container {
        max-width: 200px;
    }

    .filter-label {
        font-size: 14px;
    }

    .city-select {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .cities-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }
    
    .city-button {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .local-news-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .local-news-image {
        height: 180px;
    }
    
    .news-section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .selected-city-info {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .city-filter-section,
    .local-news-content {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .local-news-content-area {
        padding: 16px;
    }
    
    .local-news-title {
        font-size: 1.6rem;
    }
    
    .selected-city-name {
        font-size: 1.5rem;
    }
    
    .local-news-item .local-news-content-area .local-news-title {
        font-size: 1.1rem;
    }
}
