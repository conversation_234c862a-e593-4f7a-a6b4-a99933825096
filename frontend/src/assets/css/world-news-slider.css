/* Dünya Ha<PERSON>leri Section */
.world-news-section {
  padding: 40px 0;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.world-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 3px solid #dc3545;
}

.world-section-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  position: relative;
}

.world-section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #dc3545, #e74c3c);
}

/* Layout Container */
.world-news-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: start;
}

/* Sol Taraf - Görselli Haberler Grid */
.world-news-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
  gap: 20px;
  min-height: 400px;
}

.world-grid-item.featured {
  grid-row: 1 / 3;
}

.world-news-card {
  display: block;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.world-news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  text-decoration: none;
  color: inherit;
}

.world-card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.world-grid-item.featured .world-card-image {
  height: 250px;
}

.world-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.world-news-card:hover .world-card-image img {
  transform: scale(1.05);
}

.world-card-category {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(135deg, #dc3545, #e74c3c);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.world-card-content {
  padding: 20px;
}

.world-grid-item:not(.featured) .world-card-content {
  padding: 15px;
}

.world-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.world-grid-item.featured .world-card-title {
  font-size: 22px;
  -webkit-line-clamp: 3;
}

.world-grid-item:not(.featured) .world-card-title {
  font-size: 16px;
  -webkit-line-clamp: 2;
}

.world-card-description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.6;
  margin: 10px 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.world-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
  margin-top: auto;
}

.world-card-date {
  font-weight: 500;
}

.world-card-views {
  display: flex;
  align-items: center;
  gap: 5px;
}

.world-card-views i {
  color: #dc3545;
}

/* Sağ Taraf - Görselsiz Haberler Liste */
.world-news-list {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  height: fit-content;
}

.world-list-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 20px 0;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
  position: relative;
}

.world-list-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: #dc3545;
}

.world-list-item {
  border-bottom: 1px solid #f1f3f4;
  padding: 15px 0;
}

.world-list-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.world-list-content {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.world-list-content:hover {
  text-decoration: none;
  color: inherit;
}

.world-list-content:hover .world-list-title-text {
  color: #dc3545;
}

.world-list-category {
  display: inline-block;
  background: #f8f9fa;
  color: #dc3545;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.world-list-title-text {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.2s ease;
}

.world-list-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.world-list-date {
  font-weight: 500;
}

.world-list-views {
  display: flex;
  align-items: center;
  gap: 4px;
}

.world-list-views i {
  color: #dc3545;
}

/* Loading ve Error States */
.world-loading, .world-error {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #dc3545;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.world-error p {
  color: #6c757d;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .world-news-layout {
    grid-template-columns: 1.5fr 1fr;
    gap: 25px;
  }
}

@media (max-width: 992px) {
  .world-news-layout {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .world-news-grid {
    grid-template-columns: 1fr 1fr;
    height: auto;
  }
  
  .world-grid-item.featured {
    grid-row: auto;
    grid-column: 1 / 3;
  }
  
  .world-card-image {
    height: 180px;
  }
  
  .world-grid-item.featured .world-card-image {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .world-news-section {
    padding: 30px 0;
  }
  
  .world-section-title {
    font-size: 24px;
  }
  
  .world-news-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .world-grid-item.featured {
    grid-column: auto;
  }
  
  .world-card-image {
    height: 200px;
  }
  
  .world-grid-item.featured .world-card-image {
    height: 220px;
  }
  
  .world-news-list {
    padding: 20px;
  }
}
