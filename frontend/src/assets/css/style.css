/* Meta<PERSON><PERSON><PERSON> - <PERSON>z CSS Mimarisi */

/* ===== CSS IMPORTS ===== */
@import url('./variables.css');
@import url('./base.css');
@import url('./components.css');
@import url('./layout.css');
@import url('./new-homepage.css');
@import url('./category.css');
@import url('./video-news.css');
@import url('./local-news.css');
@import url('./news-detail.css');

/* ===== GLOBAL RESETS ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    overflow-x: hidden;
    max-width: 100%;
}

body {
    font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 48px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 48px; }

/* ===== RESPONSIVE GRID SYSTEM ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
}

.col {
    flex: 1;
    padding: 0 12px;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== RESPONSIVE BREAKPOINTS ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .row {
        margin: 0 -8px;
    }
    
    .col {
        padding: 0 8px;
    }
    
    /* Mobile-first: Tüm kolonlar mobilde full width */
    .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        padding: 0 20px;
    }
}

/* ===== GLOBAL OVERRIDES ===== */
/* Sadece kritik global düzeltmeler burada - maksimum 50 satır */

/* Bootstrap override */
.container-fluid {
    max-width: 100%;
    padding: 0 24px;
}

/* Link styles */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:focus {
    outline: none;
    box-shadow: none;
}

/* Remove focus outline from buttons and interactive elements */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: none;
    box-shadow: none;
}

a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* Image responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Focus states removed - no outlines */

/* Print styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .no-print {
        display: none !important;
    }
}
