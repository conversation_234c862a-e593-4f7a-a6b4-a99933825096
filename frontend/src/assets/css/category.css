/* Category Page Styles - MetaAnal<PERSON> */

/* Category page uses unified news-card system from components.css */
/* Additional category-specific styles can be added here if needed */

.category-page {
    padding: 0; /* Üst boşluğu kaldır */
}

/* Ana container - tüm sayfa için */
.category-page .container {
    max-width: 1400px; /* Daha geniş container */
    margin: 0 auto;
    padding: 0 30px; /* Sağ ve sol eşit boşluk */
}

/* Page header container */
.page-header .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px; /* Breadcrumb ile haber kartları aynı hizada */
}

/* Kategori sayfası container genişliği */
.category-news-section .container {
    max-width: 1400px; /* Daha geniş container */
    margin: 0 auto;
    padding: 0 30px; /* Haber kartları için aynı padding */
}

/* Page Header Styles */
.page-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 0;
    margin-bottom: 30px; /* Haber kartları ile arasında boşluk */
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 12px 0;
    display: flex;
    align-items: baseline;
    gap: 8px;
    position: relative;
    z-index: 10;
}

.category-name {
    color: var(--primary-color);
}

.category-subtitle {
    color: var(--text-light);
    font-weight: 400;
}

/* Breadcrumb Styles */
.category-breadcrumb {
    margin: 0;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.category-page .breadcrumb-item {
    color: #666 !important;
}

.category-page .breadcrumb-item a {
    color: #666666 !important;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.category-page .breadcrumb-item a:hover {
    color: #333333 !important;
    text-decoration: none;
}

.category-page .breadcrumb-item.active {
    color: #333 !important;
    font-weight: 500;
}

/* Breadcrumb separator - basit */
.category-page .breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #999 !important;
    margin: 0 8px;
}

.category-header {
    margin-bottom: 32px;
    margin-top: 30px; /* Header ile haber kartları arasında boşluk */
}

.category-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.category-description {
    color: var(--text-light);
    font-size: 16px;
    line-height: 1.5;
}

/* Kategori sayfası grid sistemi */
.news-grid.row {
    margin: 0 -8px; /* Daha az margin */
    display: flex;
    flex-wrap: wrap;
}

.news-grid .news-grid-item {
    padding: 0 8px; /* Daha az padding, kartlar daha geniş */
    margin-bottom: 24px; /* Kartlar arası dikey boşluk */
    display: flex; /* Kartların eşit yükseklikte olması için */
}

.news-grid .news-grid-item .news-card {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Desktop - 5'li grid (kompakt görünüm) */
@media (min-width: 992px) {
    .news-grid .news-grid-item {
        flex: 0 0 20% !important; /* 5 kart yan yana: 100% / 5 = 20% */
        max-width: 20% !important;
        width: 20% !important; /* Ekstra güvence için */
    }

    /* Haber kartlarını biraz daha geniş yap */
    .news-grid .news-card {
        width: 100%;
        min-height: 420px; /* Daha yüksek minimum yükseklik */
    }

    .news-grid .news-card__image {
        height: 240px; /* Daha yüksek resim */
    }

    .news-grid .news-card__content {
        padding: 20px; /* Daha fazla içerik padding'i */
        min-height: 170px; /* Daha yüksek içerik alanı */
    }

    .news-grid .news-card__title {
        font-size: 17px; /* Daha büyük başlık font boyutu */
        line-height: 1.4;
        -webkit-line-clamp: 4; /* 4 satıra kadar başlık göster */
        font-weight: 600; /* Başlığı daha belirgin yap */
    }
}

/* Tablet - 2'li grid */
@media (min-width: 768px) and (max-width: 991.98px) {
    .news-grid .news-grid-item {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .news-grid .news-card {
        min-height: 320px;
    }

    .news-grid .news-card__image {
        height: 180px;
    }

    .news-grid .news-card__content {
        padding: 16px;
        min-height: 120px;
    }

    .news-grid .news-card__title {
        font-size: 15px;
        -webkit-line-clamp: 3;
    }
}

/* Mobil - 2'li grid */
@media (max-width: 767.98px) {
    .news-grid.row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin: 0;
    }

    .news-grid .news-grid-item {
        padding: 0;
    }

    .news-grid .news-card {
        min-height: 300px;
    }

    .news-grid .news-card__image {
        height: 150px;
    }

    .news-grid .news-card__content {
        padding: 14px;
        min-height: 130px;
    }

    .news-grid .news-card__title {
        font-size: 14px;
        -webkit-line-clamp: 3;
    }
}

