/* World News Page Styles */
.world-news-page {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 20px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="worldgrid" width="15" height="15" patternUnits="userSpaceOnUse"><circle cx="7.5" cy="7.5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23worldgrid)"/></svg>');
    opacity: 0.4;
}

.page-title {
    font-size: 36px;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    z-index: 2;
}

.page-title i {
    color: #ffd700;
    animation: rotate 15s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.page-description {
    font-size: 18px;
    opacity: 0.9;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
    position: relative;
    z-index: 2;
}

/* World News Grid */
.world-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.world-news-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border-left: 4px solid #3498db;
}

.world-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-left-color: #e74c3c;
}

.world-news-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.world-card-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.world-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.world-news-card:hover .world-card-image img {
    transform: scale(1.05);
}

.world-card-overlay {
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.world-card-category {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 4px;
    backdrop-filter: blur(10px);
}

.world-card-content {
    padding: 20px;
}

.world-card-title {
    font-size: 18px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    line-height: 1.4;
    margin: 0 0 12px 0;
    color: #2c3e50;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.world-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #666;
}

.world-card-location,
.world-card-date,
.world-card-views {
    display: flex;
    align-items: center;
    gap: 4px;
    font-family: 'Times New Roman', Times, serif;
}

.world-card-location {
    color: #3498db;
    font-weight: 600;
}

.world-card-location i {
    color: #e74c3c;
}

.world-card-excerpt {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin: 40px 0;
}

.load-more-btn {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Times New Roman', Times, serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.load-more-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.load-more-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* No News State */
.no-news {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-news-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.no-news h3 {
    font-size: 24px;
    font-family: 'Times New Roman', Times, serif;
    margin: 0 0 12px 0;
    color: #333;
}

.no-news p {
    font-size: 16px;
    margin: 0;
    font-family: 'Times New Roman', Times, serif;
}

.no-more-news {
    text-align: center;
    padding: 20px;
    color: #666;
    font-family: 'Times New Roman', Times, serif;
}

/* Error State */
.error-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    margin: 40px 0;
}

.error-message h2 {
    color: #e74c3c;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 12px;
}

.error-message p {
    color: #666;
    margin-bottom: 20px;
    font-family: 'Times New Roman', Times, serif;
}

.retry-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Times New Roman', Times, serif;
}

/* Responsive Design */
@media (max-width: 768px) {
    .world-news-page {
        padding: 10px 0;
    }
    
    .page-header {
        margin-bottom: 20px;
        padding: 30px 20px;
    }
    
    .page-title {
        font-size: 28px;
        flex-direction: column;
        gap: 8px;
    }
    
    .page-description {
        font-size: 16px;
    }
    
    .world-news-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }
    
    .world-card-image {
        height: 180px;
    }
    
    .world-card-content {
        padding: 16px;
    }
    
    .world-card-title {
        font-size: 16px;
    }
    
    .world-card-meta {
        font-size: 12px;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 20px 15px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .world-card-image {
        height: 160px;
    }
    
    .world-card-content {
        padding: 12px;
    }
    
    .world-card-title {
        font-size: 15px;
    }
    
    .world-card-overlay {
        top: 8px;
        left: 8px;
        right: 8px;
    }
    
    .world-card-category {
        font-size: 10px;
        padding: 4px 8px;
    }
}
