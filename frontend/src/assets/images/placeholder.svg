<svg width="400" height="250" viewBox="0 0 400 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="250" fill="#f8f9fa"/>
  
  <!-- Gradient overlay -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="250" fill="url(#grad1)"/>
  
  <!-- News icon -->
  <g transform="translate(200, 80)">
    <circle cx="0" cy="0" r="40" fill="#1976d2" opacity="0.1"/>
    <path d="M-20 -15 L20 -15 L20 15 L-20 15 Z" fill="#1976d2" opacity="0.3"/>
    <path d="M-15 -10 L15 -10 M-15 -5 L10 -5 M-15 0 L15 0 M-15 5 L8 5 M-15 10 L12 10" stroke="#1976d2" stroke-width="1.5" stroke-linecap="round"/>
  </g>
  
  <!-- MetaAnaliz text -->
  <text x="200" y="160" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#1976d2">
    MetaAnaliz
  </text>
  
  <!-- Haber text -->
  <text x="200" y="185" font-family="Arial, sans-serif" font-size="20" font-weight="600" text-anchor="middle" fill="#2196f3">
    Haber
  </text>
  
  <!-- Decorative elements -->
  <circle cx="80" cy="60" r="3" fill="#1976d2" opacity="0.3"/>
  <circle cx="320" cy="190" r="4" fill="#2196f3" opacity="0.2"/>
  <circle cx="350" cy="70" r="2" fill="#1976d2" opacity="0.4"/>
  <circle cx="50" cy="200" r="2.5" fill="#2196f3" opacity="0.3"/>
</svg>
