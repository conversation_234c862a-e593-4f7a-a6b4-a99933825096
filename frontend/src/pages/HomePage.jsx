import React, { useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';

import { stripHtmlTags, decodeHtmlEntities } from '../utils/textUtils';

// Components
import LoadingSpinner from '../components/UI/LoadingSpinner';
import VideoNewsSection from '../components/News/VideoNewsSection';
import LocalNewsSection from '../components/News/LocalNewsSection';
import NewsCard from '../components/News/NewsCard';
import { LazyLoadImage } from 'react-lazy-load-image-component';

const HomePage = () => {
  const { getRelativeTime, truncateText } = useNews();

  // Fetch main news for hero section
  const {
    data: newsData,
    isLoading: newsLoading
  } = useQuery(
    'homepage-main-news',
    () => newsAPI.getNews({ limit: 50 }), // Ana haberler için
    {
      staleTime: 60000,
      cacheTime: 300000,
      refetchInterval: 120000,
    }
  );

  // Her kategori için ayrı API çağrısı - Garantili 5'er haber
  const { data: economyData } = useQuery(
    'homepage-economy',
    () => newsAPI.getNews({ kategori: 'EKONOMİ', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: sportsData } = useQuery(
    'homepage-sports',
    () => newsAPI.getNews({ kategori: 'SPOR', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: healthData } = useQuery(
    'homepage-health',
    () => newsAPI.getNews({ kategori: 'SAĞLIK', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: techData } = useQuery(
    'homepage-tech',
    () => newsAPI.getNews({ kategori: 'BİLİM VE TEKNOLOJİ', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: politicsData } = useQuery(
    'homepage-politics',
    () => newsAPI.getNews({ kategori: 'POLİTİKA', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: cultureData } = useQuery(
    'homepage-culture',
    () => newsAPI.getNews({ kategori: 'KÜLTÜR SANAT', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  const { data: educationData } = useQuery(
    'homepage-education',
    () => newsAPI.getNews({ kategori: 'EĞİTİM', limit: 5 }),
    { staleTime: 60000, cacheTime: 300000 }
  );

  // Optimize: Tek API çağrısında daha fazla haber al
  // Breaking news ve kategori haberlerini ana haber listesinden filtrele

  // Memoized data processing for better performance
  const allNews = useMemo(() => {
    // API response yapısı: {success: true, data: [...], message: "", total: X}
    return newsData?.success && Array.isArray(newsData.data) ? newsData.data : [];
  }, [newsData]);

  // Filter news with actual images (not placeholder) - memoized
  const newsWithImages = useMemo(() => {
    return allNews.filter(news =>
      news.main_image &&
      news.main_image !== '/assets/images/placeholder.svg' &&
      news.main_image !== null &&
      news.main_image.trim() !== '' &&
      !news.main_image.includes('placeholder')
    );
  }, [allNews]);

  // Ana haberler için memoized processing
  const { mainNews, featuredNews, sideNews, latestNews } = useMemo(() => {
    return {
      mainNews: newsWithImages[0] || allNews[0], // Görselli haber varsa onu, yoksa ilk haberi al
      featuredNews: newsWithImages.slice(1, 6), // Sadece görselli haberler
      sideNews: newsWithImages.slice(6, 10), // Sadece görselli haberler
      latestNews: allNews.slice(0, 8) // Son haberler (görsel olsun olmasın)
    };
  }, [allNews, newsWithImages]);

  // Kategori haberlerini API'den direkt al - Garantili 5'er haber
  const economyNews = economyData?.success && Array.isArray(economyData.data) ? economyData.data : [];
  const sportsNews = sportsData?.success && Array.isArray(sportsData.data) ? sportsData.data : [];
  const healthNews = healthData?.success && Array.isArray(healthData.data) ? healthData.data : [];
  const techNews = techData?.success && Array.isArray(techData.data) ? techData.data : [];
  const politicsNews = politicsData?.success && Array.isArray(politicsData.data) ? politicsData.data : [];
  const cultureNews = cultureData?.success && Array.isArray(cultureData.data) ? cultureData.data : [];
  const educationNews = educationData?.success && Array.isArray(educationData.data) ? educationData.data : [];

  // handleRetry already defined above before conditional returns

  return (
    <>
      <Helmet>
        <title>MetaAnaliz Haber - Son Haberler</title>
        <meta name="description" content="Türkiye'den ve dünyadan en güncel haberler." />
      </Helmet>

      <div className="homepage">
        <div className="container">
          {newsLoading ? (
            <div className="text-center py-5">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              {/* Creative Hero Section */}
              <section className="creative-hero-section">
                <div className="hero-container">
                  {/* Main Featured News - Large */}
                  {mainNews && (
                    <div className="hero-main-card">
                      <Link to={`/haber/${mainNews.slug || mainNews.haber_kodu}`} className="hero-main-link">
                        <div className="hero-main-image">
                          <LazyLoadImage
                            src={mainNews.main_image || '/assets/images/placeholder.svg'}
                            alt={mainNews.title}
                            effect="blur"
                            onError={(e) => {
                              e.target.src = '/assets/images/placeholder.svg';
                            }}
                          />
                          <div className="hero-main-overlay">
                            {mainNews.son_dakika === 'Evet' && (
                              <span className="hero-breaking-badge">SON DAKİKA</span>
                            )}
                            <div className="hero-main-content">
                              <span className="hero-category">{mainNews.kategori}</span>
                              <h1 className="hero-main-title">
                                {decodeHtmlEntities(stripHtmlTags(mainNews.title))}
                              </h1>
                              <p className="hero-main-excerpt">
                                {decodeHtmlEntities(stripHtmlTags(mainNews.description || '')).substring(0, 150)}...
                              </p>
                              <div className="hero-main-meta">
                                <span className="hero-date">{getRelativeTime(mainNews.pub_date)}</span>
                                <span className="hero-location">{mainNews.sehir}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </div>
                  )}

                  {/* Side News Grid */}
                  <div className="hero-side-grid">
                    {sideNews.slice(0, 4).map((news, index) => (
                      <div key={news.haber_kodu} className={`hero-side-card ${index === 0 ? 'featured' : ''}`}>
                        <Link to={`/haber/${news.slug || news.haber_kodu}`} className="hero-side-link">
                          <div className="hero-side-image">
                            <LazyLoadImage
                              src={news.main_image || '/assets/images/placeholder.svg'}
                              alt={news.title}
                              effect="blur"
                            />
                            {news.son_dakika === 'Evet' && (
                              <span className="hero-side-breaking">SON DAKİKA</span>
                            )}
                          </div>
                          <div className="hero-side-content">
                            <span className="hero-side-category">{news.kategori}</span>
                            <h3 className="hero-side-title">
                              {decodeHtmlEntities(truncateText(news.title, index === 0 ? 100 : 80))}
                            </h3>
                            <div className="hero-side-meta">
                              <span className="hero-side-date">{getRelativeTime(news.pub_date)}</span>
                            </div>
                          </div>
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Featured News Grid */}
              <section className="featured-news-section">
                <div className="container">
                  <div className="section-header">
                    <h2 className="section-title">Öne Çıkan Haberler</h2>
                  </div>
                  <div className="row">
                    {featuredNews.map((news) => (
                      <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                        <NewsCard
                          news={news}
                          variant="default"
                          showCategory={true}
                          showDate={true}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Video News Section */}
              <VideoNewsSection />

              {/* Latest News Section */}
              <section className="latest-news-section">
                <div className="container">
                  <div className="section-header">
                    <h2 className="section-title">Son Haberler</h2>
                    <Link to="/son-haberler" className="section-link">Tümünü Gör</Link>
                  </div>
                  <div className="row">
                    {latestNews.slice(0, 5).map((news) => (
                      <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                        <NewsCard
                          news={news}
                          variant="default"
                          showCategory={true}
                          showDate={true}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Economy News Section */}
              {economyNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Ekonomi Haberleri</h2>
                      <Link to="/kategori/EKONOMİ" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {economyNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}

              {/* Sports News Section */}
              {sportsNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Spor Haberleri</h2>
                      <Link to="/kategori/SPOR" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {sportsNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}

              {/* Health News Section */}
              {healthNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Sağlık Haberleri</h2>
                      <Link to="/kategori/SAĞLIK" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {healthNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}

              {/* Technology News Section */}
              {techNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Bilim ve Teknoloji Haberleri</h2>
                      <Link to="/kategori/BİLİM VE TEKNOLOJİ" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {techNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}

              {/* Local News Section */}
              <LocalNewsSection />

              {/* Politics News Section */}
              {politicsNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Politika Haberleri</h2>
                      <Link to="/kategori/POLİTİKA" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {politicsNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}

              {/* Culture News Section */}
              {cultureNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Kültür Sanat Haberleri</h2>
                      <Link to="/kategori/KÜLTÜR SANAT" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {cultureNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}



              {/* Education News Section */}
              {educationNews?.length > 0 && (
                <section className="category-news-section">
                  <div className="container">
                    <div className="section-header">
                      <h2 className="section-title">Eğitim Haberleri</h2>
                      <Link to="/kategori/EĞİTİM" className="section-link">Tümünü Gör</Link>
                    </div>
                    <div className="row">
                      {educationNews.map((news) => (
                        <div key={news.haber_kodu} className="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4">
                          <NewsCard
                            news={news}
                            variant="default"
                            showCategory={true}
                            showDate={true}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default HomePage;