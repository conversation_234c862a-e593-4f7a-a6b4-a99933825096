import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';

// Styles
import '../assets/css/category.css';

// Components
import NewsGrid from '../components/News/NewsGrid';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

const VideoNewsPage = () => {
  const [page, setPage] = useState(1);
  const [allNews, setAllNews] = useState([]);
  const { setCurrentCategory } = useNews();
  const queryClient = useQueryClient();

  // Update current category
  useEffect(() => {
    setCurrentCategory('Video Haberler');
    setPage(1);
    setAllNews([]);

    // Clear cache for video news to ensure fresh data
    queryClient.removeQueries(['video-news-page']);

    return () => setCurrentCategory('');
  }, [setCurrentCategory, queryClient]);

  // Fetch video news
  const {
    data: newsData,
    isLoading: newsLoading
  } = useQuery(
    ['video-news-page', page],
    () => newsAPI.getVideoNews({ page, limit: 12 }),
    {
      keepPreviousData: true,
      enabled: true,
      retry: 3,
      retryDelay: 1000,
      staleTime: 0, // Always fetch fresh data
      cacheTime: 1000 * 60 * 5, // Cache for 5 minutes
      onSuccess: (data) => {
        if (data.success && Array.isArray(data.data)) {
          if (page === 1) {
            setAllNews(data.data);
          } else {
            setAllNews(prev => [...prev, ...data.data]);
          }
        }
      }
    }
  );

  const hasMoreNews = newsData?.success && newsData.data?.length === 12;
  const isInitialLoading = newsLoading && page === 1;

  // Load more news
  const loadMoreNews = () => {
    if (hasMoreNews && !newsLoading) {
      setPage(prev => prev + 1);
    }
  };

  // Scroll to top on mount
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <Helmet>
        <title>Video Haberler - MetaAnaliz Haber</title>
        <meta name="description" content="En güncel video haberler ve görüntülü haberler." />
      </Helmet>

      <div className="category-page">
        {/* Page Header - Full Width */}
        <div className="page-header">
          <div className="container">
            <h1 className="page-title">
              <span className="category-name">Video</span>
              <span className="category-subtitle"> Haberler</span>
            </h1>
            <div className="category-breadcrumb">
              <nav aria-label="breadcrumb">
                <ol className="breadcrumb">
                  <li className="breadcrumb-item">
                    <Link to="/">Ana Sayfa</Link>
                  </li>
                  <li className="breadcrumb-item active">Video Haberler</li>
                </ol>
              </nav>
            </div>
          </div>
        </div>

        {/* Category Content */}
        <div className="category-news-section">
          <div className="container">
            {isInitialLoading ? (
              <div className="text-center py-5">
                <LoadingSpinner text="Video haberler yükleniyor..." />
              </div>
            ) : allNews.length === 0 && !newsLoading ? (
              <div className="alert alert-info text-center">
                <i className="fas fa-info-circle me-2"></i>
                Şu anda video haber bulunmuyor.
              </div>
            ) : (
              <>
                <NewsGrid news={allNews} showCategory={true} variant="video" />

                {/* Load More Section */}
                {hasMoreNews && (
                  <div className="text-center mt-5">
                    <button
                      className="btn btn-outline-primary btn-lg px-5"
                      onClick={loadMoreNews}
                      disabled={newsLoading}
                    >
                      {newsLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Yükleniyor...
                        </>
                      ) : (
                        'Daha Fazla Video Haber Yükle'
                      )}
                    </button>
                  </div>
                )}

                {/* Loading indicator for pagination */}
                {newsLoading && page > 1 && (
                  <div className="text-center py-3">
                    <LoadingSpinner />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default VideoNewsPage;
