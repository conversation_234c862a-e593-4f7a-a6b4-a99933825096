import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';

// Components
import NewsGrid from '../components/News/NewsGrid';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

// Category specific styles
import '../assets/css/category.css';

const VideoPage = () => {
  const [page, setPage] = useState(1);
  const [allNews, setAllNews] = useState([]);
  const { setCurrentCategory } = useNews();

  // Update current category
  React.useEffect(() => {
    setCurrentCategory('Video Haberler');
    return () => setCurrentCategory('');
  }, [setCurrentCategory]);

  // Fetch video news (has_videos = true)
  const {
    data: newsData,
    isLoading: newsLoading,
    error: newsError,
    refetch: refetchNews
  } = useQuery(
    ['video-news', page],
    () => newsAPI.getNews({
      limit: 12,
      page: page,
      has_videos: true // Video içeren haberleri filtrele
    }),
    {
      keepPreviousData: true,
      onSuccess: (data) => {
        if (page === 1) {
          setAllNews(data?.data?.data || []);
        } else {
          setAllNews(prev => [...prev, ...(data?.data?.data || [])]);
        }
      }
    }
  );

  if (newsError) {
    return (
      <div className="container py-5">
        <ErrorMessage 
          message="Videolu haberler yüklenirken bir hata oluştu." 
          onRetry={refetchNews}
        />
      </div>
    );
  }

  const totalCount = newsData?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / 12);
  const hasMoreNews = page < totalPages;

  const loadMoreNews = () => {
    if (hasMoreNews && !newsLoading) {
      setPage(prev => prev + 1);
    }
  };

  return (
    <>
      <Helmet>
        <title>Videolu Haberler - MetaAnaliz Haber</title>
        <meta name="description" content="Video içeren güncel haberler ve gelişmeler." />
      </Helmet>

      {/* Page Header */}
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">
            <i className="fas fa-play-circle me-3"></i>
            Videolu Haberler
          </h1>
          <nav aria-label="breadcrumb">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <Link to="/">Ana Sayfa</Link>
              </li>
              <li className="breadcrumb-item active" aria-current="page">
                Videolu Haberler
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* News Section */}
      <div className="container">
        <div className="category-news-section">
          {newsLoading && page === 1 ? (
            <div className="text-center py-5">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              {/* News Grid */}
              <NewsGrid news={allNews} />

              {/* Load More Section */}
              {hasMoreNews && (
                <div className="text-center mt-4">
                  <button
                    className="btn btn-outline-primary btn-lg px-5"
                    onClick={loadMoreNews}
                    disabled={newsLoading}
                  >
                    {newsLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Yükleniyor...
                      </>
                    ) : (
                      'Daha Fazla Video Yükle'
                    )}
                  </button>
                </div>
              )}

              {/* Loading indicator for pagination */}
              {newsLoading && page > 1 && (
                <div className="text-center py-3">
                  <LoadingSpinner />
                </div>
              )}

              {/* No Results */}
              {allNews.length === 0 && !newsLoading && (
                <div className="text-center py-5">
                  <div className="no-results">
                    <i className="fas fa-video fa-3x text-muted mb-3"></i>
                    <h3>Videolu haber bulunamadı</h3>
                    <p className="text-muted">Şu anda videolu haber bulunmamaktadır.</p>
                    <Link to="/" className="btn btn-primary">
                      Ana Sayfaya Dön
                    </Link>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default VideoPage;
