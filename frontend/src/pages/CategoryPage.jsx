import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';
import { getOriginalCategoryName, fixCategoryName } from '../utils/urlUtils';

// Styles
import '../assets/css/category.css';

// Components
import NewsGrid from '../components/News/NewsGrid';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

const CategoryPage = () => {
  const { categoryName } = useParams();
  const [page, setPage] = useState(1);
  const [allNews, setAllNews] = useState([]);
  const [isCategoryChanging, setIsCategoryChanging] = useState(false);
  const { setCurrentCategory } = useNews();

  // URL slug'ından orijinal kategori adını al ve Türkçe karakterleri düzelt
  const rawCategoryName = getOriginalCategoryName(categoryName);
  const originalCategoryName = fixCategoryName(rawCategoryName);

  // Kategori adını başlık formatına çevir (sadece ilk harf büyük)
  const formatCategoryTitle = (categoryName) => {
    if (!categoryName) return '';
    // Zaten düzeltilmiş kategori adını kullan
    return categoryName.toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Update current category when the parameter changes
  useEffect(() => {
    setCurrentCategory(originalCategoryName);
    setPage(1);
    setAllNews([]);
    setIsCategoryChanging(true);

    return () => setCurrentCategory('');
  }, [categoryName, originalCategoryName, setCurrentCategory]);

  // Fetch news for this category
  const {
    data: newsData,
    isLoading: newsLoading,
    error: newsError,
    refetch: refetchNews
  } = useQuery(
    ['category-news', rawCategoryName, page],
    () => newsAPI.getNews({ kategori: rawCategoryName, page, limit: 12 }),
    {
      keepPreviousData: true,
      enabled: !!rawCategoryName && !!categoryName, // Hem URL param hem de ham kategori adı hazır olmalı
      retry: 3,
      retryDelay: 1000,
      onSuccess: (data) => {
        if (data.success && Array.isArray(data.data)) {
          if (page === 1) {
            setAllNews(data.data);
          } else {
            setAllNews(prev => [...prev, ...data.data]);
          }
        }
        setIsCategoryChanging(false);
      }
    }
  );

  // Loading state - kategori adı henüz hazır değilse veya kategori değişiyorsa loading göster
  const isInitialLoading = !originalCategoryName || (newsLoading && page === 1) || isCategoryChanging;

  // Load more news
  const loadMoreNews = () => {
    if (newsData?.pagination?.current_page < newsData?.pagination?.total_pages) {
      setPage(prev => prev + 1);
    }
  };
  
  // Check if there are more news to load
  const hasMoreNews = newsData?.pagination?.current_page < newsData?.pagination?.total_pages;
  
  if (newsError) {
    return (
      <div className="container">
        <ErrorMessage 
          message="Haberler yüklenirken bir hata oluştu." 
          onRetry={() => {
            refetchNews();
          }}
        />
      </div>
    );
  }
  
  return (
    <>
      <Helmet>
        <title>{originalCategoryName} Haberleri - MetaAnaliz Haber</title>
        <meta name="description" content={`${originalCategoryName} kategorisindeki en güncel haberler. MetaAnaliz Haber'de ${originalCategoryName} ile ilgili tüm gelişmeleri takip edin.`} />
      </Helmet>

      <div className="category-page">
        {/* Page Header - Full Width */}
        <div className="page-header">
        <div className="container">
          <h1 className="page-title">
            <span className="category-name">{formatCategoryTitle(originalCategoryName)}</span>
            <span className="category-subtitle"> Haberleri</span>
          </h1>
          <div className="category-breadcrumb">
            <nav aria-label="breadcrumb">
              <ol className="breadcrumb">
                <li className="breadcrumb-item">
                  <Link to="/">Ana Sayfa</Link>
                </li>
                <li className="breadcrumb-item active">{originalCategoryName}</li>
              </ol>
            </nav>
          </div>
        </div>
      </div>

      {/* Category Content */}
      <div className="category-news-section">
        <div className="container">
          {isInitialLoading ? (
            <div className="text-center py-5">
              <LoadingSpinner text="Haberler yükleniyor..." />
            </div>
          ) : allNews.length === 0 && !newsLoading && originalCategoryName ? (
            <div className="alert alert-info text-center">
              <i className="fas fa-info-circle me-2"></i>
              Bu kategoride henüz haber bulunmuyor.
            </div>
          ) : (
            <>
              <NewsGrid news={allNews} showCategory={false} variant="default" />

              {/* Load More Section */}
              {hasMoreNews && (
                <div className="text-center mt-5">
                  <button
                    className="btn btn-outline-primary btn-lg px-5"
                    onClick={loadMoreNews}
                    disabled={newsLoading}
                  >
                    {newsLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Yükleniyor...
                      </>
                    ) : (
                      'Daha Fazla Haber Yükle'
                    )}
                  </button>
                </div>
              )}

              {/* Loading indicator for pagination */}
              {newsLoading && page > 1 && (
                <div className="text-center py-3">
                  <LoadingSpinner />
                </div>
              )}
            </>
          )}
        </div>
      </div>
      </div>
    </>
  );
};

export default CategoryPage;
