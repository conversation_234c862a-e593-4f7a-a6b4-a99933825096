import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const NotFoundPage = () => {
  return (
    <>
      <Helmet>
        <title>Sayfa Bulunamadı - MetaAnaliz Haber</title>
        <meta name="description" content="Aradığınız sayfa bulunamadı." />
      </Helmet>
      
      <div className="container">
        <div className="not-found-page text-center py-5">
          <p className="error-message">
            Aradığınız sayfa bulunamadı veya taşınmış olabilir.
          </p>
          <div className="error-actions mt-4">
            <Link to="/" className="btn btn-primary btn-lg">
              <i className="fas fa-home me-2"></i>
              Ana Sayfaya Dön
            </Link>
          </div>
        
        </div>
      </div>
    </>
  );
};

export default NotFoundPage;
