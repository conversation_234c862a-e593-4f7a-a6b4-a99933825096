import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';

// Components
import NewsGrid from '../components/News/NewsGrid';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

// Category specific styles
import '../assets/css/category.css';

const LatestNewsPage = () => {
  const [page, setPage] = useState(1);
  const [allNews, setAllNews] = useState([]);
  const { setCurrentCategory } = useNews();

  // Update current category
  React.useEffect(() => {
    setCurrentCategory('<PERSON> Haberler');
    return () => setCurrentCategory('');
  }, [setCurrentCategory]);

  // Fetch latest news
  const {
    data: newsData,
    isLoading: newsLoading,
    error: newsError,
    refetch: refetchNews
  } = useQuery(
    ['latest-news', page], 
    () => newsAPI.getNews({ 
      limit: 12,
      page: page,
      sort: 'pub_date',
      order: 'DESC'
    }),
    {
      keepPreviousData: true,
      onSuccess: (data) => {
        const newsArray = data?.success && Array.isArray(data.data) ? data.data : [];
        if (page === 1) {
          setAllNews(newsArray);
        } else {
          setAllNews(prev => [...prev, ...newsArray]);
        }
      }
    }
  );

  if (newsError) {
    return (
      <div className="container py-5">
        <ErrorMessage 
          message="Son haberler yüklenirken bir hata oluştu." 
          onRetry={refetchNews}
        />
      </div>
    );
  }

  const totalCount = newsData?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / 12);
  const hasMoreNews = page < totalPages;

  const loadMoreNews = () => {
    if (hasMoreNews && !newsLoading) {
      setPage(prev => prev + 1);
    }
  };

  return (
    <>
      <Helmet>
        <title>Son Haberler - MetaAnaliz Haber</title>
        <meta name="description" content="En güncel haberler ve son dakika gelişmeleri." />
      </Helmet>

      {/* Page Header */}
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">
            <i className="fas fa-clock me-3"></i>
            Son Haberler
          </h1>
          <nav aria-label="breadcrumb">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <Link to="/">Ana Sayfa</Link>
              </li>
              <li className="breadcrumb-item active" aria-current="page">
                Son Haberler
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* News Section */}
      <div className="container">
        <div className="category-news-section">
          {newsLoading && page === 1 ? (
            <div className="text-center py-5">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              {/* News Grid */}
              <NewsGrid news={allNews} />
              
              {/* Load More Section */}
              {hasMoreNews && (
                <div className="text-center mt-4">
                  <button
                    className="btn btn-outline-primary btn-lg px-5"
                    onClick={loadMoreNews}
                    disabled={newsLoading}
                  >
                    {newsLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Yükleniyor...
                      </>
                    ) : (
                      'Daha Fazla Haber Yükle'
                    )}
                  </button>
                </div>
              )}

              {/* Loading indicator for pagination */}
              {newsLoading && page > 1 && (
                <div className="text-center py-3">
                  <LoadingSpinner />
                </div>
              )}

              {/* No Results */}
              {allNews.length === 0 && !newsLoading && (
                <div className="text-center py-5">
                  <div className="no-results">
                    <i className="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h3>Haber bulunamadı</h3>
                    <p className="text-muted">Şu anda haber bulunmamaktadır.</p>
                    <Link to="/" className="btn btn-primary">
                      Ana Sayfaya Dön
                    </Link>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default LatestNewsPage;
