import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';
import { stripHtmlTags, decodeHtmlEntities } from '../utils/textUtils';
import WorldNewsSlider from '../components/News/WorldNewsSlider';

import LoadingSpinner from '../components/UI/LoadingSpinner';
import { LazyLoadImage } from 'react-lazy-load-image-component';

const NewHomePage = () => {
  const { getRelativeTime, truncateText } = useNews();
  const [currentSlide, setCurrentSlide] = useState(0);

  // Helper function to create proper news URL
  const createNewsUrl = (newsItem) => {
    if (newsItem.slug && newsItem.slug.trim() !== '') {
      return `/haber/${newsItem.slug}`;
    }
    return `/haber/${newsItem.haber_kodu}`;
  };

  // Fetch all news - daha fazla haber çek çünkü görselli olanları filtreleyeceğiz
  const { data: newsData, isLoading } = useQuery(
    'homepage-news',
    () => newsAPI.getNews({ limit: 100, sort: 'pub_date', order: 'DESC' }),
    {
      staleTime: 60000,
      cacheTime: 300000,
      refetchInterval: 120000,
    }
  );

  // Fetch category news
  const { data: economyData } = useQuery(
    'homepage-economy',
    () => newsAPI.getNews({ kategori: 'EKONOMİ', limit: 8, sort: 'pub_date', order: 'DESC' }),
    {
      staleTime: 30000,
      cacheTime: 60000,
      refetchInterval: 60000,
      refetchOnWindowFocus: true
    }
  );

  const { data: sportsData } = useQuery(
    'homepage-sports',
    () => newsAPI.getNews({ kategori: 'SPOR', limit: 8, sort: 'pub_date', order: 'DESC' }),
    {
      staleTime: 30000,
      cacheTime: 60000,
      refetchInterval: 60000,
      refetchOnWindowFocus: true
    }
  );



  // Process news data
  const allNews = newsData?.success && Array.isArray(newsData.data) ? newsData.data : [];

  // Hero slider için sadece gerçek görseli olan haberleri filtrele ve ilk 15'ini al
  // Default/placeholder görseli olan haberler hariç
  const newsWithImages = allNews
    .filter(news =>
      news.main_image &&
      news.main_image !== null &&
      news.main_image !== '/assets/images/placeholder.svg' &&
      news.main_image.trim() !== '' &&
      !news.main_image.includes('placeholder')
    )
    .sort((a, b) => new Date(b.pub_date) - new Date(a.pub_date));
  const heroNews = newsWithImages.slice(0, 15);

  // Hero slider'da olmayan görselli haberlerden 4 tanesini yan liste için al
  const sidebarNews = newsWithImages
    .filter(news => !heroNews.some(heroItem => heroItem.haber_kodu === news.haber_kodu))
    .slice(0, 4);

  // Öne çıkan haberler: Hero ve sidebar'da olmayan haberlerden 8 tane
  const featuredNews = newsWithImages
    .filter(news =>
      !heroNews.some(heroItem => heroItem.haber_kodu === news.haber_kodu) &&
      !sidebarNews.some(sidebarItem => sidebarItem.haber_kodu === news.haber_kodu)
    )
    .slice(0, 8);

  // Son haberler: Hero, sidebar ve featured'da olmayan haberlerden 8 tane
  const latestNews = newsWithImages
    .filter(news =>
      !heroNews.some(heroItem => heroItem.haber_kodu === news.haber_kodu) &&
      !sidebarNews.some(sidebarItem => sidebarItem.haber_kodu === news.haber_kodu) &&
      !featuredNews.some(featuredItem => featuredItem.haber_kodu === news.haber_kodu)
    )
    .slice(0, 8);

  const economyNews = economyData?.success && Array.isArray(economyData.data) ? economyData.data : [];
  const sportsNews = sportsData?.success && Array.isArray(sportsData.data) ? sportsData.data : [];

  // Reset slider to first slide when component mounts
  useEffect(() => {
    setCurrentSlide(0);
  }, []);

  // Hero slider auto-play
  useEffect(() => {
    if (heroNews.length > 1) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % heroNews.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [heroNews.length]);

  // Touch/Swipe functionality
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [isDragging, setIsDragging] = useState(false);

  const minSwipeDistance = 50;

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setIsDragging(true);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      setIsDragging(false);
      return;
    }

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && heroNews.length > 1) {
      nextSlide();
    }
    if (isRightSwipe && heroNews.length > 1) {
      prevSlide();
    }

    setIsDragging(false);
  };

  // Mouse drag functionality
  const [mouseStart, setMouseStart] = useState(null);
  const [mouseEnd, setMouseEnd] = useState(null);
  const [isMouseDragging, setIsMouseDragging] = useState(false);

  const onMouseDown = (e) => {
    setMouseEnd(null);
    setMouseStart(e.clientX);
    setIsMouseDragging(true);
  };

  const onMouseMove = (e) => {
    if (!isMouseDragging) return;
    setMouseEnd(e.clientX);
  };

  const onMouseUp = () => {
    if (!mouseStart || !mouseEnd || !isMouseDragging) {
      setIsMouseDragging(false);
      return;
    }

    const distance = mouseStart - mouseEnd;
    const isLeftDrag = distance > minSwipeDistance;
    const isRightDrag = distance < -minSwipeDistance;

    if (isLeftDrag && heroNews.length > 1) {
      nextSlide();
    }
    if (isRightDrag && heroNews.length > 1) {
      prevSlide();
    }

    setIsMouseDragging(false);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroNews.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroNews.length) % heroNews.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>MetaAnaliz Haber - Son Haberler</title>
        <meta name="description" content="Türkiye'den ve dünyadan en güncel haberler." />
      </Helmet>

      <div className="new-homepage">
        {/* Hero Slider Section */}
        <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              {/* Hero Slider */}
              <div className="hero-slider">
                <div className="slider-container">
            <div
              className="slider-wrapper"
              style={{
                transform: `translateX(-${currentSlide * 100}%)`,
                width: `${heroNews.length * 100}%`
              }}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
              onMouseDown={onMouseDown}
              onMouseMove={onMouseMove}
              onMouseUp={onMouseUp}
              onMouseLeave={onMouseUp}
            >
              {heroNews.map((news, index) => (
                <div key={`slide-${news.haber_kodu}-${index}`} className="slide">
                  <Link to={createNewsUrl(news)}>
                    <div className="slide-image">
                      <LazyLoadImage
                        src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                        alt={news.title}
                        effect="blur"
                        onError={(e) => {
                          e.target.src = '/assets/images/placeholder.svg';
                        }}
                      />
                      <div className="slide-overlay">
                        <div className="slide-content">
                          {news.kategori && (
                            <span className="slide-category">{news.kategori}</span>
                          )}
                          <h2 className="slide-title">
                            {decodeHtmlEntities(stripHtmlTags(news.title))}
                          </h2>
                          <p className="slide-summary">
                            {decodeHtmlEntities(truncateText(stripHtmlTags(news.summary || news.content), 150))}
                          </p>
                          <div className="slide-meta">
                            <span className="slide-date">{getRelativeTime(news.pub_date)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Slider Controls */}
            {heroNews.length > 1 && (
              <>
                <button className="slider-btn slider-btn-prev" onClick={prevSlide}>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button className="slider-btn slider-btn-next" onClick={nextSlide}>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </>
            )}

            {/* Slider Dots */}
            {heroNews.length > 1 && (
              <div className="slider-dots">
                {heroNews.map((_, index) => (
                  <button
                    key={`dot-${index}`}
                    className={`slider-dot ${index === currentSlide ? 'active' : ''}`}
                    onClick={() => goToSlide(index)}
                    aria-label={`Slide ${index + 1}'e git`}
                  />
                ))}
              </div>
            )}
                </div>
              </div>

              {/* Sidebar News */}
              <div className="hero-sidebar">
                <div className="sidebar-news-list">
                  {sidebarNews.map((news, index) => (
                    <article key={news.haber_kodu} className="sidebar-news-item">
                      <Link to={createNewsUrl(news)}>
                        <div className="sidebar-news-image">
                          <LazyLoadImage
                            src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                            alt={news.title}
                            effect="blur"
                            onError={(e) => {
                              e.target.src = '/assets/images/placeholder.svg';
                            }}
                          />
                          {news.kategori && (
                            <span className="sidebar-news-category">{news.kategori}</span>
                          )}
                        </div>
                        <div className="sidebar-news-content">
                          <h4 className="sidebar-news-title">{decodeHtmlEntities(stripHtmlTags(truncateText(news.title, 80)))}</h4>
                          <div className="sidebar-news-meta">
                            <span className="sidebar-news-date">{getRelativeTime(news.pub_date)}</span>
                          </div>
                        </div>
                      </Link>
                    </article>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured News Section */}
        <section className="featured-section">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">Öne Çıkan Haberler</h2>
            </div>
            <div className="news-grid">
              {featuredNews.map((news) => (
                <article key={news.haber_kodu} className="news-card">
                  <Link to={createNewsUrl(news)}>
                    <div className="card-image">
                      <LazyLoadImage
                        src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                        alt={news.title}
                        effect="blur"
                        onError={(e) => {
                          e.target.src = '/assets/images/placeholder.svg';
                        }}
                      />
                      {news.kategori && (
                        <span className="card-category">{news.kategori}</span>
                      )}
                    </div>
                    <div className="card-content">
                      <h3 className="card-title">
                        {decodeHtmlEntities(stripHtmlTags(news.title))}
                      </h3>
                      <div className="card-meta">
                        <span className="card-date">{getRelativeTime(news.pub_date)}</span>
                      </div>
                    </div>
                  </Link>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* Latest News Section */}
        <section className="latest-section">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">Son Haberler</h2>
              <Link to="/son-haberler" className="section-link">Tümünü Gör</Link>
            </div>
            <div className="news-grid">
              {latestNews.map((news) => (
                <article key={news.haber_kodu} className="news-card">
                  <Link to={createNewsUrl(news)}>
                    <div className="card-image">
                      <LazyLoadImage
                        src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                        alt={news.title}
                        effect="blur"
                        onError={(e) => {
                          e.target.src = '/assets/images/placeholder.svg';
                        }}
                      />
                      {news.kategori && (
                        <span className="card-category">{news.kategori}</span>
                      )}
                    </div>
                    <div className="card-content">
                      <h3 className="card-title">
                        {decodeHtmlEntities(stripHtmlTags(news.title))}
                      </h3>
                      <div className="card-meta">
                        <span className="card-date">{getRelativeTime(news.pub_date)}</span>
                      </div>
                    </div>
                  </Link>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* World News Slider */}
        <WorldNewsSlider />

        {/* Category Sections */}
        {economyNews.length > 0 && (
          <section className="category-section">
            <div className="container">
              <div className="section-header">
                <h2 className="section-title">Ekonomi Haberleri</h2>
                <Link to="/kategori/ekonomi" className="section-link">Tümünü Gör</Link>
              </div>
              <div className="news-grid">
                {economyNews.slice(0, 8).map((news) => (
                  <article key={news.haber_kodu} className="news-card">
                    <Link to={createNewsUrl(news)}>
                      <div className="card-image">
                        <LazyLoadImage
                          src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                          alt={news.title}
                          effect="blur"
                          onError={(e) => {
                            e.target.src = '/assets/images/placeholder.svg';
                          }}
                        />
                        <span className="card-category">EKONOMİ</span>
                      </div>
                      <div className="card-content">
                        <h3 className="card-title">
                          {decodeHtmlEntities(stripHtmlTags(news.title))}
                        </h3>
                        <div className="card-meta">
                          <span className="card-date">{getRelativeTime(news.pub_date)}</span>
                        </div>
                      </div>
                    </Link>
                  </article>
                ))}
              </div>
            </div>
          </section>
        )}

        {sportsNews.length > 0 && (
          <section className="category-section">
            <div className="container">
              <div className="section-header">
                <h2 className="section-title">Spor Haberleri</h2>
                <Link to="/kategori/spor" className="section-link">Tümünü Gör</Link>
              </div>
              <div className="news-grid">
                {sportsNews.slice(0, 8).map((news) => (
                  <article key={news.haber_kodu} className="news-card">
                    <Link to={createNewsUrl(news)}>
                      <div className="card-image">
                        <LazyLoadImage
                          src={news.main_image || news.featured_image || '/assets/images/placeholder.svg'}
                          alt={news.title}
                          effect="blur"
                          onError={(e) => {
                            e.target.src = '/assets/images/placeholder.svg';
                          }}
                        />
                        <span className="card-category">SPOR</span>
                      </div>
                      <div className="card-content">
                        <h3 className="card-title">
                          {decodeHtmlEntities(stripHtmlTags(news.title))}
                        </h3>
                        <div className="card-meta">
                          <span className="card-date">{getRelativeTime(news.pub_date)}</span>
                        </div>
                      </div>
                    </Link>
                  </article>
                ))}
              </div>
            </div>
          </section>
        )}
      </div>
    </>
  );
};

export default NewHomePage;
