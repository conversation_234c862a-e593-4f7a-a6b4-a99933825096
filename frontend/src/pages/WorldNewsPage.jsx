import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';

// Styles
import '../assets/css/category.css';

// Components
import NewsGrid from '../components/News/NewsGrid';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const WorldNewsPage = () => {
  const [page, setPage] = useState(1);
  const [allNews, setAllNews] = useState([]);
  const { setCurrentCategory } = useNews();

  // Update current category
  useEffect(() => {
    setCurrentCategory('Dünya Haberleri');
    setPage(1);
    setAllNews([]);

    return () => setCurrentCategory('');
  }, [setCurrentCategory]);

  // Fetch world news
  const {
    data: newsData,
    isLoading: newsLoading,
    error: newsError,
    refetch: refetchNews
  } = useQuery(
    ['world-news-page', page],
    () => newsAPI.getWorldNews({ page, limit: 12 }),
    {
      keepPreviousData: true,
      enabled: true,
      retry: 3,
      retryDelay: 1000,
      onSuccess: (data) => {
        if (data.success && Array.isArray(data.data)) {
          if (page === 1) {
            setAllNews(data.data);
          } else {
            setAllNews(prev => [...prev, ...data.data]);
          }
        }
      }
    }
  );

  const hasMoreNews = newsData?.success && newsData.data?.length === 12;
  const isInitialLoading = newsLoading && page === 1;

  // Load more news
  const loadMoreNews = () => {
    if (hasMoreNews && !newsLoading) {
      setPage(prev => prev + 1);
    }
  };

  // Scroll to top on mount
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <Helmet>
        <title>Dünya Haberleri - MetaAnaliz Haber</title>
        <meta name="description" content="Dünyadan en güncel haberler ve uluslararası gelişmeler." />
      </Helmet>

      <div className="category-page">
        {/* Page Header - Full Width */}
        <div className="page-header">
          <div className="container">
            <h1 className="page-title">
              <span className="category-name">Dünya</span>
              <span className="category-subtitle"> Haberleri</span>
            </h1>
            <div className="category-breadcrumb">
              <nav aria-label="breadcrumb">
                <ol className="breadcrumb">
                  <li className="breadcrumb-item">
                    <Link to="/">Ana Sayfa</Link>
                  </li>
                  <li className="breadcrumb-item active">Dünya Haberleri</li>
                </ol>
              </nav>
            </div>
          </div>
        </div>

        {/* Category Content */}
        <div className="category-news-section">
          <div className="container">
            {isInitialLoading ? (
              <div className="text-center py-5">
                <LoadingSpinner text="Dünya haberleri yükleniyor..." />
              </div>
            ) : allNews.length === 0 && !newsLoading ? (
              <div className="alert alert-info text-center">
                <i className="fas fa-info-circle me-2"></i>
                Şu anda dünya haberi bulunmuyor.
              </div>
            ) : (
              <>
                <NewsGrid news={allNews} showCategory={true} variant="world" />

                {/* Load More Section */}
                {hasMoreNews && (
                  <div className="text-center mt-5">
                    <button
                      className="btn btn-outline-primary btn-lg px-5"
                      onClick={loadMoreNews}
                      disabled={newsLoading}
                    >
                      {newsLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Yükleniyor...
                        </>
                      ) : (
                        'Daha Fazla Dünya Haberi Yükle'
                      )}
                    </button>
                  </div>
                )}

                {/* Loading indicator for pagination */}
                {newsLoading && page > 1 && (
                  <div className="text-center py-3">
                    <LoadingSpinner />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default WorldNewsPage;
