import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { newsAPI } from '../services/api';

// Components
import NewsGrid from '../components/News/NewsGrid';
import Sidebar from '../components/Layout/Sidebar';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

const SearchPage = () => {
  const [searchParams] = useSearchParams();
  const [page, setPage] = useState(1);
  const [allResults, setAllResults] = useState([]);
  
  const query = searchParams.get('q') || '';
  
  // Reset page when query changes
  useEffect(() => {
    setPage(1);
    setAllResults([]);
  }, [query]);
  
  // Fetch search results
  const { 
    data: searchData, 
    isLoading, 
    error,
    refetch
  } = useQuery(
    ['search', query, page],
    () => newsAPI.searchNews(query, { page, limit: 12 }),
    {
      enabled: !!query,
      keepPreviousData: true,
      onSuccess: (data) => {
        if (data.success && data.data?.data) {
          if (page === 1) {
            setAllResults(data.data.data);
          } else {
            setAllResults(prev => [...prev, ...data.data.data]);
          }
        }
      }
    }
  );
  
  // Load more results
  const loadMoreResults = () => {
    if (searchData?.pagination?.current_page < searchData?.pagination?.total_pages) {
      setPage(prev => prev + 1);
    }
  };
  
  // Check if there are more results to load
  const hasMoreResults = searchData?.pagination?.current_page < searchData?.pagination?.total_pages;
  
  if (!query) {
    return (
      <div className="container">
        <div className="page-header">
          <h1 className="page-title">Arama</h1>
          <div className="page-breadcrumb">
            <nav aria-label="breadcrumb">
              <ol className="breadcrumb">
                <li className="breadcrumb-item">
                  <Link to="/">Ana Sayfa</Link>
                </li>
                <li className="breadcrumb-item active">Arama</li>
              </ol>
            </nav>
          </div>
        </div>
        
        <div className="alert alert-info">
          Arama yapmak için bir kelime girin.
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container">
        <ErrorMessage 
          message="Arama sonuçları yüklenirken bir hata oluştu." 
          onRetry={refetch}
        />
      </div>
    );
  }
  
  return (
    <>
      <Helmet>
        <title>{query} - Arama Sonuçları | MetaAnaliz Haber</title>
        <meta name="description" content={`"${query}" için arama sonuçları. MetaAnaliz Haber'de ${query} ile ilgili haberler.`} />
      </Helmet>
      
      <div className="container">
        {/* Page Header */}
        <div className="page-header">
          <h1 className="page-title">
            "{query}" için Arama Sonuçları
          </h1>
          <div className="page-breadcrumb">
            <nav aria-label="breadcrumb">
              <ol className="breadcrumb">
                <li className="breadcrumb-item">
                  <Link to="/">Ana Sayfa</Link>
                </li>
                <li className="breadcrumb-item active">Arama</li>
              </ol>
            </nav>
          </div>
        </div>
        
        <div className="row">
          {/* Main Content */}
          <div className="col-lg-8">
            {/* Search Results */}
            <section className="search-results">
              {isLoading && page === 1 ? (
                <div className="text-center py-5">
                  <LoadingSpinner />
                </div>
              ) : searchData?.data?.data?.length === 0 ? (
                <div className="alert alert-warning">
                  <h4>Sonuç bulunamadı</h4>
                  <p>"{query}" için herhangi bir haber bulunamadı.</p>
                  <p>Öneriler:</p>
                  <ul>
                    <li>Farklı kelimeler deneyin</li>
                    <li>Daha genel terimler kullanın</li>
                    <li>Yazım hatası olup olmadığını kontrol edin</li>
                  </ul>
                </div>
              ) : (
                <>
                  {/* Results Count */}
                  {searchData?.pagination && (
                    <div className="search-info mb-4">
                      <p className="text-muted">
                        <strong>{searchData.pagination.total_count}</strong> sonuç bulundu
                        {searchData.pagination.total_pages > 1 && (
                          <span> (Sayfa {searchData.pagination.current_page} / {searchData.pagination.total_pages})</span>
                        )}
                      </p>
                    </div>
                  )}
                  
                  {/* Results Grid */}
                  <NewsGrid news={allResults} />
                  
                  {/* Load More Section */}
                  {hasMoreResults && (
                    <div className="text-center mt-4">
                      <button 
                        className="btn btn-outline-primary btn-lg"
                        onClick={loadMoreResults}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Yükleniyor...
                          </>
                        ) : (
                          'Daha Fazla Sonuç Yükle'
                        )}
                      </button>
                    </div>
                  )}
                  
                  {/* Loading indicator for pagination */}
                  {isLoading && page > 1 && (
                    <div className="text-center py-3">
                      <LoadingSpinner />
                    </div>
                  )}
                </>
              )}
            </section>
          </div>
          
          {/* Sidebar */}
          <div className="col-lg-4">
            <Sidebar />
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchPage;
