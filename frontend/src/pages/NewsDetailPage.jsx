import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useQuery } from 'react-query';
import { newsAPI } from '../services/api';
import { useNews } from '../context/NewsContext';
import { cleanNewsContent, stripHtmlTags, decodeHtmlEntities } from '../utils/textUtils';

// Components
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';
import RelatedNews from '../components/News/RelatedNews';

// Styles
import '../assets/css/news-detail.css';

const NewsDetailPage = () => {
  const { newsSlug } = useParams();
  const navigate = useNavigate();
  const { formatDate } = useNews();

  // Font boyutu kontrolü için state
  const [fontSize, setFontSize] = useState(() => {
    const savedSize = localStorage.getItem('news-font-size');
    return savedSize ? parseInt(savedSize) : 18;
  });

  // Sesli okuma kontrolü için state
  const [isReading, setIsReading] = useState(false);

  // Font boyutu kontrol fonksiyonları
  const increaseFontSize = () => {
    const newSize = Math.min(fontSize + 2, 24); // Maksimum 24px
    setFontSize(newSize);
    localStorage.setItem('news-font-size', newSize.toString());
  };

  const decreaseFontSize = () => {
    const newSize = Math.max(fontSize - 2, 14); // Minimum 14px
    setFontSize(newSize);
    localStorage.setItem('news-font-size', newSize.toString());
  };

  const resetFontSize = () => {
    setFontSize(18);
    localStorage.setItem('news-font-size', '18');
  };

  // Sesli okuma fonksiyonu
  const toggleSpeech = () => {
    if (isReading) {
      // Okumayı durdur
      window.speechSynthesis.cancel();
      setIsReading(false);
    } else {
      // Okumaya başla
      if ('speechSynthesis' in window && news) {
        const cleanedContent = cleanNewsContent(news.description);
        const textToRead = `${news.title}. ${cleanedContent}`;

        const utterance = new SpeechSynthesisUtterance(textToRead);
        utterance.lang = 'tr-TR';
        utterance.rate = 0.9;
        utterance.pitch = 1;

        utterance.onstart = () => setIsReading(true);
        utterance.onend = () => setIsReading(false);
        utterance.onerror = () => setIsReading(false);

        window.speechSynthesis.speak(utterance);
      }
    }
  };

  // Slug'ı direkt API'ye gönder, API'de handling yapılacak

  // Fetch news detail by slug
  const {
    data: newsData,
    isLoading,
    error,
    refetch
  } = useQuery(
    ['news-detail', newsSlug],
    () => newsAPI.getNewsDetail(newsSlug),
    {
      enabled: !!newsSlug,
      onError: (error) => {
        console.error('Haber detay hatası:', error);
        console.error('Error details:', error.response?.data);
        console.error('Error status:', error.response?.status);
        // DEBUG: Geçici olarak 404 redirect'i devre dışı
        // navigate('/404', { replace: true });
      }
    }
  );

  // Extract news data
  const news = newsData?.success ? newsData.data : null;
  const images = newsData?.data?.images || [];
  const videos = newsData?.data?.videos || [];

  // Function to distribute images between paragraphs
  const distributeImagesInContent = (content, availableImages) => {
    if (!content || availableImages.length <= 1) {
      const cleanedContent = cleanNewsContent(content || '');
      return { contentElements: cleanedContent.split('\n').filter(line => line.trim()), usedImages: [] };
    }

    // İçeriği önce temizle
    const cleanedContent = cleanNewsContent(content);
    const paragraphs = cleanedContent.split('\n').filter(line => line.trim());
    const contentElements = [];
    const usedImages = [];

    // İlk görseli ana görsel olarak kullan (carousel'de gösterilecek)
    const mainImage = availableImages[0];

    // Kalan görselleri paragraflar arasına dağıt
    const imagesToDistribute = availableImages.slice(1);

    if (imagesToDistribute.length === 0) {
      return { contentElements: paragraphs, usedImages: [] };
    }

    // Paragraf sayısına göre görselleri eşit aralıklarla dağıt
    const imageInterval = Math.max(2, Math.floor(paragraphs.length / imagesToDistribute.length));

    paragraphs.forEach((paragraph, index) => {
      contentElements.push({ type: 'paragraph', content: paragraph, index });

      // Her imageInterval paragrafta bir görsel ekle
      if (imagesToDistribute.length > 0 &&
          index > 0 &&
          (index + 1) % imageInterval === 0 &&
          index < paragraphs.length - 1) {
        const imageToAdd = imagesToDistribute.shift();
        if (imageToAdd) {
          contentElements.push({ type: 'image', content: imageToAdd, index: usedImages.length });
          usedImages.push(imageToAdd);
        }
      }
    });

    return { contentElements, usedImages };
  };

  // Debug: Images array'ini logla
  useEffect(() => {
    if (images.length > 0) {
      console.log('Images array:', images);
      console.log('Images count:', images.length);
    }
  }, [images]);

  // Distribute images in content
  const { contentElements, usedImages } = news ?
    distributeImagesInContent(news.description, images) :
    { contentElements: [], usedImages: [] };

  // Carousel için kullanılacak görseller (ana görsel + kullanılmayan görseller)
  const carouselImages = images.length > 0 ? [images[0], ...images.slice(1).filter(img => !usedImages.includes(img))] : [];

  // Image carousel state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Image carousel functions
  const nextImage = () => {
    if (carouselImages.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % carouselImages.length);
    }
  };

  const prevImage = () => {
    if (carouselImages.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + carouselImages.length) % carouselImages.length);
    }
  };

  const selectImage = (index) => {
    setCurrentImageIndex(index);
  };

  // Set document title when news data is loaded
  useEffect(() => {
    if (news?.title) {
      document.title = `${decodeHtmlEntities(news.title)} - MetaAnaliz Haber`;
    }
  }, [news]);
  
  if (isLoading) {
    return (
      <div className="container">
        <div className="text-center py-5">
          <LoadingSpinner />
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container">
        <div className="alert alert-danger">
          <h4>Haber yüklenirken bir hata oluştu</h4>
          <p><strong>Slug:</strong> {newsSlug}</p>
          <p><strong>Hata:</strong> {error.message}</p>
          <p><strong>Status:</strong> {error.response?.status}</p>
          <p><strong>Response:</strong> {JSON.stringify(error.response?.data)}</p>
          <button className="btn btn-primary" onClick={refetch}>
            Tekrar Dene
          </button>
        </div>
      </div>
    );
  }
  
  if (!news) {
    return (
      <div className="container">
        <div className="alert alert-warning">
          Haber bulunamadı.
        </div>
      </div>
    );
  }
  
  return (
    <>
      <Helmet>
        <title>{decodeHtmlEntities(news.title)} - MetaAnaliz Haber</title>
        <meta name="description" content={stripHtmlTags(news.description).substring(0, 160)} />

        {/* Open Graph meta tags */}
        <meta property="og:title" content={`${decodeHtmlEntities(news.title)} - MetaAnaliz Haber`} />
        <meta property="og:description" content={stripHtmlTags(news.description).substring(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={window.location.href} />
        <meta property="og:site_name" content="MetaAnaliz Haber" />
        {images.length > 0 && images[0] && (
          <>
            <meta property="og:image" content={images[0]} />
            <meta property="og:image:width" content="1200" />
            <meta property="og:image:height" content="630" />
            <meta property="og:image:alt" content={decodeHtmlEntities(news.title)} />
            <meta property="og:image:type" content="image/webp" />
          </>
        )}

        {/* Twitter Card meta tags - PHP handles these for better SEO */}
      </Helmet>

      <div className="news-detail-page">
        <div className="container">
          <article className="news-article">
            {/* News Header */}
            <header className="news-header">
              {news.son_dakika === 'Evet' && (
                <span className="breaking-badge">SON DAKİKA</span>
              )}
              <h1 className="news-title">{decodeHtmlEntities(news.title)}</h1>
              <div className="news-meta">
                <div className="news-meta-left">
                  <span className="category-badge">{news.kategori}</span>
                  <span className="news-date">
                    <i className="far fa-clock"></i>
                    {formatDate(news.pub_date)}
                  </span>
                  <span className="news-location">
                    <i className="fas fa-map-marker-alt"></i>
                    {news.sehir}
                  </span>
                </div>
                <div className="news-meta-right">
                  {/* Font boyutu kontrolleri */}
                  <div className="font-controls">
                    <button
                      onClick={decreaseFontSize}
                      className="font-control-btn"
                      title="Yazıyı küçült"
                    >
                      A-
                    </button>
                    <button
                      onClick={resetFontSize}
                      className="font-control-btn active"
                      title="Normal boyut"
                    >
                      A
                    </button>
                    <button
                      onClick={increaseFontSize}
                      className="font-control-btn"
                      title="Yazıyı büyült"
                    >
                      A+
                    </button>
                  </div>
                  {/* Sesli okuma butonu */}
                  <button
                    onClick={toggleSpeech}
                    className={`speech-btn ${isReading ? 'reading' : ''}`}
                    title={isReading ? 'Okumayı durdur' : 'Sesli oku'}
                  >
                    <i className={`fas ${isReading ? 'fa-stop' : 'fa-volume-up'}`}></i>
                    {isReading ? 'Durdur' : 'Sesli Oku'}
                  </button>
                </div>
              </div>
            </header>

            {/* Image Carousel */}
            {(carouselImages.length > 0 || news.main_image) && (
              <div className="news-image-carousel">
                <div className="carousel-container">
                  <div className="carousel-main">
                    {carouselImages.length > 0 ? (
                      <img
                        src={carouselImages[currentImageIndex]}
                        alt={news.title}
                        className="main-image"
                      />
                    ) : (
                      <img
                        src={news.main_image}
                        alt={news.title}
                        className="main-image"
                      />
                    )}

                    {/* Navigation Arrows - Sadece birden fazla görsel varsa göster */}
                    {carouselImages.length > 1 && (
                      <>
                        <button
                          className="carousel-arrow carousel-arrow-left"
                          onClick={prevImage}
                          aria-label="Önceki görsel"
                        >
                          <i className="fas fa-chevron-left"></i>
                        </button>
                        <button
                          className="carousel-arrow carousel-arrow-right"
                          onClick={nextImage}
                          aria-label="Sonraki görsel"
                        >
                          <i className="fas fa-chevron-right"></i>
                        </button>
                      </>
                    )}

                    {/* Image Caption - Gizlendi */}
                    {/* {images[currentImageIndex].description && (
                      <div className="image-caption">
                        {images[currentImageIndex].description}
                      </div>
                    )} */}
                  </div>

                  {/* Thumbnail Navigation - Sadece birden fazla görsel varsa göster */}
                  {carouselImages.length > 1 && (
                    <div className="carousel-thumbnails">
                      {carouselImages.map((image, index) => (
                        <button
                          key={index}
                          className={`thumbnail ${index === currentImageIndex ? 'active' : ''}`}
                          onClick={() => selectImage(index)}
                        >
                          <img
                            src={image}
                            alt={`Görsel ${index + 1}`}
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* News Content */}
            <div className="news-content">
              <h2 className="content-title">Haber İçeriği</h2>
              <div className="content-text">
                {contentElements.map((element, index) => {
                  if (element.type === 'paragraph') {
                    return (
                      <p
                        key={`paragraph-${index}`}
                        className="content-paragraph"
                        style={{ fontSize: `${fontSize}px` }}
                      >
                        {element.content}
                      </p>
                    );
                  } else if (element.type === 'image') {
                    return (
                      <div key={`image-${index}`} className="content-image">
                        <img
                          src={element.content}
                          alt={`Haber görseli ${element.index + 1}`}
                          className="inline-image"
                          loading="lazy"
                        />
                      </div>
                    );
                  }
                  return null;
                })}
              </div>

              {/* IHA Disclaimer */}
              <div className="news-disclaimer">
                <p className="disclaimer-text">
                  "Bu içerik IHA Haber Ajansı tarafından hazırlanmıştır. İçerikte yer alan görüş ve ifadeler, editöryal politikamızı yansıtmayabilir"
                </p>
              </div>
            </div>



            {/* News Videos */}
            {videos.length > 0 && (
              <div className="news-videos">
                <h3 className="section-title">İlgili Videolar</h3>
                {videos.map(video => {
                  // HTML entities'leri decode et
                  const videoUrl = video.video_url?.replace(/&amp;/g, '&');
                  const posterUrl = video.poster_url?.replace(/&amp;/g, '&');

                  console.log('Video data:', {
                    video_kodu: video.video_kodu,
                    videoUrl,
                    posterUrl,
                    description: video.description,
                    hasVideoUrl: !!videoUrl,
                    hasPosterUrl: !!posterUrl
                  });

                  // Video URL yoksa bu videoyu atla
                  if (!videoUrl) {
                    console.warn('Video URL bulunamadı, video atlanıyor');
                    return null;
                  }

                  return (
                    <div key={video.video_kodu || video.video_url || Math.random()} className="video-container">
                      <video
                        controls
                        poster={posterUrl}
                        className="video-player"
                        preload="metadata"
                        crossOrigin="anonymous"
                        onError={(e) => {
                          console.error('Video yükleme hatası:', e.target.error);
                          console.log('Video URL:', videoUrl);
                          console.log('Poster URL:', posterUrl);
                        }}
                        onLoadStart={() => {
                          console.log('Video yüklenmeye başladı:', videoUrl);
                        }}
                        onCanPlay={() => {
                          console.log('Video oynatılabilir:', videoUrl);
                        }}
                        onLoadedMetadata={() => {
                          console.log('Video metadata yüklendi:', videoUrl);
                        }}
                      >
                        <source src={videoUrl} type="video/mp4" />
                        <source src={videoUrl} type="video/webm" />
                        <source src={videoUrl} type="video/ogg" />
                        Tarayıcınız video etiketini desteklemiyor.
                      </video>
                      {video.description && (
                        <div className="video-caption">
                          {video.description}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </article>

          {/* News Footer */}
          <footer className="news-footer">
            <div className="news-tags">
              <div className="tags-container">
                <h4 className="tags-title">Etiketler</h4>
                <div className="tags-list">
                  <Link to={`/kategori/${encodeURIComponent(news.kategori)}`} className="tag-item">
                    {news.kategori}
                  </Link>
                  <Link to={`/arama?q=${encodeURIComponent(news.sehir)}`} className="tag-item">
                    {news.sehir}
                  </Link>
                </div>
              </div>
            </div>

            <div className="news-share">
              <div className="share-container">
                <h4 className="share-title">Paylaş</h4>
                <div className="share-buttons">
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="share-button facebook"
                    aria-label="Facebook'ta paylaş"
                    title="Facebook'ta paylaş"
                  >
                    <i className="fab fa-facebook-f"></i>
                  </a>

                  <a
                    href={`https://x.com/intent/tweet?text=${encodeURIComponent(news.title)}&url=${encodeURIComponent(window.location.href)}&hashtags=haber,gündem`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="share-button twitter"
                    aria-label="X'te paylaş"
                    title="X'te paylaş"
                  >
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </a>

                  <a
                    href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(news.title)}&summary=${encodeURIComponent(stripHtmlTags(news.description).substring(0, 200))}&source=MetaAnaliz%20Haber`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="share-button linkedin"
                    aria-label="LinkedIn'de paylaş"
                    title="LinkedIn'de paylaş"
                  >
                    <i className="fab fa-linkedin-in"></i>
                  </a>

                  <a
                    href={`https://wa.me/?text=${encodeURIComponent(`${news.title}\n\n${stripHtmlTags(news.description).substring(0, 100)}...\n\nDetaylar: ${window.location.href}`)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="share-button whatsapp"
                    aria-label="WhatsApp'ta paylaş"
                    title="WhatsApp'ta paylaş"
                  >
                    <i className="fab fa-whatsapp"></i>
                  </a>

                  <a
                    href={`https://telegram.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(news.title)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="share-button telegram"
                    aria-label="Telegram'da paylaş"
                    title="Telegram'da paylaş"
                  >
                    <i className="fab fa-telegram-plane"></i>
                  </a>

                  <button
                    onClick={async (event) => {
                      try {
                        if (navigator.share) {
                          // Native Web Share API (mobil cihazlarda)
                          await navigator.share({
                            title: news.title,
                            text: stripHtmlTags(news.description).substring(0, 100) + '...',
                            url: window.location.href
                          });
                        } else if (navigator.clipboard) {
                          // Clipboard API
                          await navigator.clipboard.writeText(window.location.href);
                          // Başarı mesajı göster
                          const button = event.target.closest('button');
                          const originalIcon = button.innerHTML;
                          button.innerHTML = '<i class="fas fa-check"></i>';
                          button.style.background = '#28a745';
                          button.style.color = '#fff';

                          setTimeout(() => {
                            button.innerHTML = originalIcon;
                            button.style.background = '';
                            button.style.color = '';
                          }, 2000);
                        } else {
                          // Fallback for older browsers
                          const textArea = document.createElement('textarea');
                          textArea.value = window.location.href;
                          document.body.appendChild(textArea);
                          textArea.select();
                          document.execCommand('copy');
                          document.body.removeChild(textArea);

                          // Başarı mesajı göster
                          const button = event.target.closest('button');
                          const originalIcon = button.innerHTML;
                          button.innerHTML = '<i class="fas fa-check"></i>';
                          button.style.background = '#28a745';
                          button.style.color = '#fff';

                          setTimeout(() => {
                            button.innerHTML = originalIcon;
                            button.style.background = '';
                            button.style.color = '';
                          }, 2000);
                        }
                      } catch (error) {
                        console.error('Paylaşım hatası:', error);
                      }
                    }}
                    className="share-button copy"
                    aria-label="Linki kopyala veya paylaş"
                    title="Linki kopyala"
                  >
                    <i className="fas fa-copy"></i>
                  </button>
              </div>
            </div>
          </div>
          </footer>

          {/* Related News */}
          <RelatedNews category={news.kategori} excludeHaberKodu={news.haber_kodu} />
        </div>
      </div>
    </>
  );
};

export default NewsDetailPage;
