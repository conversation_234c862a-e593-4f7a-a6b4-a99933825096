import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

// Components
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';

// Styles
import '../assets/css/cron-monitor.css';

const CronMonitorPage = () => {
  const [cronStatus, setCronStatus] = useState(null);
  const [rssStats, setRssStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Backend API base URL
  const API_BASE = 'http://localhost/metaanalizhaber_yeni/backend';

  // Fetch cron status
  const fetchCronStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/cron_monitor.php?action=status`);
      const data = await response.json();
      
      if (data.success) {
        setCronStatus(data.data);
        setLastUpdate(new Date());
      } else {
        throw new Error(data.error || 'Veri alınamadı');
      }
    } catch (err) {
      setError('Cron durumu alınamadı: ' + err.message);
    }
  };

  // Fetch RSS stats
  const fetchRssStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/cron_monitor.php?action=rss_stats`);
      const data = await response.json();
      
      if (data.success) {
        setRssStats(data.data);
      } else {
        throw new Error(data.error || 'RSS istatistikleri alınamadı');
      }
    } catch (err) {
      console.error('RSS stats error:', err);
    }
  };

  // Initial load
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      
      await Promise.all([
        fetchCronStatus(),
        fetchRssStats()
      ]);
      
      setLoading(false);
    };

    loadData();
  }, []);

  // Auto refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchCronStatus();
      fetchRssStats();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusInfo = (status) => {
      switch (status) {
        case 'running':
          return { class: 'success', text: 'Çalışıyor', icon: 'fa-check-circle' };
        case 'delayed':
          return { class: 'warning', text: 'Gecikmeli', icon: 'fa-exclamation-triangle' };
        case 'stopped':
          return { class: 'danger', text: 'Durdu', icon: 'fa-times-circle' };
        default:
          return { class: 'secondary', text: 'Bilinmiyor', icon: 'fa-question-circle' };
      }
    };

    const info = getStatusInfo(status);
    return (
      <span className={`badge badge-${info.class}`}>
        <i className={`fas ${info.icon} me-1`}></i>
        {info.text}
      </span>
    );
  };

  // Health score component
  const HealthScore = ({ score }) => {
    const getHealthClass = (score) => {
      if (score >= 80) return 'success';
      if (score >= 60) return 'warning';
      return 'danger';
    };

    return (
      <div className={`health-score health-${getHealthClass(score)}`}>
        <div className="health-circle">
          <span className="health-number">{score}</span>
        </div>
        <small>Sağlık Skoru</small>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container py-5">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-3">Cron durumu yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-5">
        <ErrorMessage 
          message={error} 
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Cron Monitoring - MetaAnaliz Haber</title>
        <meta name="description" content="Cron job'ların durumu ve sistem istatistikleri" />
      </Helmet>

      {/* Page Header */}
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">
            <i className="fas fa-cogs me-3"></i>
            Cron Monitoring
          </h1>
          <nav aria-label="breadcrumb">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <Link to="/">Ana Sayfa</Link>
              </li>
              <li className="breadcrumb-item active" aria-current="page">
                Cron Monitoring
              </li>
            </ol>
          </nav>
          {lastUpdate && (
            <p className="text-muted mb-0">
              <i className="fas fa-sync-alt me-1"></i>
              Son güncelleme: {lastUpdate.toLocaleTimeString('tr-TR')}
            </p>
          )}
        </div>
      </div>

      <div className="container">
        {/* Overall Health */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-body text-center">
                <h5 className="card-title">Genel Sistem Durumu</h5>
                <div className={`overall-health health-${cronStatus?.overall_health || 'unknown'}`}>
                  <i className={`fas ${
                    cronStatus?.overall_health === 'good' ? 'fa-check-circle' :
                    cronStatus?.overall_health === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-times-circle'
                  } fa-3x mb-2`}></i>
                  <h4>
                    {cronStatus?.overall_health === 'good' ? 'İyi' :
                     cronStatus?.overall_health === 'warning' ? 'Uyarı' : 'Kritik'}
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cron Jobs Status */}
        <div className="row">
          {/* News Fetch */}
          {cronStatus?.news_fetch && (
            <div className="col-lg-4 mb-4">
              <div className="card h-100">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h6 className="mb-0">
                    <i className="fas fa-newspaper me-2"></i>
                    {cronStatus.news_fetch.name}
                  </h6>
                  <StatusBadge status={cronStatus.news_fetch.status} />
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-8">
                      <div className="stats-grid">
                        <div className="stat-item">
                          <span className="stat-number">{cronStatus.news_fetch.news_count_24h}</span>
                          <span className="stat-label">Haber (24s)</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number">{cronStatus.news_fetch.image_count_24h}</span>
                          <span className="stat-label">Görsel (24s)</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number">{cronStatus.news_fetch.video_count_24h}</span>
                          <span className="stat-label">Video (24s)</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number text-danger">{cronStatus.news_fetch.error_count_24h}</span>
                          <span className="stat-label">Hata (24s)</span>
                        </div>
                      </div>
                    </div>
                    <div className="col-4 text-center">
                      <HealthScore score={cronStatus.news_fetch.health_score} />
                    </div>
                  </div>
                  {cronStatus.news_fetch.last_run && (
                    <div className="mt-3">
                      <small className="text-muted">
                        <i className="fas fa-clock me-1"></i>
                        Son çalışma: {cronStatus.news_fetch.last_run.ago}
                      </small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Media Download */}
          {cronStatus?.media_download && (
            <div className="col-lg-4 mb-4">
              <div className="card h-100">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h6 className="mb-0">
                    <i className="fas fa-download me-2"></i>
                    {cronStatus.media_download.name}
                  </h6>
                  <StatusBadge status={cronStatus.media_download.status} />
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-8">
                      <div className="stats-grid">
                        <div className="stat-item">
                          <span className="stat-number text-warning">{cronStatus.media_download.pending_images}</span>
                          <span className="stat-label">Bekleyen Görsel</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number text-warning">{cronStatus.media_download.pending_videos}</span>
                          <span className="stat-label">Bekleyen Video</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number text-danger">{cronStatus.media_download.failed_images}</span>
                          <span className="stat-label">Başarısız Görsel</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-number text-danger">{cronStatus.media_download.failed_videos}</span>
                          <span className="stat-label">Başarısız Video</span>
                        </div>
                      </div>
                    </div>
                    <div className="col-4 text-center">
                      <HealthScore score={cronStatus.media_download.health_score} />
                    </div>
                  </div>
                  {cronStatus.media_download.last_run && (
                    <div className="mt-3">
                      <small className="text-muted">
                        <i className="fas fa-clock me-1"></i>
                        Son çalışma: {cronStatus.media_download.last_run.ago}
                      </small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* System Cleanup */}
          {cronStatus?.system_cleanup && (
            <div className="col-lg-4 mb-4">
              <div className="card h-100">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h6 className="mb-0">
                    <i className="fas fa-broom me-2"></i>
                    {cronStatus.system_cleanup.name}
                  </h6>
                  <StatusBadge status={cronStatus.system_cleanup.status} />
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-8">
                      {cronStatus.system_cleanup.disk_usage && (
                        <div className="mb-3">
                          <div className="d-flex justify-content-between">
                            <span>Disk Kullanımı</span>
                            <span>{cronStatus.system_cleanup.disk_usage.usage_percent}%</span>
                          </div>
                          <div className="progress">
                            <div 
                              className={`progress-bar ${
                                cronStatus.system_cleanup.disk_usage.usage_percent > 80 ? 'bg-danger' :
                                cronStatus.system_cleanup.disk_usage.usage_percent > 60 ? 'bg-warning' : 'bg-success'
                              }`}
                              style={{width: `${cronStatus.system_cleanup.disk_usage.usage_percent}%`}}
                            ></div>
                          </div>
                          <small className="text-muted">
                            {cronStatus.system_cleanup.disk_usage.used} / {cronStatus.system_cleanup.disk_usage.total}
                          </small>
                        </div>
                      )}
                      <div className="stat-item">
                        <span className="stat-number text-danger">{cronStatus.system_cleanup.error_count_7d}</span>
                        <span className="stat-label">Hata (7 gün)</span>
                      </div>
                    </div>
                    <div className="col-4 text-center">
                      <HealthScore score={cronStatus.system_cleanup.health_score} />
                    </div>
                  </div>
                  {cronStatus.system_cleanup.last_run && (
                    <div className="mt-3">
                      <small className="text-muted">
                        <i className="fas fa-clock me-1"></i>
                        Son çalışma: {cronStatus.system_cleanup.last_run.ago}
                      </small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* RSS Statistics */}
        {rssStats && (
          <div className="row mt-4">
            <div className="col-12">
              <div className="card">
                <div className="card-header">
                  <h6 className="mb-0">
                    <i className="fas fa-rss me-2"></i>
                    RSS İstatistikleri (Son 24 Saat)
                  </h6>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <span className="stat-number">{rssStats.total_requests_24h}</span>
                        <span className="stat-label">Toplam İstek</span>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <span className="stat-number text-success">{rssStats.successful_requests_24h}</span>
                        <span className="stat-label">Başarılı</span>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <span className="stat-number text-danger">{rssStats.failed_requests_24h}</span>
                        <span className="stat-label">Başarısız</span>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <span className="stat-number">{rssStats.success_rate}%</span>
                        <span className="stat-label">Başarı Oranı</span>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <span className="stat-number">{rssStats.avg_response_time}s</span>
                        <span className="stat-label">Ort. Yanıt Süresi</span>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="stat-item text-center">
                        <button 
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => {
                            fetchCronStatus();
                            fetchRssStats();
                          }}
                        >
                          <i className="fas fa-sync-alt"></i>
                          Yenile
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CronMonitorPage;
