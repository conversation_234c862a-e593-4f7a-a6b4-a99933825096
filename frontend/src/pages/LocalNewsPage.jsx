import React, { useState, useEffect } from "react";
import { useQuery } from "react-query";
import { useParams, Link } from "react-router-dom";
import { newsAPI } from "../services/api";
import { Helmet } from "react-helmet-async";
import { LazyLoadImage } from "react-lazy-load-image-component";
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';
import NewsCard from '../components/News/NewsCard';
import NewsGrid from '../components/News/NewsGrid';
import '../assets/css/local-news.css';
import '../assets/css/category.css';

const LocalNewsPage = () => {
    const { cityName } = useParams();
    const [selectedCity, setSelectedCity] = useState(cityName || '');
    const [availableCities, setAvailableCities] = useState([]);
    const [cityNews, setCityNews] = useState({});
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 20;

    // Ana şehirler (5 tane) - Aydın en üstte
    const mainCities = ['Aydın', 'İstanbul', 'Ankara', 'Bursa', 'İzmir'];

    // URL parametresi değiştiğinde selectedCity'yi güncelle
    useEffect(() => {
        if (cityName) {
            // URL'den gelen şehir adını decode et
            const decodedCityName = decodeURIComponent(cityName);
            console.log('URL\'den gelen şehir:', decodedCityName);
            setSelectedCity(decodedCityName);
        } else {
            // URL'de şehir parametresi yoksa selectedCity'yi temizle
            setSelectedCity('');
        }
    }, [cityName]);

    // Şehir listesi için ayrı API çağrısı
    const {
        data: citiesData,
        isLoading: citiesLoading
    } = useQuery(
        'cities-list',
        () => newsAPI.getCities(),
        {
            staleTime: 600000, // 10 dakika
            onSuccess: (data) => {
                if (data && data.success && Array.isArray(data.data)) {
                    setAvailableCities(data.data);
                }
            }
        }
    );

    // Ana sayfa için ana şehir haberlerini çek
    const {
        data: newsData,
        isLoading: loading,
        error,
        refetch
    } = useQuery(
        'local-news',
        () => newsAPI.getNews({ limit: 2000, sort: 'pub_date', order: 'DESC' }), // Daha fazla haber çek
        {
            staleTime: 300000, // 5 dakika
            onSuccess: (data) => {
                if (data && data.success && Array.isArray(data.data)) {
                    processNewsData(data.data);
                }
            }
        }
    );

    // Seçili şehir için ayrı API çağrısı
    const {
        data: selectedCityNewsData,
        isLoading: selectedCityLoading,
        error: selectedCityError
    } = useQuery(
        ['selected-city-news', selectedCity],
        () => {
            console.log('API çağrısı yapılıyor, şehir:', selectedCity);
            return newsAPI.getNews({
                sehir: selectedCity,
                limit: 1000, // Çok fazla haber çek ki sayfalandırma yapabilelim
                sort: 'pub_date',
                order: 'DESC'
            });
        },
        {
            enabled: !!selectedCity, // Sadece şehir seçildiğinde çalışsın
            staleTime: 300000,
            onSuccess: (data) => {
                console.log('API yanıtı:', data);
                console.log('Bulunan haber sayısı:', data?.data?.length || 0);
            }
        }
    );

    // Türkçe karakter normalize fonksiyonu
    const normalizeText = (text) => {
        return text.toLowerCase()
            .replace(/ı/g, 'i')
            .replace(/ğ/g, 'g')
            .replace(/ü/g, 'u')
            .replace(/ş/g, 's')
            .replace(/ö/g, 'o')
            .replace(/ç/g, 'c')
            .trim();
    };

    const processNewsData = (news) => {
        // Şehir bilgisi olan haberleri filtrele
        const newsWithCities = news.filter(item =>
            item.sehir && item.sehir.trim() !== ''
        );

        // Ana şehirler için haberleri grupla
        const groupedNews = {};
        mainCities.forEach(city => {
            const normalizedTargetCity = normalizeText(city);

            const cityNewsItems = newsWithCities
                .filter(item => {
                    if (!item.sehir) return false;
                    const normalizedItemCity = normalizeText(item.sehir);

                    // Normalize edilmiş şehir adları ile eşleştirme
                    return normalizedItemCity === normalizedTargetCity ||
                           normalizedItemCity.includes(normalizedTargetCity) ||
                           normalizedTargetCity.includes(normalizedItemCity);
                })
                .sort((a, b) => new Date(b.pub_date || b.created_at) - new Date(a.pub_date || a.created_at)) // En güncel üstte
                .slice(0, 8); // Her şehir için son 8 haber

            if (cityNewsItems.length > 0) {
                groupedNews[city] = cityNewsItems;
            }
        });
        setCityNews(groupedNews);
    };

    const handleCityChange = (event) => {
        setSelectedCity(event.target.value);
        setCurrentPage(1); // Şehir değiştiğinde sayfa sıfırla
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleBackToLocalNews = () => {
        setSelectedCity('');
        setCurrentPage(1);
    };



    const getFilteredNews = () => {
        if (!selectedCity) return [];

        // Seçili şehir için ayrı API çağrısından gelen verileri kullan
        if (selectedCityNewsData?.success && Array.isArray(selectedCityNewsData.data)) {
            const normalizedSelectedCity = normalizeText(selectedCity);

            return selectedCityNewsData.data
                .filter(item => {
                    if (!item.sehir) return false;
                    const normalizedItemCity = normalizeText(item.sehir);

                    // Normalize edilmiş şehir adları ile eşleştirme
                    return normalizedItemCity === normalizedSelectedCity ||
                           normalizedItemCity.includes(normalizedSelectedCity) ||
                           normalizedSelectedCity.includes(normalizedItemCity);
                })
                .sort((a, b) => new Date(b.pub_date || b.created_at) - new Date(a.pub_date || a.created_at));
        }

        return [];
    };

    // Sayfalandırma için haberleri böl
    const getPaginatedNews = () => {
        const filteredNews = getFilteredNews();
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredNews.slice(startIndex, endIndex);
    };

    // Toplam sayfa sayısı
    const getTotalPages = () => {
        const filteredNews = getFilteredNews();
        return Math.ceil(filteredNews.length / itemsPerPage);
    };



    if (loading) {
        return (
            <div className="local-news-container">
                <div className="container">
                    <div className="loading-state">
                        <LoadingSpinner />
                        <p>Yerel haberler yükleniyor...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="local-news-container">
                <div className="container">
                    <ErrorMessage message="Yerel haberler yüklenirken bir hata oluştu." />
                </div>
            </div>
        );
    }

    return (
        <>
            <Helmet>
                <title>Yerel Haberler - MetaAnaliz Haber</title>
                <meta name="description" content="Türkiye'nin tüm şehirlerinden güncel yerel haberler. İl il, bölge bölge haberleri takip edin." />
                <meta name="keywords" content="yerel haberler, şehir haberleri, il haberleri, bölgesel haberler, Türkiye haberleri" />
            </Helmet>

            <div className="local-news-page">
                {/* Page Header - Full Width */}
                <div className="page-header">
                    <div className="container">
                        <div className="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 className="page-title">
                                    <span className="category-name">Yerel</span>
                                    <span className="category-subtitle"> Haberler</span>
                                </h1>
                                <div className="category-breadcrumb">
                                    <nav aria-label="breadcrumb">
                                        <ol className="breadcrumb">
                                            <li className="breadcrumb-item">
                                                <Link to="/">Ana Sayfa</Link>
                                            </li>
                                            <li className="breadcrumb-item">
                                                <Link to="/yerel-haberler" onClick={handleBackToLocalNews}>Yerel Haberler</Link>
                                            </li>
                                            {selectedCity && (
                                                <li className="breadcrumb-item active">{selectedCity}</li>
                                            )}
                                            {!selectedCity && (
                                                <li className="breadcrumb-item active">Yerel Haberler</li>
                                            )}
                                        </ol>
                                    </nav>
                                </div>
                            </div>

                        {/* Şehir Seçici - Başlık kartının sağ tarafında */}
                        <div className="city-filter-header">
                            <label htmlFor="city-select" className="filter-label me-2">Şehir Seçin:</label>
                            <select
                                id="city-select"
                                className="form-select city-select"
                                value={selectedCity}
                                onChange={handleCityChange}
                                style={{ width: '200px' }}
                            >
                                <option value="">Şehir seçiniz</option>
                                {availableCities.map(city => (
                                    <option key={city} value={city}>
                                        {city}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>
            </div>

                <div className="container">

                    {/* Ana Şehirler Bölümleri */}
                    {!selectedCity && Object.keys(cityNews).length > 0 && (
                        <div className="main-cities-sections">
                            {Object.entries(cityNews).map(([city, newsItems]) => (
                                <div key={city} className="city-section">
                                    <div className="section-header">
                                        <h2 className="section-title">{city} Haberleri</h2>
                                        <Link to={`/yerel-haberler/${encodeURIComponent(city)}`} className="section-link">
                                            Tümünü Gör
                                        </Link>
                                    </div>
                                    <div className="news-grid row">
                                        {newsItems.map((newsItem) => (
                                            <div key={newsItem.haber_kodu} className="news-grid-item">
                                                <NewsCard
                                                    news={newsItem}
                                                    variant="default"
                                                    showCategory={true}
                                                    showDate={true}
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* Seçili Şehir Haberleri */}
                    {selectedCity && (
                        <div className="selected-city-news">
                            <div className="section-header">
                                <h2 className="section-title">{selectedCity} Haberleri</h2>
                                <span className="news-count">
                                    {getFilteredNews().length} haber - Sayfa {currentPage}/{getTotalPages()}
                                </span>
                            </div>

                            {/* Loading state */}
                            {selectedCityLoading ? (
                                <div className="text-center py-5">
                                    <LoadingSpinner text="Haberler yükleniyor..." />
                                </div>
                            ) : selectedCityError ? (
                                <div className="alert alert-danger text-center">
                                    <i className="fas fa-exclamation-triangle me-2"></i>
                                    Haberler yüklenirken bir hata oluştu.
                                </div>
                            ) : getPaginatedNews().length === 0 ? (
                                <div className="alert alert-info text-center">
                                    <i className="fas fa-info-circle me-2"></i>
                                    {selectedCity} için haber bulunamadı.
                                </div>
                            ) : (
                                <>
                                    {/* NewsGrid component'ini kullan - sayfalandırılmış haberler */}
                                    <NewsGrid
                                        news={getPaginatedNews()}
                                        showCategory={true}
                                        variant="local"
                                    />

                            {/* Sayfalandırma */}
                            {getTotalPages() > 1 && (
                                <div className="pagination-container">
                                    <nav aria-label="Sayfa navigasyonu">
                                        <ul className="pagination justify-content-center">
                                            {/* Önceki sayfa */}
                                            <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                                                <button
                                                    className="page-link"
                                                    onClick={() => handlePageChange(currentPage - 1)}
                                                    disabled={currentPage === 1}
                                                >
                                                    Önceki
                                                </button>
                                            </li>

                                            {/* Sayfa numaraları */}
                                            {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map(page => (
                                                <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                                                    <button
                                                        className="page-link"
                                                        onClick={() => handlePageChange(page)}
                                                    >
                                                        {page}
                                                    </button>
                                                </li>
                                            ))}

                                            {/* Sonraki sayfa */}
                                            <li className={`page-item ${currentPage === getTotalPages() ? 'disabled' : ''}`}>
                                                <button
                                                    className="page-link"
                                                    onClick={() => handlePageChange(currentPage + 1)}
                                                    disabled={currentPage === getTotalPages()}
                                                >
                                                    Sonraki
                                                </button>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            )}
                                </>
                            )}
                        </div>
                    )}

                    {/* Hiç haber yoksa - sadece loading tamamlandıktan sonra göster */}
                    {!selectedCity && !loading && Object.keys(cityNews).length === 0 && (
                        <div className="no-news">
                            <p>Henüz yerel haber bulunmuyor.</p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default LocalNewsPage;
