import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

// Components
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import ScrollToTop from './components/ScrollToTop';

// Pages
import HomePage from './pages/NewHomePage';
import CategoryPage from './pages/CategoryPage';
import VideoPage from './pages/VideoPage';
import LatestNewsPage from './pages/LatestNewsPage';
import CronMonitorPage from './pages/CronMonitorPage';
import NewsDetailPage from './pages/NewsDetailPage';
import SearchPage from './pages/SearchPage';
import LocalNewsPage from './pages/LocalNewsPage';
import VideoNewsPage from './pages/VideoNewsPage';
import WorldNewsPage from './pages/WorldNewsPage';
import NotFoundPage from './pages/NotFoundPage';

// Context
import { NewsProvider } from './context/NewsContext';

function App() {
  return (
    <NewsProvider>
      <div className="App">
        <Helmet>
          <title>MetaAnaliz Haber - Güncel Haberler</title>
          <meta name="description" content="Türkiye'den ve dünyadan en güncel haberler" />
        </Helmet>
        
        <Header />
        
        <main className="main-content">
          <ScrollToTop />
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/kategori/:categoryName" element={<CategoryPage />} />
            <Route path="/kategori/video" element={<VideoPage />} />
            <Route path="/son-haberler" element={<LatestNewsPage />} />
            <Route path="/cron-monitor" element={<CronMonitorPage />} />
            <Route path="/yerel-haberler" element={<LocalNewsPage />} />
            <Route path="/yerel-haberler/:cityName" element={<LocalNewsPage />} />
            <Route path="/video-haberler" element={<VideoNewsPage />} />
            <Route path="/dunya-haberleri" element={<WorldNewsPage />} />
            <Route path="/haber/:newsSlug" element={<NewsDetailPage />} />
            <Route path="/arama" element={<SearchPage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </main>
        
        <Footer />
      </div>
    </NewsProvider>
  );
}

export default App;
