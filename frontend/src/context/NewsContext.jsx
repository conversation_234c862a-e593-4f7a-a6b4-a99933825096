import React, { createContext, useState, useContext, useMemo, useCallback } from 'react';
import { useQuery } from 'react-query';
import { newsAPI } from '../services/api';

// Create context
const NewsContext = createContext();

// Provider component
export const NewsProvider = ({ children }) => {
  const [categories, setCategories] = useState([]);
  const [currentCategory, setCurrentCategory] = useState('');
  
  // Fetch categories
  const { data: categoriesData, isLoading: categoriesLoading } = useQuery(
    'categories',
    newsAPI.getCategories,
    {
      staleTime: 600000, // 10 minutes
      onSuccess: (data) => {
        if (data && data.success && data.data) {
          setCategories(data.data);
        }
      },
    }
  );
  
  // Memoized format date function
  const formatDate = useCallback((dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  // Memoized get relative time function
  const getRelativeTime = useCallback((dateString) => {
    if (!dateString) return '';

    const now = new Date();

    // Veritabanından gelen tarih formatı: '2025-07-28 10:39:43'
    // Bu tarih Türkiye saati olduğu için doğru şekilde parse ediyoruz
    let date;
    if (dateString.includes(' ')) {
      // Format: 'YYYY-MM-DD HH:mm:ss' - Türkiye saati
      date = new Date(dateString.replace(' ', 'T'));
    } else {
      // Diğer formatlar için standart parse
      date = new Date(dateString);
    }

    // Geçersiz tarih kontrolü
    if (isNaN(date.getTime())) {
      return dateString; // Orijinal string'i döndür
    }

    const diffInSeconds = Math.floor((now - date) / 1000);

    // Negatif fark kontrolü (gelecekteki tarihler)
    if (diffInSeconds < 0) {
      return 'Az önce';
    }

    if (diffInSeconds < 60) {
      return 'Az önce';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)} dakika önce`;
    } else if (diffInSeconds < 86400) { // 24 saat
      return `${Math.floor(diffInSeconds / 3600)} saat önce`;
    } else if (diffInSeconds < 604800) { // 7 gün
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} gün önce`;
    } else {
      // 7 günden fazla ise tam tarih göster
      return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }, []);

  // Memoized truncate text function
  const truncateText = useCallback((text, maxLength) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
  }, []);
  
  // Memoized context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    categories,
    categoriesLoading,
    currentCategory,
    setCurrentCategory,
    formatDate,
    getRelativeTime,
    truncateText
  }), [categories, categoriesLoading, currentCategory, formatDate, getRelativeTime, truncateText]);
  
  return <NewsContext.Provider value={value}>{children}</NewsContext.Provider>;
};

// Custom hook to use the context
export const useNews = () => {
  const context = useContext(NewsContext);
  if (context === undefined) {
    throw new Error('useNews must be used within a NewsProvider');
  }
  return context;
};

export default NewsContext;
