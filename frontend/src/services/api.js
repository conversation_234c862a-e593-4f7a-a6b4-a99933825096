import axios from 'axios';

// API base URL - her zaman production kullan
const API_BASE_URL = 'https://metaanalizhaber.com';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Production API kullan
const USE_MOCK_API = false;

// Request interceptor
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// No mock data - use real API

// API endpoints
export const newsAPI = {
  // Get news list
  getNews: async (params = {}) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';

      // Varsayılan sıralama parametreleri
      const defaultParams = {
        sort: 'pub_date',
        order: 'DESC',
        ...params
      };

      const response = await api.get(apiPath, {
        params: {
          action: 'get_news',
          ...defaultParams
        }
      });

      return response;
    } catch (error) {
      console.error('Error fetching news:', error);

      // API hatası durumunda boş veri döndür
      return {
        success: false,
        data: [],
        total: 0,
        error: error.message
      };
    }
  },

  // Get news detail by slug
  getNewsBySlug: async (slug) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_news_by_slug',  // ✅ Doğru endpoint
          slug: slug                   // ✅ Slug parametresi
        }
      });

      return response;
    } catch (error) {
      console.error('Error fetching news by slug:', error);
      throw error;
    }
  },

  // Get news detail by slug or haber kodu
  getNewsDetail: async (slugOrKodu) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';

      // Eğer slug haber kodu formatındaysa (20250722AW501707 gibi) direkt kullan
      if (/^\d{8}[A-Z]{2}\d{6}$/.test(slugOrKodu)) {
        const response = await api.get(apiPath, {
          params: {
            action: 'get_news_detail',
            haber_kodu: slugOrKodu
          }
        });
        return response;
      }

      // Eğer slug title-based ise, backend'deki slug endpoint'ini kullan
      const response = await api.get(apiPath, {
        params: {
          action: 'get_news_by_slug',
          slug: slugOrKodu
        }
      });
      return response;
    } catch (error) {
      console.error('API Error in getNewsDetail:', error);
      throw error;
    }
  },

  // Get categories
  getCategories: async () => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_categories'
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  // Get cities
  getCities: async () => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_cities'
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching cities:', error);
      throw error;
    }
  },

  // Get world news
  getWorldNews: async (params = {}) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_world_news',
          ...params
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching world news:', error);
      throw error;
    }
  },

  // Get video news
  getVideoNews: async (params = {}) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_video_news',
          ...params
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching video news:', error);
      throw error;
    }
  },

  // Search news
  searchNews: async (query, params = {}) => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'search_news',
          q: query,
          ...params
        }
      });
      return response;
    } catch (error) {
      console.error('Error searching news:', error);
      throw error;
    }
  },

  // Get currency rates
  getCurrencyRates: async () => {
    try {
      const apiPath = USE_MOCK_API ? '/backend/mock_api.php' : '/backend/api.php';
      const response = await api.get(apiPath, {
        params: {
          action: 'get_currency_rates'
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching currency rates:', error);
      throw error;
    }
  }
};

export default api;
