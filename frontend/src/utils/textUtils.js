// HTML tag'lerini temizleme fonksiyonu
export const stripHtmlTags = (html) => {
  if (!html) return '';
  
  // HTML tag'lerini kaldır
  const withoutTags = html.replace(/<[^>]*>/g, '');
  
  // HTML entity'lerini decode et
  const textarea = document.createElement('textarea');
  textarea.innerHTML = withoutTags;
  const decoded = textarea.value;
  
  // Fazla boşlukları temizle
  return decoded.replace(/\s+/g, ' ').trim();
};

// Haber özeti için HTML temizleme (excerpt için)
export const cleanExcerpt = (text, maxLength = 200) => {
  if (!text) return '';
  
  const cleaned = stripHtmlTags(text);
  
  if (cleaned.length <= maxLength) {
    return cleaned;
  }
  
  // Kelime sınırında kes
  const truncated = cleaned.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
};

// Haber içeriği için HTML'i temizle ama paragrafları koru
export const cleanNewsContent = (html) => {
  if (!html) return '';

  let cleaned = html;

  // Önce HTML entity'lerini decode et (escape edilmiş HTML'i çöz)
  const textarea = document.createElement('textarea');
  textarea.innerHTML = cleaned;
  cleaned = textarea.value;

  // Şimdi <p> tag'lerini paragraf ayırıcısı olarak işle
  cleaned = cleaned.replace(/<\/p>/gi, '\n\n');
  cleaned = cleaned.replace(/<p[^>]*>/gi, '');

  // <br> tag'lerini yeni satır olarak işle
  cleaned = cleaned.replace(/<br\s*\/?>/gi, '\n');

  // Diğer tüm HTML tag'lerini kaldır
  cleaned = cleaned.replace(/<[^>]*>/g, '');

  // Fazla boşlukları temizle
  cleaned = cleaned.replace(/[ \t]+/g, ' ');

  // Fazla yeni satırları temizle (3+ yeni satırı 2'ye indir)
  cleaned = cleaned.replace(/\n\s*\n\s*\n+/g, '\n\n');

  // Satır başı ve sonundaki boşlukları temizle
  cleaned = cleaned.replace(/\n\s+/g, '\n');
  cleaned = cleaned.replace(/\s+\n/g, '\n');

  return cleaned.trim();
};

// Metni belirtilen uzunlukta kes
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';

  if (text.length <= maxLength) {
    return text;
  }

  // Kelime sınırında kes
  const truncated = text.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');

  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }

  return truncated + '...';
};

// HTML entity'lerini decode et (başlıklar için)
export const decodeHtmlEntities = (text) => {
  if (!text) return '';

  // Textarea kullanarak HTML entity'lerini decode et
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};
