// URL slug oluşturma fonksiyonu
export const createSlug = (text) => {
  if (!text) return '';
  
  // Türkçe karakterleri İngilizce karşılıklarına çevir
  const turkishMap = {
    'ç': 'c', 'Ç': 'c',
    'ğ': 'g', 'Ğ': 'g', 
    'ı': 'i', 'I': 'i', 'İ': 'i',
    'ö': 'o', 'Ö': 'o',
    'ş': 's', 'Ş': 's',
    'ü': 'u', 'Ü': 'u'
  };
  
  return text
    .split('')
    .map(char => turkishMap[char] || char)
    .join('')
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Özel karakterleri kaldır
    .replace(/\s+/g, '-') // Boşlukları tire ile değiştir
    .replace(/-+/g, '-') // Çoklu tireleri tek tire yap
    .trim();
};

// Slug'dan orijinal kategori adını bulma fonksiyonu
export const getOriginalCategoryName = (slug) => {
  const categoryMap = {
    'asayis': 'ASAYIŞ',
    'genel': 'GENEL',
    'haberde-insan': 'HABERDE İNSAN',
    'cevre': 'ÇEVRE',
    'politika': 'POLİTİKA',
    'spor': 'SPOR',
    'kultur-sanat': 'KÜLTÜR SANAT',
    'egitim': 'EĞİTİM',
    'ekonomi': 'EKONOMİ',
    'saglik': 'SAĞLIK',
    'bilim-ve-teknoloji': 'BİLİM VE TEKNOLOJİ',
    'magazin': 'MAGAZİN'
  };

  return categoryMap[slug] || slug;
};

// Veritabanından gelen kategori adlarını düzeltme fonksiyonu
export const fixCategoryName = (categoryName) => {
  if (!categoryName) return '';

  const categoryFixes = {
    'SAGLIK': 'SAĞLIK',
    'ASAYIS': 'ASAYIŞ',
    'CEVRE': 'ÇEVRE',
    'POLITIKA': 'POLİTİKA',
    'EGITIM': 'EĞİTİM',
    'EKONOMI': 'EKONOMİ',
    'KULTUR SANAT': 'KÜLTÜR SANAT',
    'BILIM VE TEKNOLOJI': 'BİLİM VE TEKNOLOJİ',
    'MAGAZIN': 'MAGAZİN'
  };

  return categoryFixes[categoryName] || categoryName;
};

// Haber başlığından SEO uyumlu slug oluşturma (API ile uyumlu)
export const createNewsSlug = (title, haberKodu) => {
  if (!title) return 'haber';

  // HTML entity'lerini decode et
  const textarea = document.createElement('textarea');
  textarea.innerHTML = title;
  const decodedTitle = textarea.value;

  // Backend ile aynı algoritma kullan
  let slug = decodedTitle
    .toLowerCase()
    .replace(/[çÇ]/g, 'c')
    .replace(/[ğĞ]/g, 'g')
    .replace(/[ıIİ]/g, 'i')
    .replace(/[öÖ]/g, 'o')
    .replace(/[şŞ]/g, 's')
    .replace(/[üÜ]/g, 'u')
    .replace(/[^a-z0-9\s-]/g, '') // Özel karakterleri kaldır
    .replace(/\s+/g, '-') // Boşlukları tire ile değiştir
    .replace(/-+/g, '-') // Çoklu tireleri tek tire yap
    .replace(/^-+|-+$/g, ''); // Başındaki ve sonundaki tireleri kaldır

  // Slug her zaman title'dan oluşturulur, haber kodu kullanılmaz
  return slug || 'haber';
};

// Slug'dan haber başlığını temizle (URL'de kullanım için)
export const cleanSlugForTitle = (slug) => {
  if (!slug) return '';

  // Eğer slug haber kodu formatındaysa (20250722AW501707 gibi) direkt döndür
  if (/^\d{8}[A-Z]{2}\d{6}$/.test(slug)) {
    return slug;
  }

  return slug;
};
