<?php
/**
 * Meta Tag Debug Sayfası
 * Bu sayfa meta tag'lerin doğru inject edilip edilmediğini kontrol eder
 */

// Debug için hata raporlamayı aç
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test için örnek haber slug'ı
$testSlug = $_GET['slug'] ?? 'test-haberi';

echo "<h1>🔍 Meta Tag Debug - Slug: " . htmlspecialchars($testSlug) . "</h1>";

// Haber verilerini çek
function getNewsDataBySlug($slug) {
    try {
        // Backend API'ye istek at - HTTPS kullan
        $apiUrl = "https://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);
        
        echo "<h3>📡 API İsteği:</h3>";
        echo "<code>" . htmlspecialchars($apiUrl) . "</code><br><br>";

        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            echo "<div style='color: red;'>❌ API isteği başarısız!</div>";
            return null;
        }

        echo "<h3>📥 API Yanıtı:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data']['news'])) {
            echo "<div style='color: green;'>✅ API yanıtı başarılı!</div>";
            return $data['data'];
        } else {
            echo "<div style='color: red;'>❌ API yanıtı geçersiz!</div>";
            return null;
        }

    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Hata: " . htmlspecialchars($e->getMessage()) . "</div>";
        return null;
    }
}

// Meta tag'leri inject eden fonksiyon (basitleştirilmiş)
function injectNewsMetaTags($html, $newsData) {
    $news = $newsData['news'] ?? $newsData;
    $images = $newsData['images'] ?? [];

    $title = htmlspecialchars($news['title'] ?? 'MetaAnaliz Haber');
    $rawDescription = htmlspecialchars(strip_tags($news['description'] ?? ''));
    $facebookDescription = substr($rawDescription, 0, 300) . (strlen($rawDescription) > 300 ? '...' : '');
    $twitterDescription = substr($rawDescription, 0, 160) . (strlen($rawDescription) > 160 ? '...' : '');
    $url = "https://metaanalizhaber.com/haber/" . ($news['slug'] ?? '');

    // Haber görseli
    $image = 'https://metaanalizhaber.com/logo7.webp';
    if (!empty($images) && is_array($images)) {
        $firstImage = $images[0];
        if (isset($firstImage['url']) && !empty($firstImage['url'])) {
            $image = $firstImage['url'];
        }
    }

    echo "<h3>🏷️ Meta Tag Bilgileri:</h3>";
    echo "<ul>";
    echo "<li><strong>Başlık:</strong> " . htmlspecialchars($title) . "</li>";
    echo "<li><strong>Facebook Açıklama:</strong> " . htmlspecialchars($facebookDescription) . "</li>";
    echo "<li><strong>Twitter Açıklama:</strong> " . htmlspecialchars($twitterDescription) . "</li>";
    echo "<li><strong>URL:</strong> " . htmlspecialchars($url) . "</li>";
    echo "<li><strong>Görsel:</strong> " . htmlspecialchars($image) . "</li>";
    echo "</ul>";

    // Meta tag'leri değiştir
    $html = preg_replace('/<title>.*?<\/title>/', "<title>{$title} - MetaAnaliz Haber</title>", $html);
    $html = preg_replace('/<meta name="description" content=".*?">/', '<meta name="description" content="' . $twitterDescription . '">', $html);
    $html = preg_replace('/<meta property="og:title" content=".*?">/', '<meta property="og:title" content="' . $title . '">', $html);
    $html = preg_replace('/<meta property="og:description" content=".*?">/', '<meta property="og:description" content="' . $facebookDescription . '">', $html);
    $html = preg_replace('/<meta property="og:url" content=".*?">/', '<meta property="og:url" content="' . $url . '">', $html);
    $html = preg_replace('/<meta property="og:image" content=".*?">/', '<meta property="og:image" content="' . $image . '">', $html);
    $html = preg_replace('/<meta name="twitter:title" content=".*?">/', '<meta name="twitter:title" content="' . $title . '">', $html);
    $html = preg_replace('/<meta name="twitter:description" content=".*?">/', '<meta name="twitter:description" content="' . $twitterDescription . '">', $html);
    $html = preg_replace('/<meta name="twitter:image" content=".*?">/', '<meta name="twitter:image" content="' . $image . '">', $html);

    return $html;
}

// Test et
$newsData = getNewsDataBySlug($testSlug);

if ($newsData) {
    // index.html'i oku
    $frontendIndex = __DIR__ . '/frontend/dist/index.html';
    if (!file_exists($frontendIndex)) {
        $frontendIndex = __DIR__ . '/frontend/index.html';
    }

    echo "<h3>📄 Kullanılan Index Dosyası:</h3>";
    echo "<code>" . htmlspecialchars($frontendIndex) . "</code><br><br>";

    if (file_exists($frontendIndex)) {
        $html = file_get_contents($frontendIndex);
        $modifiedHtml = injectNewsMetaTags($html, $newsData);

        echo "<h3>🔧 İşlenmiş Meta Tag'ler:</h3>";
        
        // Meta tag'leri çıkar ve göster
        preg_match_all('/<meta[^>]+>/', $modifiedHtml, $metaTags);
        preg_match('/<title>.*?<\/title>/', $modifiedHtml, $titleTag);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
        
        if (!empty($titleTag[0])) {
            echo htmlspecialchars($titleTag[0]) . "<br>";
        }
        
        foreach ($metaTags[0] as $tag) {
            if (strpos($tag, 'og:') !== false || strpos($tag, 'twitter:') !== false || strpos($tag, 'description') !== false) {
                echo htmlspecialchars($tag) . "<br>";
            }
        }
        echo "</div>";

        echo "<h3>🌐 Test Linkleri:</h3>";
        $testUrl = "https://metaanalizhaber.com/haber/" . urlencode($testSlug);
        echo "<ul>";
        echo "<li><a href='https://developers.facebook.com/tools/debug/?q=" . urlencode($testUrl) . "' target='_blank'>📘 Facebook Debugger</a></li>";
        echo "<li><a href='https://cards-dev.twitter.com/validator' target='_blank'>🐦 Twitter Card Validator</a></li>";
        echo "<li><a href='https://www.linkedin.com/post-inspector/inspect/" . urlencode($testUrl) . "' target='_blank'>💼 LinkedIn Post Inspector</a></li>";
        echo "<li><a href='https://www.opengraph.xyz/?url=" . urlencode($testUrl) . "' target='_blank'>🌐 Open Graph Preview</a></li>";
        echo "</ul>";

    } else {
        echo "<div style='color: red;'>❌ index.html dosyası bulunamadı!</div>";
    }
} else {
    echo "<div style='color: red;'>❌ Haber verisi alınamadı!</div>";
}

echo "<hr>";
echo "<h3>🧪 Farklı Slug'lar ile Test:</h3>";
echo "<ul>";
echo "<li><a href='?slug=test-haberi'>Test Haberi</a></li>";
echo "<li><a href='?slug=guncel-haber'>Güncel Haber</a></li>";
echo "<li><a href='?slug=spor-haberi'>Spor Haberi</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
h1, h2, h3 { color: #333; }
code { background: #f5f5f5; padding: 2px 5px; border-radius: 3px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
ul { line-height: 1.6; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
